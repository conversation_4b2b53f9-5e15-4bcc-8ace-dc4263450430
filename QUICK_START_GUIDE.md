# دليل البدء السريع - مصمم الأثاث الاحترافي المتقدم (الإصدار 2.1)
## Quick Start Guide - Advanced Professional Furniture Designer (Version 2.1)

### 🆕 الجديد في هذا الإصدار
- **PySide6**: تم الانتقال من PyQt6 إلى PySide6 لتحسين الأداء
- **العملة الليبية**: دعم شامل للدينار الليبي (د.ل) في جميع أجزاء التطبيق
- **تحسينات الواجهة**: واجهة مستخدم محسنة وأكثر استجابة

### 🚀 البدء السريع

#### الطريقة الأولى: التشغيل الآمن (موصى به)
```bash
# تشغيل المشغل الآمن (يفحص ويثبت المتطلبات تلقائياً)
python run_safe.py
```

#### الطريقة الثانية: التثبيت التدريجي
```bash
# 1. تثبيت المكتبات تدريجياً مع معالجة الأخطاء
python install_requirements.py

# 2. تشغيل التطبيق
python run_advanced_furniture_designer.py
```

#### الطريقة الثالثة: التثبيت اليدوي
```bash
# 1. تثبيت المكتبات الأساسية فقط
pip install -r requirements_core.txt

# 2. أو تثبيت جميع المكتبات (قد يفشل بعضها)
pip install -r requirements.txt

# 3. تشغيل التطبيق
python run_advanced_furniture_designer.py
```

#### اختبار سريع
```bash
# للتحقق من حالة التثبيت
python run_migration_test.py
```

### 🎯 الميزات الرئيسية

#### 🔮 المعاين ثلاثي الأبعاد المحسن
- **الوصول**: تبويب "المعاينة ثلاثية الأبعاد"
- **الميزات الجديدة**:
  - إضاءة وظلال واقعية
  - مواد وخامات متنوعة (خشب، معدن، بلاستيك، زجاج، قماش، جلد)
  - دوران تلقائي وتحكم متقدم في الكاميرا
  - أدوات قياس وتحليل

**كيفية الاستخدام**:
1. اضغط على "تحميل نموذج" لتحميل ملف ثلاثي الأبعاد
2. استخدم تبويب "إعدادات العرض" لتخصيص الكاميرا
3. جرب تبويب "الإضاءة والمواد" لتغيير المظهر
4. استخدم "أداة القياس" لقياس الأبعاد

#### 👥 نظام إدارة العملاء المتقدم
- **الوصول**: تبويب "إدارة العملاء المتقدمة"
- **الميزات الجديدة**:
  - بحث وفلترة متقدمة
  - تتبع تاريخ التفاعلات
  - إدارة المشاريع والمهام
  - إحصائيات تفصيلية

**كيفية الاستخدام**:
1. اضغط على "إضافة عميل" لإضافة عميل جديد
2. استخدم شريط البحث للعثور على العملاء
3. اختر عميل لعرض تفاصيله ومشاريعه
4. اضغط على "تصدير" لحفظ قائمة العملاء

#### 📦 نظام تتبع المخزون الذكي
- **الوصول**: تبويب "إدارة المخزون المتقدمة"
- **الميزات الجديدة**:
  - تنبيهات المخزون المنخفض
  - تتبع حركة المواد
  - إدارة الموردين
  - تقارير المخزون التفصيلية

**كيفية الاستخدام**:
1. اضغط على "إضافة عنصر" لإضافة مادة جديدة
2. راقب التنبيهات للمواد منخفضة المخزون
3. سجل حركات الدخول والخروج
4. راجع تقارير المخزون الدورية

#### 🔧 تكامل ماكينات CNC المحسن
- **الوصول**: تبويب "إدارة CNC المتقدمة"
- **الميزات الجديدة**:
  - معاينة مسار القطع
  - محلل G-Code متقدم
  - مراقبة الأداء في الوقت الفعلي
  - نظام أمان محسن

**كيفية الاستخدام**:
1. اضغط على "إضافة ماكينة" لتسجيل ماكينة جديدة
2. اضغط على "تحميل G-Code" لتحميل ملف القطع
3. راقب حالة الماكينة والتقدم
4. استخدم "إيقاف طارئ" عند الحاجة

#### 📊 نظام التقارير المتطور
- **الوصول**: تبويب "التقارير المتقدمة"
- **الميزات الجديدة**:
  - تقارير مالية مفصلة
  - دعم PDF مع العربية
  - رسوم بيانية تفاعلية
  - تصدير متعدد الصيغ

**كيفية الاستخدام**:
1. اختر نوع التقرير المطلوب
2. حدد الفترة الزمنية والفلاتر
3. اضغط على "إنشاء التقرير"
4. اختر صيغة التصدير (PDF، Excel، CSV)

### 🛠️ نصائح للاستخدام الأمثل

#### الأداء
- **تحميل النماذج**: استخدم ملفات صغيرة للاختبار أولاً
- **قواعد البيانات**: قم بعمل نسخ احتياطية دورية
- **الذاكرة**: أغلق التبويبات غير المستخدمة

#### الأمان
- **النسخ الاحتياطي**: فعل النسخ الاحتياطي التلقائي
- **كلمات المرور**: استخدم كلمات مرور قوية للبيانات الحساسة
- **التحديثات**: حافظ على تحديث التطبيق

#### التخصيص
- **الثيمات**: اختر الثيم المناسب من الإعدادات
- **اللغة**: يمكن التبديل بين العربية والإنجليزية
- **الاختصارات**: تعلم اختصارات لوحة المفاتيح

### 🔧 حل المشاكل الشائعة

#### مشكلة: "sqlite3 not found" أو خطأ في المتطلبات
**الحل**:
```bash
# استخدم المثبت التدريجي
python install_requirements.py

# أو استخدم المتطلبات الأساسية فقط
pip install -r requirements_core.txt
```

#### مشكلة: "المكتبات مفقودة" أو فشل التثبيت
**الحل**:
```bash
# 1. تحديث pip أولاً
python -m pip install --upgrade pip

# 2. استخدام المشغل الآمن
python run_safe.py

# 3. أو التثبيت التدريجي
python install_requirements.py
```

#### مشكلة: "PySide6 لا يعمل" أو خطأ في الواجهة
**الحل**:
```bash
# تثبيت PySide6 منفصلاً
pip install PySide6>=6.4.0

# اختبار التثبيت
python -c "from PySide6.QtWidgets import QApplication; print('PySide6 يعمل!')"
```

#### مشكلة: "المعاين ثلاثي الأبعاد لا يعمل"
**الحل**:
```bash
pip install matplotlib trimesh numpy
```

#### مشكلة: "العملة لا تظهر بشكل صحيح"
**الحل**:
```bash
# اختبار منسق العملة
python -c "from ui.modern_styles import CurrencyFormatter; print(CurrencyFormatter.format_currency(1234.56))"
```

#### مشكلة: "لا يمكن الاتصال بماكينة CNC"
**الحل**:
1. تثبيت pyserial: `pip install pyserial`
2. تحقق من كابل USB
3. تأكد من رقم المنفذ الصحيح
4. تحقق من سرعة البود

#### مشكلة: "التقارير لا تُصدر بشكل صحيح"
**الحل**:
```bash
# للتقارير الأساسية
pip install openpyxl pandas

# للتقارير المتقدمة مع PDF
pip install reportlab arabic-reshaper python-bidi
```

#### مشكلة عامة: التطبيق لا يعمل
**الحل التدريجي**:
```bash
# 1. اختبار سريع
python run_migration_test.py

# 2. إذا فشل، استخدم المشغل الآمن
python run_safe.py

# 3. إذا استمر الفشل، تثبيت يدوي
pip install PySide6 numpy pandas matplotlib trimesh openpyxl

# 4. تشغيل التطبيق
python run_advanced_furniture_designer.py
```

### 📞 الدعم والمساعدة

#### الوثائق
- **دليل المستخدم الكامل**: `README_ADVANCED_FEATURES.md`
- **وثائق API**: في مجلد `docs/`
- **أمثلة**: في مجلد `examples/`

#### الاختبار
- **اختبار سريع**: `python quick_test.py`
- **اختبار شامل**: `python test_advanced_features.py`
- **اختبار الأداء**: `python performance_test.py`

#### المجتمع
- **المنتدى**: [رابط المنتدى]
- **GitHub**: [رابط المستودع]
- **البريد الإلكتروني**: <EMAIL>

### 🎓 دروس تعليمية سريعة

#### الدرس 1: إنشاء مشروع أثاث جديد
1. افتح التطبيق
2. اذهب إلى تبويب "التصميم"
3. اضغط على "مشروع جديد"
4. أدخل تفاصيل المشروع
5. احفظ المشروع

#### الدرس 2: تحليل نموذج ثلاثي الأبعاد
1. اذهب إلى تبويب "المعاينة ثلاثية الأبعاد"
2. اضغط على "تحميل نموذج"
3. اختر ملف .obj أو .stl
4. استخدم أدوات التحليل
5. احفظ النتائج

#### الدرس 3: إدارة مخزون المواد
1. اذهب إلى تبويب "إدارة المخزون المتقدمة"
2. اضغط على "إضافة عنصر"
3. أدخل تفاصيل المادة
4. حدد الحد الأدنى للتنبيه
5. راقب التنبيهات

#### الدرس 4: إنشاء تقرير مالي
1. اذهب إلى تبويب "التقارير المتقدمة"
2. اختر "تقرير الإيرادات"
3. حدد الفترة الزمنية
4. اضغط على "إنشاء التقرير"
5. صدر إلى PDF

### 🎯 الخطوات التالية

بعد إتقان الأساسيات، يمكنك:
1. **تخصيص التطبيق** حسب احتياجاتك
2. **إنشاء قوالب مخصصة** للمشاريع
3. **تطوير إضافات** جديدة
4. **المشاركة في المجتمع** وتبادل الخبرات

---

**مرحباً بك في عالم تصميم الأثاث الاحترافي! 🪑✨**
