#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حاسبة التكلفة المتقدمة لتطبيق تصميم الأثاث
Advanced Cost Calculator for Furniture Design Application
"""

def get_safe_icon(icon_name):
    """الحصول على أيقونة آمنة"""
    try:
        from ui.icons_manager import IconsManager
        return IconsManager.get_standard_icon(icon_name)
    except Exception:
        # إرجاع أيقونة فارغة في حالة الخطأ
        from PySide6.QtGui import QIcon
        return QIcon()


import os
import json
import math
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QLineEdit, QTextEdit, QComboBox, QDateEdit, QSpinBox,
    QTable<PERSON>idget, Q<PERSON>able<PERSON>idgetI<PERSON>, Q<PERSON>eader<PERSON>iew, QMessageBox,
    QDialog, QFormLayout, QDialogButtonBox, QGroupBox,
    QCheckBox, QDoubleSpinBox, QTabWidget, QSplitter,
    QTreeWidget, QTreeWidgetItem, QProgressBar, QFrame,
    QScrollArea, QGridLayout, QFileDialog
)
from PySide6.QtCore import Qt, QDate, QThread, Signal, QTimer
from PySide6.QtGui import QFont, QColor, QPixmap, QPainter

from ui.modern_styles import ModernStyleManager
from ui.icons_manager import IconsManager


@dataclass
class MaterialCost:
    """تكلفة المواد"""
    name: str
    quantity: float
    unit: str
    unit_cost: float
    total_cost: float
    waste_percentage: float = 10.0  # نسبة الهدر

    def calculate_total_with_waste(self) -> float:
        """حساب التكلفة الإجمالية مع الهدر"""
        base_cost = self.quantity * self.unit_cost
        waste_cost = base_cost * (self.waste_percentage / 100)
        return base_cost + waste_cost


@dataclass
class LaborCost:
    """تكلفة العمالة"""
    task_name: str
    hours: float
    hourly_rate: float
    total_cost: float
    difficulty_multiplier: float = 1.0  # معامل الصعوبة

    def calculate_total_with_difficulty(self) -> float:
        """حساب التكلفة مع معامل الصعوبة"""
        return self.hours * self.hourly_rate * self.difficulty_multiplier


@dataclass
class OverheadCost:
    """التكاليف العامة"""
    category: str
    description: str
    cost: float
    percentage_of_materials: float = 0.0  # نسبة من تكلفة المواد


@dataclass
class ProjectCalculation:
    """حساب المشروع"""
    project_name: str
    materials: List[MaterialCost]
    labor: List[LaborCost]
    overheads: List[OverheadCost]
    profit_margin: float = 20.0  # هامش الربح بالنسبة المئوية
    tax_rate: float = 15.0  # ضريبة القيمة المضافة
    created_date: str = ""

    def __post_init__(self):
        if not self.created_date:
            self.created_date = datetime.now().isoformat()

    def calculate_materials_total(self) -> float:
        """حساب إجمالي تكلفة المواد"""
        return sum(material.calculate_total_with_waste() for material in self.materials)

    def calculate_labor_total(self) -> float:
        """حساب إجمالي تكلفة العمالة"""
        return sum(labor.calculate_total_with_difficulty() for labor in self.labor)

    def calculate_overheads_total(self, materials_total: float) -> float:
        """حساب إجمالي التكاليف العامة"""
        total = 0.0
        for overhead in self.overheads:
            if overhead.percentage_of_materials > 0:
                total += materials_total * (overhead.percentage_of_materials / 100)
            else:
                total += overhead.cost
        return total

    def calculate_subtotal(self) -> float:
        """حساب المجموع الفرعي"""
        materials_total = self.calculate_materials_total()
        labor_total = self.calculate_labor_total()
        overheads_total = self.calculate_overheads_total(materials_total)
        return materials_total + labor_total + overheads_total

    def calculate_profit(self, subtotal: float) -> float:
        """حساب الربح"""
        return subtotal * (self.profit_margin / 100)

    def calculate_tax(self, subtotal_with_profit: float) -> float:
        """حساب الضريبة"""
        return subtotal_with_profit * (self.tax_rate / 100)

    def calculate_total(self) -> Dict[str, float]:
        """حساب الإجمالي النهائي"""
        materials_total = self.calculate_materials_total()
        labor_total = self.calculate_labor_total()
        overheads_total = self.calculate_overheads_total(materials_total)
        subtotal = materials_total + labor_total + overheads_total
        profit = self.calculate_profit(subtotal)
        subtotal_with_profit = subtotal + profit
        tax = self.calculate_tax(subtotal_with_profit)
        total = subtotal_with_profit + tax

        return {
            'materials_total': materials_total,
            'labor_total': labor_total,
            'overheads_total': overheads_total,
            'subtotal': subtotal,
            'profit': profit,
            'subtotal_with_profit': subtotal_with_profit,
            'tax': tax,
            'total': total
        }


class MaterialDialog(QDialog):
    """نافذة إضافة/تعديل المواد"""

    def __init__(self, material: Optional[MaterialCost] = None, parent=None):
        super().__init__(parent)
        self.material = material
        self.is_edit_mode = material is not None
        self.init_ui()
        if self.material:
            self.load_material_data()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("تعديل المادة" if self.is_edit_mode else "إضافة مادة جديدة")
        self.setModal(True)
        self.resize(400, 300)

        layout = QVBoxLayout(self)

        # نموذج البيانات
        form_layout = QFormLayout()

        # اسم المادة
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("اسم المادة")
        form_layout.addRow("اسم المادة *:", self.name_edit)

        # الكمية
        self.quantity_spin = QDoubleSpinBox()
        self.quantity_spin.setRange(0.01, 999999)
        self.quantity_spin.setDecimals(2)
        self.quantity_spin.setValue(1.0)
        form_layout.addRow("الكمية:", self.quantity_spin)

        # الوحدة
        self.unit_combo = QComboBox()
        self.unit_combo.addItems([
            "متر", "متر مربع", "متر مكعب", "قطعة", "كيلو", "لتر", "علبة", "رزمة"
        ])
        form_layout.addRow("الوحدة:", self.unit_combo)

        # تكلفة الوحدة
        self.unit_cost_spin = QDoubleSpinBox()
        self.unit_cost_spin.setRange(0.01, 999999)
        self.unit_cost_spin.setDecimals(2)
        self.unit_cost_spin.setSuffix(" د.ل")
        self.unit_cost_spin.valueChanged.connect(self.calculate_total)
        form_layout.addRow("تكلفة الوحدة:", self.unit_cost_spin)

        # نسبة الهدر
        self.waste_spin = QDoubleSpinBox()
        self.waste_spin.setRange(0, 100)
        self.waste_spin.setDecimals(1)
        self.waste_spin.setSuffix(" %")
        self.waste_spin.setValue(10.0)
        self.waste_spin.valueChanged.connect(self.calculate_total)
        form_layout.addRow("نسبة الهدر:", self.waste_spin)

        # التكلفة الإجمالية
        self.total_label = QLabel("0.00 ريال")
        self.total_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                font-weight: bold;
                color: {ModernStyleManager.COLORS['primary']};
                padding: 8px;
                background-color: {ModernStyleManager.COLORS['card']};
                border-radius: 6px;
            }}
        """)
        form_layout.addRow("التكلفة الإجمالية:", self.total_label)

        layout.addLayout(form_layout)

        # أزرار التحكم
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)

        # تخصيص الأزرار
        ok_button = buttons.button(QDialogButtonBox.StandardButton.Ok)
        ok_button.setText("حفظ")

        cancel_button = buttons.button(QDialogButtonBox.StandardButton.Cancel)
        cancel_button.setText("إلغاء")

        layout.addWidget(buttons)

        # ربط الأحداث
        self.quantity_spin.valueChanged.connect(self.calculate_total)

        # حساب التكلفة الأولية
        self.calculate_total()

    def calculate_total(self):
        """حساب التكلفة الإجمالية"""
        quantity = self.quantity_spin.value()
        unit_cost = self.unit_cost_spin.value()
        waste_percentage = self.waste_spin.value()

        base_cost = quantity * unit_cost
        waste_cost = base_cost * (waste_percentage / 100)
        total = base_cost + waste_cost

        self.total_label.setText(f"{total:,.2f} د.ل")

    def load_material_data(self):
        """تحميل بيانات المادة للتعديل"""
        if not self.material:
            return

        self.name_edit.setText(self.material.name)
        self.quantity_spin.setValue(self.material.quantity)
        self.unit_cost_spin.setValue(self.material.unit_cost)
        self.waste_spin.setValue(self.material.waste_percentage)

        # تعيين الوحدة
        unit_index = self.unit_combo.findText(self.material.unit)
        if unit_index >= 0:
            self.unit_combo.setCurrentIndex(unit_index)

    def get_material_data(self) -> MaterialCost:
        """الحصول على بيانات المادة من النموذج"""
        quantity = self.quantity_spin.value()
        unit_cost = self.unit_cost_spin.value()
        waste_percentage = self.waste_spin.value()

        base_cost = quantity * unit_cost
        waste_cost = base_cost * (waste_percentage / 100)
        total_cost = base_cost + waste_cost

        return MaterialCost(
            name=self.name_edit.text().strip(),
            quantity=quantity,
            unit=self.unit_combo.currentText(),
            unit_cost=unit_cost,
            total_cost=total_cost,
            waste_percentage=waste_percentage
        )

    def validate_data(self) -> bool:
        """التحقق من صحة البيانات"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المادة")
            self.name_edit.setFocus()
            return False

        if self.quantity_spin.value() <= 0:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال كمية صحيحة")
            self.quantity_spin.setFocus()
            return False

        if self.unit_cost_spin.value() <= 0:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال تكلفة صحيحة")
            self.unit_cost_spin.setFocus()
            return False

        return True

    def accept(self):
        """قبول النموذج"""
        if self.validate_data():
            super().accept()


class LaborDialog(QDialog):
    """نافذة إضافة/تعديل العمالة"""

    def __init__(self, labor: Optional[LaborCost] = None, parent=None):
        super().__init__(parent)
        self.labor = labor
        self.is_edit_mode = labor is not None
        self.init_ui()
        if self.labor:
            self.load_labor_data()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("تعديل مهمة العمالة" if self.is_edit_mode else "إضافة مهمة عمالة جديدة")
        self.setModal(True)
        self.resize(400, 300)

        layout = QVBoxLayout(self)

        # نموذج البيانات
        form_layout = QFormLayout()

        # اسم المهمة
        self.task_name_edit = QLineEdit()
        self.task_name_edit.setPlaceholderText("اسم المهمة")
        form_layout.addRow("اسم المهمة *:", self.task_name_edit)

        # عدد الساعات
        self.hours_spin = QDoubleSpinBox()
        self.hours_spin.setRange(0.1, 999.9)
        self.hours_spin.setDecimals(1)
        self.hours_spin.setValue(1.0)
        self.hours_spin.setSuffix(" ساعة")
        self.hours_spin.valueChanged.connect(self.calculate_total)
        form_layout.addRow("عدد الساعات:", self.hours_spin)

        # أجر الساعة
        self.hourly_rate_spin = QDoubleSpinBox()
        self.hourly_rate_spin.setRange(1.0, 9999.99)
        self.hourly_rate_spin.setDecimals(2)
        self.hourly_rate_spin.setValue(50.0)
        self.hourly_rate_spin.setSuffix(" د.ل/ساعة")
        self.hourly_rate_spin.valueChanged.connect(self.calculate_total)
        form_layout.addRow("أجر الساعة:", self.hourly_rate_spin)

        # معامل الصعوبة
        self.difficulty_spin = QDoubleSpinBox()
        self.difficulty_spin.setRange(0.5, 5.0)
        self.difficulty_spin.setDecimals(1)
        self.difficulty_spin.setValue(1.0)
        self.difficulty_spin.setSingleStep(0.1)
        self.difficulty_spin.valueChanged.connect(self.calculate_total)
        form_layout.addRow("معامل الصعوبة:", self.difficulty_spin)

        # شرح معامل الصعوبة
        difficulty_info = QLabel("0.5 = سهل جداً | 1.0 = عادي | 2.0 = صعب | 3.0+ = معقد جداً")
        difficulty_info.setStyleSheet("color: #7f8c8d; font-size: 11px; font-style: italic;")
        form_layout.addRow("", difficulty_info)

        # التكلفة الإجمالية
        self.total_label = QLabel("0.00 ريال")
        self.total_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                font-weight: bold;
                color: {ModernStyleManager.COLORS['primary']};
                padding: 8px;
                background-color: {ModernStyleManager.COLORS['card']};
                border-radius: 6px;
            }}
        """)
        form_layout.addRow("التكلفة الإجمالية:", self.total_label)

        layout.addLayout(form_layout)

        # أزرار التحكم
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)

        # تخصيص الأزرار
        ok_button = buttons.button(QDialogButtonBox.StandardButton.Ok)
        ok_button.setText("حفظ")

        cancel_button = buttons.button(QDialogButtonBox.StandardButton.Cancel)
        cancel_button.setText("إلغاء")

        layout.addWidget(buttons)

        # حساب التكلفة الأولية
        self.calculate_total()

    def calculate_total(self):
        """حساب التكلفة الإجمالية"""
        hours = self.hours_spin.value()
        hourly_rate = self.hourly_rate_spin.value()
        difficulty_multiplier = self.difficulty_spin.value()

        total = hours * hourly_rate * difficulty_multiplier
        self.total_label.setText(f"{total:,.2f} د.ل")

    def load_labor_data(self):
        """تحميل بيانات العمالة للتعديل"""
        if self.labor:
            self.task_name_edit.setText(self.labor.task_name)
            self.hours_spin.setValue(self.labor.hours)
            self.hourly_rate_spin.setValue(self.labor.hourly_rate)
            self.difficulty_spin.setValue(self.labor.difficulty_multiplier)
            self.calculate_total()

    def accept(self):
        """التحقق من صحة البيانات قبل الحفظ"""
        if not self.task_name_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المهمة")
            self.task_name_edit.setFocus()
            return

        if self.hours_spin.value() <= 0:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال عدد ساعات صحيح")
            self.hours_spin.setFocus()
            return

        if self.hourly_rate_spin.value() <= 0:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال أجر ساعة صحيح")
            self.hourly_rate_spin.setFocus()
            return

        super().accept()

    def get_labor_data(self) -> LaborCost:
        """الحصول على بيانات العمالة من النموذج"""
        hours = self.hours_spin.value()
        hourly_rate = self.hourly_rate_spin.value()
        difficulty_multiplier = self.difficulty_spin.value()
        total_cost = hours * hourly_rate * difficulty_multiplier

        return LaborCost(
            task_name=self.task_name_edit.text().strip(),
            hours=hours,
            hourly_rate=hourly_rate,
            total_cost=total_cost,
            difficulty_multiplier=difficulty_multiplier
        )


class CostCalculatorWidget(QWidget):
    """واجهة حاسبة التكلفة الرئيسية"""

    def __init__(self):
        super().__init__()
        self.current_calculation = ProjectCalculation(
            project_name="مشروع جديد",
            materials=[],
            labor=[],
            overheads=[]
        )
        self.init_ui()
        self.update_calculations()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(ModernStyleManager.SPACING['md'])
        layout.setContentsMargins(
            ModernStyleManager.SPACING['lg'],
            ModernStyleManager.SPACING['lg'],
            ModernStyleManager.SPACING['lg'],
            ModernStyleManager.SPACING['lg']
        )

        # شريط الأدوات العلوي
        toolbar_layout = QHBoxLayout()

        # عنوان الصفحة
        title_label = QLabel("🧮 حاسبة التكلفة المتقدمة")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['title']}px;
                font-weight: bold;
                color: {ModernStyleManager.COLORS['primary']};
                margin-bottom: {ModernStyleManager.SPACING['md']}px;
            }}
        """)

        # اسم المشروع
        self.project_name_edit = QLineEdit()
        self.project_name_edit.setText(self.current_calculation.project_name)
        self.project_name_edit.setPlaceholderText("اسم المشروع")
        self.project_name_edit.textChanged.connect(self.update_project_name)
        self.project_name_edit.setProperty("inputSize", "large")

        # أزرار التحكم
        self.new_project_btn = QPushButton("مشروع جديد")
        self.new_project_btn.setIcon(get_safe_icon("add"))
        self.new_project_btn.clicked.connect(self.new_project)
        self.new_project_btn.setProperty("buttonSize", "medium")
        self.new_project_btn.setMinimumHeight(35)

        self.save_project_btn = QPushButton("حفظ")
        self.save_project_btn.setIcon(get_safe_icon("save"))
        self.save_project_btn.clicked.connect(self.save_project)
        self.save_project_btn.setProperty("buttonSize", "medium")
        self.save_project_btn.setMinimumHeight(35)

        self.load_project_btn = QPushButton("تحميل")
        self.load_project_btn.setIcon(get_safe_icon("open"))
        self.load_project_btn.clicked.connect(self.load_project)
        self.load_project_btn.setProperty("buttonSize", "medium")
        self.load_project_btn.setMinimumHeight(35)

        toolbar_layout.addWidget(title_label)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(QLabel("اسم المشروع:"))
        toolbar_layout.addWidget(self.project_name_edit)
        toolbar_layout.addSpacing(ModernStyleManager.SPACING['md'])
        toolbar_layout.addWidget(self.new_project_btn)
        toolbar_layout.addWidget(self.save_project_btn)
        toolbar_layout.addWidget(self.load_project_btn)

        layout.addLayout(toolbar_layout)

        # المقسم الرئيسي
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # المنطقة اليسرى - الإدخال
        self.create_input_area(main_splitter)

        # المنطقة اليمنى - النتائج
        self.create_results_area(main_splitter)

        # تعيين النسب
        main_splitter.setSizes([600, 400])

        layout.addWidget(main_splitter)

    def create_input_area(self, parent):
        """إنشاء منطقة الإدخال"""
        input_widget = QWidget()
        layout = QVBoxLayout(input_widget)

        # تبويبات الإدخال
        input_tabs = QTabWidget()

        # تبويب المواد
        materials_tab = self.create_materials_tab()
        input_tabs.addTab(materials_tab, "المواد")

        # تبويب العمالة
        labor_tab = self.create_labor_tab()
        input_tabs.addTab(labor_tab, "العمالة")

        # تبويب التكاليف العامة
        overheads_tab = self.create_overheads_tab()
        input_tabs.addTab(overheads_tab, "التكاليف العامة")

        # تبويب الإعدادات
        settings_tab = self.create_settings_tab()
        input_tabs.addTab(settings_tab, "الإعدادات")

        layout.addWidget(input_tabs)
        parent.addWidget(input_widget)

    def create_materials_tab(self):
        """إنشاء تبويب المواد"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # شريط أدوات المواد
        materials_toolbar = QHBoxLayout()

        add_material_btn = QPushButton("إضافة مادة")
        add_material_btn.setIcon(get_safe_icon("add"))
        add_material_btn.clicked.connect(self.add_material)
        add_material_btn.setMinimumHeight(35)
        add_material_btn.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 6px 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        edit_material_btn = QPushButton("تعديل")
        edit_material_btn.setIcon(get_safe_icon("edit"))
        edit_material_btn.clicked.connect(self.edit_material)
        edit_material_btn.setMinimumHeight(35)
        edit_material_btn.setStyleSheet("""
            QPushButton {
                background-color: #F39C12;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 6px 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #E67E22;
            }
        """)

        delete_material_btn = QPushButton("حذف")
        delete_material_btn.setIcon(get_safe_icon("delete"))
        delete_material_btn.clicked.connect(self.delete_material)
        delete_material_btn.setMinimumHeight(35)
        delete_material_btn.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 6px 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
        """)

        materials_toolbar.addWidget(add_material_btn)
        materials_toolbar.addWidget(edit_material_btn)
        materials_toolbar.addWidget(delete_material_btn)
        materials_toolbar.addStretch()

        layout.addLayout(materials_toolbar)

        # جدول المواد
        self.materials_table = QTableWidget()
        self.materials_table.setColumnCount(6)
        self.materials_table.setHorizontalHeaderLabels([
            "المادة", "الكمية", "الوحدة", "تكلفة الوحدة", "نسبة الهدر %", "الإجمالي"
        ])

        # تخصيص الجدول
        header = self.materials_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)  # المادة
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # الكمية
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # الوحدة
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # تكلفة الوحدة
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # نسبة الهدر
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # الإجمالي

        self.materials_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.materials_table.doubleClicked.connect(self.edit_material)

        layout.addWidget(self.materials_table)

        return tab

    def create_labor_tab(self):
        """إنشاء تبويب العمالة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # شريط أدوات العمالة
        labor_toolbar = QHBoxLayout()

        add_labor_btn = QPushButton("إضافة مهمة")
        add_labor_btn.setIcon(get_safe_icon("add"))
        add_labor_btn.clicked.connect(self.add_labor)
        add_labor_btn.setMinimumHeight(35)
        add_labor_btn.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 6px 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)

        edit_labor_btn = QPushButton("تعديل")
        edit_labor_btn.setIcon(get_safe_icon("edit"))
        edit_labor_btn.clicked.connect(self.edit_labor)
        edit_labor_btn.setMinimumHeight(35)
        edit_labor_btn.setStyleSheet("""
            QPushButton {
                background-color: #F39C12;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 6px 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #E67E22;
            }
        """)

        delete_labor_btn = QPushButton("حذف")
        delete_labor_btn.setIcon(get_safe_icon("delete"))
        delete_labor_btn.clicked.connect(self.delete_labor)
        delete_labor_btn.setMinimumHeight(35)
        delete_labor_btn.setStyleSheet("""
            QPushButton {
                background-color: #E74C3C;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 6px 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #C0392B;
            }
        """)

        labor_toolbar.addWidget(add_labor_btn)
        labor_toolbar.addWidget(edit_labor_btn)
        labor_toolbar.addWidget(delete_labor_btn)
        labor_toolbar.addStretch()

        layout.addLayout(labor_toolbar)

        # جدول العمالة
        self.labor_table = QTableWidget()
        self.labor_table.setColumnCount(5)
        self.labor_table.setHorizontalHeaderLabels([
            "المهمة", "الساعات", "أجر الساعة", "معامل الصعوبة", "الإجمالي"
        ])

        # تخصيص الجدول
        header = self.labor_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)  # المهمة
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # الساعات
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # أجر الساعة
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # معامل الصعوبة
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # الإجمالي

        self.labor_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.labor_table.doubleClicked.connect(self.edit_labor)

        layout.addWidget(self.labor_table)

        return tab

    def create_overheads_tab(self):
        """إنشاء تبويب التكاليف العامة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # التكاليف العامة الثابتة
        fixed_overheads_group = QGroupBox("التكاليف العامة الثابتة")
        fixed_layout = QFormLayout(fixed_overheads_group)

        self.rent_spin = QDoubleSpinBox()
        self.rent_spin.setRange(0, 999999)
        self.rent_spin.setSuffix(" ريال")
        self.rent_spin.valueChanged.connect(self.update_calculations)
        fixed_layout.addRow("إيجار المحل:", self.rent_spin)

        self.utilities_spin = QDoubleSpinBox()
        self.utilities_spin.setRange(0, 999999)
        self.utilities_spin.setSuffix(" ريال")
        self.utilities_spin.valueChanged.connect(self.update_calculations)
        fixed_layout.addRow("الكهرباء والماء:", self.utilities_spin)

        self.insurance_spin = QDoubleSpinBox()
        self.insurance_spin.setRange(0, 999999)
        self.insurance_spin.setSuffix(" ريال")
        self.insurance_spin.valueChanged.connect(self.update_calculations)
        fixed_layout.addRow("التأمين:", self.insurance_spin)

        layout.addWidget(fixed_overheads_group)

        # التكاليف العامة كنسبة
        percentage_overheads_group = QGroupBox("التكاليف العامة كنسبة من المواد")
        percentage_layout = QFormLayout(percentage_overheads_group)

        self.transport_percentage_spin = QDoubleSpinBox()
        self.transport_percentage_spin.setRange(0, 100)
        self.transport_percentage_spin.setSuffix(" %")
        self.transport_percentage_spin.setValue(5.0)
        self.transport_percentage_spin.valueChanged.connect(self.update_calculations)
        percentage_layout.addRow("النقل والشحن:", self.transport_percentage_spin)

        self.storage_percentage_spin = QDoubleSpinBox()
        self.storage_percentage_spin.setRange(0, 100)
        self.storage_percentage_spin.setSuffix(" %")
        self.storage_percentage_spin.setValue(3.0)
        self.storage_percentage_spin.valueChanged.connect(self.update_calculations)
        percentage_layout.addRow("التخزين:", self.storage_percentage_spin)

        layout.addWidget(percentage_overheads_group)
        layout.addStretch()

        return tab

    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        tab = QWidget()
        layout = QFormLayout(tab)

        # هامش الربح
        self.profit_margin_spin = QDoubleSpinBox()
        self.profit_margin_spin.setRange(0, 100)
        self.profit_margin_spin.setSuffix(" %")
        self.profit_margin_spin.setValue(self.current_calculation.profit_margin)
        self.profit_margin_spin.valueChanged.connect(self.update_profit_margin)
        layout.addRow("هامش الربح:", self.profit_margin_spin)

        # ضريبة القيمة المضافة
        self.tax_rate_spin = QDoubleSpinBox()
        self.tax_rate_spin.setRange(0, 100)
        self.tax_rate_spin.setSuffix(" %")
        self.tax_rate_spin.setValue(self.current_calculation.tax_rate)
        self.tax_rate_spin.valueChanged.connect(self.update_tax_rate)
        layout.addRow("ضريبة القيمة المضافة:", self.tax_rate_spin)

        return tab

    def create_results_area(self, parent):
        """إنشاء منطقة النتائج"""
        results_widget = QWidget()
        results_widget.setMaximumWidth(400)
        results_widget.setMinimumWidth(300)

        layout = QVBoxLayout(results_widget)

        # عنوان النتائج
        results_title = QLabel("💰 ملخص التكاليف")
        results_title.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                font-weight: bold;
                color: {ModernStyleManager.COLORS['primary']};
                padding: {ModernStyleManager.SPACING['md']}px;
                background-color: {ModernStyleManager.COLORS['card']};
                border-radius: {ModernStyleManager.COMPONENT_SIZES['border_radius']}px;
                margin-bottom: {ModernStyleManager.SPACING['md']}px;
            }}
        """)
        layout.addWidget(results_title)

        # بطاقات النتائج
        self.materials_total_label = self.create_result_card("تكلفة المواد", "0.00 ريال", "#3498DB")
        self.labor_total_label = self.create_result_card("تكلفة العمالة", "0.00 ريال", "#E67E22")
        self.overheads_total_label = self.create_result_card("التكاليف العامة", "0.00 ريال", "#9B59B6")
        self.subtotal_label = self.create_result_card("المجموع الفرعي", "0.00 ريال", "#34495E")
        self.profit_label = self.create_result_card("الربح", "0.00 ريال", "#27AE60")
        self.tax_label = self.create_result_card("الضريبة", "0.00 ريال", "#E74C3C")

        # الإجمالي النهائي
        self.total_label = QLabel("الإجمالي النهائي: 0.00 ريال")
        self.total_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['title']}px;
                font-weight: bold;
                color: {ModernStyleManager.COLORS['text_light']};
                background-color: {ModernStyleManager.COLORS['primary']};
                padding: {ModernStyleManager.SPACING['lg']}px;
                border-radius: {ModernStyleManager.COMPONENT_SIZES['card_radius']}px;
                text-align: center;
                margin: {ModernStyleManager.SPACING['md']}px 0;
            }}
        """)

        layout.addWidget(self.materials_total_label)
        layout.addWidget(self.labor_total_label)
        layout.addWidget(self.overheads_total_label)
        layout.addWidget(self.subtotal_label)
        layout.addWidget(self.profit_label)
        layout.addWidget(self.tax_label)
        layout.addWidget(self.total_label)

        # أزرار الإجراءات
        actions_layout = QVBoxLayout()

        export_btn = QPushButton("تصدير التقرير")
        export_btn.setIcon(get_safe_icon("export"))
        export_btn.clicked.connect(self.export_report)
        export_btn.setProperty("buttonSize", "medium")
        export_btn.setMinimumHeight(40)
        export_btn.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:pressed {
                background-color: #1E8449;
            }
        """)

        print_btn = QPushButton("طباعة")
        print_btn.setIcon(get_safe_icon("print"))
        print_btn.clicked.connect(self.print_report)
        print_btn.setProperty("buttonSize", "medium")
        print_btn.setMinimumHeight(40)
        print_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498DB;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
            QPushButton:pressed {
                background-color: #21618C;
            }
        """)

        actions_layout.addWidget(export_btn)
        actions_layout.addWidget(print_btn)

        layout.addLayout(actions_layout)
        layout.addStretch()

        parent.addWidget(results_widget)

    def create_result_card(self, title: str, value: str, color: str) -> QLabel:
        """إنشاء بطاقة نتيجة"""
        card = QLabel(f"{title}: {value}")
        card.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['body']}px;
                font-weight: bold;
                color: white;
                background-color: {color};
                padding: {ModernStyleManager.SPACING['md']}px;
                border-radius: {ModernStyleManager.COMPONENT_SIZES['border_radius']}px;
                margin-bottom: {ModernStyleManager.SPACING['sm']}px;
            }}
        """)
        return card

    def update_project_name(self):
        """تحديث اسم المشروع"""
        self.current_calculation.project_name = self.project_name_edit.text()

    def update_profit_margin(self):
        """تحديث هامش الربح"""
        self.current_calculation.profit_margin = self.profit_margin_spin.value()
        self.update_calculations()

    def update_tax_rate(self):
        """تحديث معدل الضريبة"""
        self.current_calculation.tax_rate = self.tax_rate_spin.value()
        self.update_calculations()

    def add_material(self):
        """إضافة مادة جديدة"""
        dialog = MaterialDialog(parent=self)

        if dialog.exec() == QDialog.DialogCode.Accepted:
            material = dialog.get_material_data()
            self.current_calculation.materials.append(material)
            self.update_materials_table()
            self.update_calculations()

    def edit_material(self):
        """تعديل المادة المحددة"""
        current_row = self.materials_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_calculation.materials):
            material = self.current_calculation.materials[current_row]
            dialog = MaterialDialog(material=material, parent=self)

            if dialog.exec() == QDialog.DialogCode.Accepted:
                updated_material = dialog.get_material_data()
                self.current_calculation.materials[current_row] = updated_material
                self.update_materials_table()
                self.update_calculations()

    def delete_material(self):
        """حذف المادة المحددة"""
        current_row = self.materials_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_calculation.materials):
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                "هل أنت متأكد من حذف هذه المادة؟",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                del self.current_calculation.materials[current_row]
                self.update_materials_table()
                self.update_calculations()

    def add_labor(self):
        """إضافة مهمة عمالة جديدة"""
        dialog = LaborDialog(parent=self)

        if dialog.exec() == QDialog.DialogCode.Accepted:
            labor = dialog.get_labor_data()
            self.current_calculation.labor.append(labor)
            self.update_labor_table()
            self.update_calculations()

    def edit_labor(self):
        """تعديل مهمة العمالة المحددة"""
        current_row = self.labor_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_calculation.labor):
            labor = self.current_calculation.labor[current_row]
            dialog = LaborDialog(labor=labor, parent=self)

            if dialog.exec() == QDialog.DialogCode.Accepted:
                updated_labor = dialog.get_labor_data()
                self.current_calculation.labor[current_row] = updated_labor
                self.update_labor_table()
                self.update_calculations()

    def delete_labor(self):
        """حذف مهمة العمالة المحددة"""
        current_row = self.labor_table.currentRow()
        if current_row >= 0 and current_row < len(self.current_calculation.labor):
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                "هل أنت متأكد من حذف هذه المهمة؟",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                del self.current_calculation.labor[current_row]
                self.update_labor_table()
                self.update_calculations()

    def update_materials_table(self):
        """تحديث جدول المواد"""
        self.materials_table.setRowCount(len(self.current_calculation.materials))

        for row, material in enumerate(self.current_calculation.materials):
            self.materials_table.setItem(row, 0, QTableWidgetItem(material.name))
            self.materials_table.setItem(row, 1, QTableWidgetItem(f"{material.quantity:.2f}"))
            self.materials_table.setItem(row, 2, QTableWidgetItem(material.unit))
            self.materials_table.setItem(row, 3, QTableWidgetItem(f"{material.unit_cost:.2f}"))
            self.materials_table.setItem(row, 4, QTableWidgetItem(f"{material.waste_percentage:.1f}"))
            self.materials_table.setItem(row, 5, QTableWidgetItem(f"{material.calculate_total_with_waste():.2f}"))

    def update_labor_table(self):
        """تحديث جدول العمالة"""
        self.labor_table.setRowCount(len(self.current_calculation.labor))

        for row, labor in enumerate(self.current_calculation.labor):
            self.labor_table.setItem(row, 0, QTableWidgetItem(labor.task_name))
            self.labor_table.setItem(row, 1, QTableWidgetItem(f"{labor.hours:.1f}"))
            self.labor_table.setItem(row, 2, QTableWidgetItem(f"{labor.hourly_rate:.2f}"))
            self.labor_table.setItem(row, 3, QTableWidgetItem(f"{labor.difficulty_multiplier:.1f}"))
            self.labor_table.setItem(row, 4, QTableWidgetItem(f"{labor.calculate_total_with_difficulty():.2f}"))

    def update_calculations(self):
        """تحديث جميع الحسابات"""
        # تحديث التكاليف العامة
        self.current_calculation.overheads = [
            OverheadCost("إيجار المحل", "إيجار شهري", self.rent_spin.value() if hasattr(self, 'rent_spin') else 0),
            OverheadCost("الكهرباء والماء", "فواتير شهرية", self.utilities_spin.value() if hasattr(self, 'utilities_spin') else 0),
            OverheadCost("التأمين", "تأمين شهري", self.insurance_spin.value() if hasattr(self, 'insurance_spin') else 0),
            OverheadCost("النقل والشحن", "نسبة من المواد", 0, self.transport_percentage_spin.value() if hasattr(self, 'transport_percentage_spin') else 0),
            OverheadCost("التخزين", "نسبة من المواد", 0, self.storage_percentage_spin.value() if hasattr(self, 'storage_percentage_spin') else 0),
        ]

        # حساب النتائج
        results = self.current_calculation.calculate_total()

        # تحديث بطاقات النتائج
        if hasattr(self, 'materials_total_label'):
            self.materials_total_label.setText(f"تكلفة المواد: {results['materials_total']:,.2f} د.ل")
            self.labor_total_label.setText(f"تكلفة العمالة: {results['labor_total']:,.2f} د.ل")
            self.overheads_total_label.setText(f"التكاليف العامة: {results['overheads_total']:,.2f} د.ل")
            self.subtotal_label.setText(f"المجموع الفرعي: {results['subtotal']:,.2f} د.ل")
            self.profit_label.setText(f"الربح: {results['profit']:,.2f} د.ل")
            self.tax_label.setText(f"الضريبة: {results['tax']:,.2f} د.ل")
            self.total_label.setText(f"الإجمالي النهائي: {results['total']:,.2f} د.ل")

    def new_project(self):
        """مشروع جديد"""
        reply = QMessageBox.question(
            self, "مشروع جديد",
            "هل أنت متأكد من إنشاء مشروع جديد؟ سيتم فقدان البيانات الحالية.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.current_calculation = ProjectCalculation(
                project_name="مشروع جديد",
                materials=[],
                labor=[],
                overheads=[]
            )
            self.project_name_edit.setText(self.current_calculation.project_name)
            self.update_materials_table()
            self.update_labor_table()
            self.update_calculations()

    def save_project(self):
        """حفظ المشروع"""
        try:
            file_dialog = QFileDialog()
            file_path, _ = file_dialog.getSaveFileName(
                self,
                "حفظ مشروع حاسبة التكلفة",
                f"{self.current_calculation.project_name}.json",
                "ملفات JSON (*.json);;جميع الملفات (*)"
            )

            if file_path:
                # تحضير البيانات للحفظ
                project_data = {
                    'project_name': self.current_calculation.project_name,
                    'materials': [asdict(material) for material in self.current_calculation.materials],
                    'labor': [asdict(labor) for labor in self.current_calculation.labor],
                    'overheads': [asdict(overhead) for overhead in self.current_calculation.overheads],
                    'profit_margin': self.current_calculation.profit_margin,
                    'tax_rate': self.current_calculation.tax_rate,
                    'created_date': self.current_calculation.created_date,
                    'saved_date': datetime.now().isoformat(),
                    'version': '3.0'
                }

                # حفظ الملف
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(project_data, f, ensure_ascii=False, indent=2)

                QMessageBox.information(self, "نجح الحفظ", f"تم حفظ المشروع بنجاح في:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ في الحفظ", f"فشل في حفظ المشروع:\n{str(e)}")

    def load_project(self):
        """تحميل مشروع"""
        try:
            file_dialog = QFileDialog()
            file_path, _ = file_dialog.getOpenFileName(
                self,
                "تحميل مشروع حاسبة التكلفة",
                "",
                "ملفات JSON (*.json);;جميع الملفات (*)"
            )

            if file_path:
                # قراءة الملف
                with open(file_path, 'r', encoding='utf-8') as f:
                    project_data = json.load(f)

                # التحقق من إصدار الملف
                if project_data.get('version', '1.0') != '3.0':
                    reply = QMessageBox.question(
                        self, "إصدار قديم",
                        "هذا الملف من إصدار قديم. هل تريد المتابعة؟",
                        QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                    )
                    if reply != QMessageBox.StandardButton.Yes:
                        return

                # تحميل البيانات
                self.current_calculation.project_name = project_data.get('project_name', 'مشروع محمل')
                self.current_calculation.profit_margin = project_data.get('profit_margin', 20.0)
                self.current_calculation.tax_rate = project_data.get('tax_rate', 15.0)

                # تحميل المواد
                self.current_calculation.materials = []
                for material_data in project_data.get('materials', []):
                    material = MaterialCost(**material_data)
                    self.current_calculation.materials.append(material)

                # تحميل العمالة
                self.current_calculation.labor = []
                for labor_data in project_data.get('labor', []):
                    labor = LaborCost(**labor_data)
                    self.current_calculation.labor.append(labor)

                # تحديث الواجهة
                self.project_name_edit.setText(self.current_calculation.project_name)
                self.profit_margin_spin.setValue(self.current_calculation.profit_margin)
                self.tax_rate_spin.setValue(self.current_calculation.tax_rate)

                self.update_materials_table()
                self.update_labor_table()
                self.update_calculations()

                QMessageBox.information(self, "نجح التحميل", f"تم تحميل المشروع بنجاح من:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "خطأ في التحميل", f"فشل في تحميل المشروع:\n{str(e)}")

    def export_report(self):
        """تصدير التقرير"""
        try:
            file_dialog = QFileDialog()
            file_path, _ = file_dialog.getSaveFileName(
                self,
                "تصدير تقرير التكلفة",
                f"تقرير_التكلفة_{self.current_calculation.project_name}_{datetime.now().strftime('%Y%m%d')}.html",
                "ملفات HTML (*.html);;ملفات نصية (*.txt);;جميع الملفات (*)"
            )

            if file_path:
                # حساب النتائج
                results = self.current_calculation.calculate_total()

                # إنشاء تقرير HTML
                html_content = self.generate_html_report(results)

                # حفظ التقرير
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(html_content)

                QMessageBox.information(self, "نجح التصدير", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

                # سؤال المستخدم إذا كان يريد فتح التقرير
                reply = QMessageBox.question(
                    self, "فتح التقرير",
                    "هل تريد فتح التقرير الآن؟",
                    QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
                )

                if reply == QMessageBox.StandardButton.Yes:
                    import webbrowser
                    webbrowser.open(file_path)

        except Exception as e:
            QMessageBox.critical(self, "خطأ في التصدير", f"فشل في تصدير التقرير:\n{str(e)}")

    def generate_html_report(self, results: Dict[str, float]) -> str:
        """إنشاء تقرير HTML"""
        html = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>تقرير التكلفة - {self.current_calculation.project_name}</title>
            <style>
                body {{
                    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                    margin: 20px;
                    background-color: #f5f5f5;
                    color: #333;
                }}
                .container {{
                    max-width: 800px;
                    margin: 0 auto;
                    background: white;
                    padding: 30px;
                    border-radius: 10px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }}
                .header {{
                    text-align: center;
                    border-bottom: 3px solid #3498db;
                    padding-bottom: 20px;
                    margin-bottom: 30px;
                }}
                .header h1 {{
                    color: #2c3e50;
                    margin: 0;
                    font-size: 28px;
                }}
                .header p {{
                    color: #7f8c8d;
                    margin: 10px 0 0 0;
                }}
                .section {{
                    margin-bottom: 30px;
                }}
                .section h2 {{
                    color: #2c3e50;
                    border-bottom: 2px solid #ecf0f1;
                    padding-bottom: 10px;
                    margin-bottom: 15px;
                }}
                table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }}
                th, td {{
                    padding: 12px;
                    text-align: right;
                    border-bottom: 1px solid #ecf0f1;
                }}
                th {{
                    background-color: #3498db;
                    color: white;
                    font-weight: bold;
                }}
                tr:nth-child(even) {{
                    background-color: #f8f9fa;
                }}
                .summary {{
                    background-color: #2c3e50;
                    color: white;
                    padding: 20px;
                    border-radius: 8px;
                    margin-top: 30px;
                }}
                .summary h3 {{
                    margin-top: 0;
                    color: #ecf0f1;
                }}
                .total {{
                    font-size: 24px;
                    font-weight: bold;
                    text-align: center;
                    color: #e74c3c;
                    margin-top: 15px;
                }}
                .footer {{
                    text-align: center;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #ecf0f1;
                    color: #7f8c8d;
                    font-size: 12px;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🧮 تقرير حاسبة التكلفة</h1>
                    <p>المشروع: {self.current_calculation.project_name}</p>
                    <p>تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}</p>
                </div>
        """

        # قسم المواد
        if self.current_calculation.materials:
            html += """
                <div class="section">
                    <h2>📦 تكلفة المواد</h2>
                    <table>
                        <thead>
                            <tr>
                                <th>المادة</th>
                                <th>الكمية</th>
                                <th>الوحدة</th>
                                <th>تكلفة الوحدة</th>
                                <th>نسبة الهدر</th>
                                <th>الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody>
            """

            for material in self.current_calculation.materials:
                total_with_waste = material.calculate_total_with_waste()
                html += f"""
                            <tr>
                                <td>{material.name}</td>
                                <td>{material.quantity:.2f}</td>
                                <td>{material.unit}</td>
                                <td>{material.unit_cost:.2f} د.ل</td>
                                <td>{material.waste_percentage:.1f}%</td>
                                <td>{total_with_waste:.2f} د.ل</td>
                            </tr>
                """

            html += f"""
                        </tbody>
                    </table>
                    <p><strong>إجمالي تكلفة المواد: {results['materials_total']:,.2f} د.ل</strong></p>
                </div>
            """

        # قسم العمالة
        if self.current_calculation.labor:
            html += """
                <div class="section">
                    <h2>👷 تكلفة العمالة</h2>
                    <table>
                        <thead>
                            <tr>
                                <th>المهمة</th>
                                <th>الساعات</th>
                                <th>أجر الساعة</th>
                                <th>معامل الصعوبة</th>
                                <th>الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody>
            """

            for labor in self.current_calculation.labor:
                total_with_difficulty = labor.calculate_total_with_difficulty()
                html += f"""
                            <tr>
                                <td>{labor.task_name}</td>
                                <td>{labor.hours:.1f}</td>
                                <td>{labor.hourly_rate:.2f} د.ل</td>
                                <td>{labor.difficulty_multiplier:.1f}</td>
                                <td>{total_with_difficulty:.2f} د.ل</td>
                            </tr>
                """

            html += f"""
                        </tbody>
                    </table>
                    <p><strong>إجمالي تكلفة العمالة: {results['labor_total']:,.2f} د.ل</strong></p>
                </div>
            """

        # الملخص النهائي
        html += f"""
                <div class="summary">
                    <h3>📊 ملخص التكاليف</h3>
                    <table style="color: white;">
                        <tr>
                            <td>تكلفة المواد:</td>
                            <td>{results['materials_total']:,.2f} د.ل</td>
                        </tr>
                        <tr>
                            <td>تكلفة العمالة:</td>
                            <td>{results['labor_total']:,.2f} د.ل</td>
                        </tr>
                        <tr>
                            <td>التكاليف العامة:</td>
                            <td>{results['overheads_total']:,.2f} د.ل</td>
                        </tr>
                        <tr>
                            <td>المجموع الفرعي:</td>
                            <td>{results['subtotal']:,.2f} د.ل</td>
                        </tr>
                        <tr>
                            <td>الربح ({self.current_calculation.profit_margin}%):</td>
                            <td>{results['profit']:,.2f} د.ل</td>
                        </tr>
                        <tr>
                            <td>الضريبة ({self.current_calculation.tax_rate}%):</td>
                            <td>{results['tax']:,.2f} د.ل</td>
                        </tr>
                    </table>
                    <div class="total">
                        الإجمالي النهائي: {results['total']:,.2f} د.ل
                    </div>
                </div>

                <div class="footer">
                    <p>تم إنشاء هذا التقرير بواسطة مصمم الأثاث الاحترافي - الإصدار 3.0</p>
                    <p>© 2024 Professional Furniture Designer</p>
                </div>
            </div>
        </body>
        </html>
        """

        return html

    def print_report(self):
        """طباعة التقرير"""
        try:
            # إنشاء تقرير مؤقت للطباعة
            results = self.current_calculation.calculate_total()
            html_content = self.generate_html_report(results)

            # إنشاء ملف مؤقت
            import tempfile
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as temp_file:
                temp_file.write(html_content)
                temp_file_path = temp_file.name

            # فتح نافذة الطباعة
            import webbrowser
            webbrowser.open(temp_file_path)

            QMessageBox.information(
                self, "طباعة",
                "تم فتح التقرير في المتصفح.\nيمكنك الآن طباعته باستخدام Ctrl+P"
            )

        except Exception as e:
            QMessageBox.critical(self, "خطأ في الطباعة", f"فشل في طباعة التقرير:\n{str(e)}")
