#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حاسبة التكلفة المتقدمة والمحسنة لمصمم الأثاث
Enhanced Advanced Cost Calculator for Furniture Designer
"""

import sys
import os
import json
import uuid
import math
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from typing import List, Optional, Dict, Any, Tuple
from pathlib import Path
from enum import Enum

from PySide6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox, QPushButton,
    QTableWidget, QTableWidgetItem, QTabWidget, QGroupBox, QTextEdit,
    QDialog, QDialogButtonBox, QMessageBox, QFileDialog, QProgressBar,
    QSplitter, Q<PERSON>rame, Q<PERSON><PERSON><PERSON><PERSON><PERSON>, QGrid<PERSON>ayout, <PERSON><PERSON><PERSON>ckBox, QSlider,
    QCalendarWidget, QDateEdit, QTimeEdit, QListWidget, QListWidgetItem,
    QTreeWidget, QTreeWidgetItem, QHeaderView, QAbstractItemView,
    QToolButton, QButtonGroup, QRadioButton, QSpacerItem, QSizePolicy
)
from PySide6.QtCore import Qt, QTimer, Signal, QDate, QTime, QDateTime, QThread
from PySide6.QtGui import QFont, QPixmap, QIcon, QPainter, QPen, QBrush, QColor, QLinearGradient

# استيراد الأنماط الحديثة
try:
    from ui.modern_styles import ModernStyleManager, CurrencyFormatter
    from ui.icons_manager import IconsManager
except ImportError:
    print("⚠️ تعذر استيراد الأنماط الحديثة، سيتم استخدام الأنماط الافتراضية")

    class ModernStyleManager:
        COLORS = {
            'primary': '#2C3E50',
            'secondary': '#3498DB',
            'success': '#27AE60',
            'warning': '#F39C12',
            'danger': '#E74C3C',
            'background': '#ECF0F1',
            'card': '#FFFFFF',
            'text': '#2C3E50',
            'text_light': '#FFFFFF'
        }

        FONT_SIZES = {
            'small': 10,
            'normal': 12,
            'heading': 14,
            'title': 16
        }

    class IconsManager:
        @staticmethod
        def get_action_icon(name):
            return QIcon()

        @staticmethod
        def get_standard_icon(name):
            return QIcon()

    class CurrencyFormatter:
        @staticmethod
        def format_currency(amount):
            return f"{amount:,.2f} د.ل"

        @staticmethod
        def format_currency_short(amount):
            if amount >= 1000000:
                return f"{amount/1000000:.1f}م د.ل"
            elif amount >= 1000:
                return f"{amount/1000:.1f}ألف د.ل"
            else:
                return f"{amount:.2f} د.ل"


# تعدادات للأنواع المختلفة
class MaterialType(Enum):
    """أنواع المواد"""
    WOOD = "خشب"
    METAL = "معدن"
    FABRIC = "قماش"
    GLASS = "زجاج"
    PLASTIC = "بلاستيك"
    HARDWARE = "عدد وأدوات"
    PAINT = "دهان"
    OTHER = "أخرى"


class SkillLevel(Enum):
    """مستويات المهارة"""
    BEGINNER = "مبتدئ"
    INTERMEDIATE = "متوسط"
    EXPERT = "خبير"
    MASTER = "أستاذ"


class ClientType(Enum):
    """أنواع العملاء"""
    NEW = "جديد"
    REGULAR = "عادي"
    VIP = "مميز"
    WHOLESALE = "بالجملة"


class ProjectSize(Enum):
    """أحجام المشاريع"""
    SMALL = "صغير"
    MEDIUM = "متوسط"
    LARGE = "كبير"
    ENTERPRISE = "مؤسسي"


@dataclass
class EnhancedMaterialCost:
    """تكلفة المواد المحسنة"""
    id: str
    name: str
    material_type: MaterialType
    quantity: float
    unit: str
    unit_cost: float
    supplier: str = ""
    delivery_cost: float = 0.0
    waste_percentage: float = 10.0
    notes: str = ""
    created_at: datetime = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if not self.id:
            self.id = str(uuid.uuid4())

    def get_waste_percentage_by_type(self) -> float:
        """حساب نسبة الهدر حسب نوع المادة"""
        waste_rates = {
            MaterialType.WOOD: 15.0,
            MaterialType.METAL: 8.0,
            MaterialType.FABRIC: 12.0,
            MaterialType.GLASS: 5.0,
            MaterialType.PLASTIC: 10.0,
            MaterialType.HARDWARE: 2.0,
            MaterialType.PAINT: 5.0,
            MaterialType.OTHER: 10.0
        }
        return waste_rates.get(self.material_type, 10.0)

    def calculate_total_cost(self) -> float:
        """حساب التكلفة الإجمالية"""
        base_cost = self.quantity * self.unit_cost
        waste_cost = base_cost * (self.get_waste_percentage_by_type() / 100)
        return base_cost + waste_cost + self.delivery_cost


@dataclass
class EnhancedLaborCost:
    """تكلفة العمالة المحسنة"""
    id: str
    task_name: str
    skill_level: SkillLevel
    hours: float
    hourly_rate: float
    difficulty_multiplier: float = 1.0
    overtime_hours: float = 0.0
    overtime_rate_multiplier: float = 1.5
    notes: str = ""
    created_at: datetime = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if not self.id:
            self.id = str(uuid.uuid4())

    def get_skill_multiplier(self) -> float:
        """حساب معامل المهارة"""
        multipliers = {
            SkillLevel.BEGINNER: 0.8,
            SkillLevel.INTERMEDIATE: 1.0,
            SkillLevel.EXPERT: 1.3,
            SkillLevel.MASTER: 1.6
        }
        return multipliers.get(self.skill_level, 1.0)

    def calculate_total_cost(self) -> float:
        """حساب التكلفة الإجمالية للعمالة"""
        # التكلفة الأساسية
        base_cost = self.hours * self.hourly_rate * self.get_skill_multiplier()

        # تكلفة الساعات الإضافية
        overtime_cost = self.overtime_hours * self.hourly_rate * self.overtime_rate_multiplier

        # تطبيق معامل الصعوبة
        total_cost = (base_cost + overtime_cost) * self.difficulty_multiplier

        return total_cost


@dataclass
class UtilityCost:
    """تكاليف المرافق"""
    electricity_kwh: float = 0.0
    electricity_rate: float = 0.15  # د.ل لكل كيلو واط ساعة
    water_cubic_meters: float = 0.0
    water_rate: float = 2.0  # د.ل لكل متر مكعب
    gas_cubic_meters: float = 0.0
    gas_rate: float = 1.5  # د.ل لكل متر مكعب
    internet_days: float = 0.0
    internet_daily_rate: float = 5.0  # د.ل يومياً

    def calculate_total_cost(self) -> float:
        """حساب إجمالي تكاليف المرافق"""
        electricity_cost = self.electricity_kwh * self.electricity_rate
        water_cost = self.water_cubic_meters * self.water_rate
        gas_cost = self.gas_cubic_meters * self.gas_rate
        internet_cost = self.internet_days * self.internet_daily_rate

        return electricity_cost + water_cost + gas_cost + internet_cost


@dataclass
class ProjectTemplate:
    """قالب المشروع"""
    id: str
    name: str
    description: str
    category: str
    materials: List[EnhancedMaterialCost]
    labor: List[EnhancedLaborCost]
    utilities: UtilityCost
    overhead_percentage: float = 15.0
    profit_margin: float = 25.0
    created_at: datetime = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if not self.id:
            self.id = str(uuid.uuid4())


@dataclass
class ProjectComparison:
    """مقارنة المشاريع"""
    project_name: str
    alternatives: List[Dict[str, Any]]
    selected_alternative: int = 0
    comparison_criteria: List[str] = None

    def __post_init__(self):
        if self.comparison_criteria is None:
            self.comparison_criteria = [
                "التكلفة الإجمالية",
                "الوقت المطلوب",
                "جودة المواد",
                "سهولة التنفيذ"
            ]


@dataclass
class ROICalculation:
    """حساب عائد الاستثمار"""
    initial_investment: float
    annual_revenue: float
    annual_costs: float
    project_lifespan_years: int = 5
    discount_rate: float = 0.1  # معدل الخصم 10%

    def calculate_npv(self) -> float:
        """حساب صافي القيمة الحالية"""
        annual_cash_flow = self.annual_revenue - self.annual_costs
        npv = -self.initial_investment

        for year in range(1, self.project_lifespan_years + 1):
            npv += annual_cash_flow / ((1 + self.discount_rate) ** year)

        return npv

    def calculate_roi_percentage(self) -> float:
        """حساب نسبة عائد الاستثمار"""
        total_profit = (self.annual_revenue - self.annual_costs) * self.project_lifespan_years
        roi = ((total_profit - self.initial_investment) / self.initial_investment) * 100
        return roi

    def calculate_payback_period(self) -> float:
        """حساب فترة الاسترداد بالسنوات"""
        annual_cash_flow = self.annual_revenue - self.annual_costs
        if annual_cash_flow <= 0:
            return float('inf')
        return self.initial_investment / annual_cash_flow


@dataclass
class EnhancedProjectCalculation:
    """حساب المشروع المحسن"""
    id: str
    project_name: str
    client_name: str
    client_type: ClientType
    project_size: ProjectSize
    materials: List[EnhancedMaterialCost]
    labor: List[EnhancedLaborCost]
    utilities: UtilityCost
    overhead_percentage: float = 15.0
    profit_margin: float = 25.0
    tax_rate: float = 0.0
    discount_percentage: float = 0.0
    budget_limit: float = 0.0
    deadline: QDate = None
    notes: str = ""
    revision_history: List[Dict[str, Any]] = None
    created_at: datetime = None
    updated_at: datetime = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()
        if not self.id:
            self.id = str(uuid.uuid4())
        if self.revision_history is None:
            self.revision_history = []

    def get_client_discount(self) -> float:
        """حساب خصم العميل حسب النوع"""
        discounts = {
            ClientType.NEW: 0.0,
            ClientType.REGULAR: 5.0,
            ClientType.VIP: 10.0,
            ClientType.WHOLESALE: 15.0
        }
        return discounts.get(self.client_type, 0.0)

    def get_project_complexity_multiplier(self) -> float:
        """حساب معامل تعقيد المشروع"""
        multipliers = {
            ProjectSize.SMALL: 1.0,
            ProjectSize.MEDIUM: 1.1,
            ProjectSize.LARGE: 1.2,
            ProjectSize.ENTERPRISE: 1.3
        }
        return multipliers.get(self.project_size, 1.0)

    def calculate_materials_total(self) -> float:
        """حساب إجمالي تكلفة المواد"""
        return sum(material.calculate_total_cost() for material in self.materials)

    def calculate_labor_total(self) -> float:
        """حساب إجمالي تكلفة العمالة"""
        base_total = sum(labor.calculate_total_cost() for labor in self.labor)
        complexity_multiplier = self.get_project_complexity_multiplier()
        return base_total * complexity_multiplier

    def calculate_utilities_total(self) -> float:
        """حساب إجمالي تكاليف المرافق"""
        return self.utilities.calculate_total_cost()

    def calculate_subtotal(self) -> float:
        """حساب المجموع الفرعي"""
        materials_total = self.calculate_materials_total()
        labor_total = self.calculate_labor_total()
        utilities_total = self.calculate_utilities_total()
        return materials_total + labor_total + utilities_total

    def calculate_overheads(self) -> float:
        """حساب التكاليف العامة"""
        subtotal = self.calculate_subtotal()
        return subtotal * (self.overhead_percentage / 100)

    def calculate_profit(self) -> float:
        """حساب الربح"""
        subtotal_with_overheads = self.calculate_subtotal() + self.calculate_overheads()
        return subtotal_with_overheads * (self.profit_margin / 100)

    def calculate_tax(self) -> float:
        """حساب الضريبة"""
        subtotal_with_profit = self.calculate_subtotal() + self.calculate_overheads() + self.calculate_profit()
        return subtotal_with_profit * (self.tax_rate / 100)

    def calculate_total_before_discount(self) -> float:
        """حساب الإجمالي قبل الخصم"""
        return (self.calculate_subtotal() +
                self.calculate_overheads() +
                self.calculate_profit() +
                self.calculate_tax())

    def calculate_discount_amount(self) -> float:
        """حساب مبلغ الخصم"""
        total_before_discount = self.calculate_total_before_discount()
        client_discount = self.get_client_discount()
        total_discount = client_discount + self.discount_percentage
        return total_before_discount * (total_discount / 100)

    def calculate_final_total(self) -> float:
        """حساب الإجمالي النهائي"""
        return self.calculate_total_before_discount() - self.calculate_discount_amount()

    def is_over_budget(self) -> bool:
        """فحص تجاوز الميزانية"""
        if self.budget_limit <= 0:
            return False
        return self.calculate_final_total() > self.budget_limit

    def get_budget_variance(self) -> float:
        """حساب انحراف الميزانية"""
        if self.budget_limit <= 0:
            return 0.0
        return self.calculate_final_total() - self.budget_limit

    def add_revision(self, description: str, user: str = "النظام"):
        """إضافة مراجعة للمشروع"""
        revision = {
            'timestamp': datetime.now().isoformat(),
            'user': user,
            'description': description,
            'total_cost': self.calculate_final_total()
        }
        self.revision_history.append(revision)
        self.updated_at = datetime.now()

    def get_cost_breakdown(self) -> Dict[str, float]:
        """الحصول على تفصيل التكاليف"""
        return {
            'materials': self.calculate_materials_total(),
            'labor': self.calculate_labor_total(),
            'utilities': self.calculate_utilities_total(),
            'overheads': self.calculate_overheads(),
            'profit': self.calculate_profit(),
            'tax': self.calculate_tax(),
            'discount': self.calculate_discount_amount(),
            'total': self.calculate_final_total()
        }


class CostChartWidget(QWidget):
    """ودجت الرسوم البيانية للتكاليف"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.cost_data = {}
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)

        # عنوان الرسم البياني
        title_label = QLabel("توزيع التكاليف")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                font-weight: bold;
                color: {ModernStyleManager.COLORS['primary']};
                padding: 10px;
            }}
        """)

        # منطقة الرسم
        self.chart_area = QLabel()
        self.chart_area.setMinimumSize(400, 300)
        self.chart_area.setStyleSheet(f"""
            QLabel {{
                background-color: {ModernStyleManager.COLORS['card']};
                border: 1px solid {ModernStyleManager.COLORS['primary']};
                border-radius: 8px;
            }}
        """)

        layout.addWidget(title_label)
        layout.addWidget(self.chart_area)

    def update_chart(self, cost_data: Dict[str, float]):
        """تحديث الرسم البياني"""
        self.cost_data = cost_data
        self.draw_pie_chart()

    def draw_pie_chart(self):
        """رسم الرسم البياني الدائري"""
        if not self.cost_data:
            return

        # إنشاء صورة للرسم
        pixmap = QPixmap(self.chart_area.size())
        pixmap.fill(QColor(ModernStyleManager.COLORS['card']))

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # حساب المجموع الكلي
        total = sum(self.cost_data.values())
        if total <= 0:
            painter.end()
            self.chart_area.setPixmap(pixmap)
            return

        # ألوان مختلفة لكل قسم
        colors = [
            QColor('#3498DB'),  # أزرق
            QColor('#E74C3C'),  # أحمر
            QColor('#2ECC71'),  # أخضر
            QColor('#F39C12'),  # برتقالي
            QColor('#9B59B6'),  # بنفسجي
            QColor('#1ABC9C'),  # تركوازي
            QColor('#34495E'),  # رمادي
            QColor('#E67E22')   # برتقالي داكن
        ]

        # رسم الدائرة
        rect = pixmap.rect().adjusted(20, 20, -20, -20)
        start_angle = 0

        for i, (category, value) in enumerate(self.cost_data.items()):
            if value <= 0:
                continue

            # حساب الزاوية
            span_angle = int((value / total) * 360 * 16)  # Qt uses 1/16th degrees

            # رسم القطاع
            painter.setBrush(QBrush(colors[i % len(colors)]))
            painter.setPen(QPen(QColor('#FFFFFF'), 2))
            painter.drawPie(rect, start_angle, span_angle)

            start_angle += span_angle

        painter.end()
        self.chart_area.setPixmap(pixmap)


class BuiltInCalculatorWidget(QWidget):
    """آلة حاسبة مدمجة"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_value = 0.0
        self.display_text = "0"
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)

        # شاشة العرض
        self.display = QLineEdit()
        self.display.setText("0")
        self.display.setReadOnly(True)
        self.display.setAlignment(Qt.AlignmentFlag.AlignRight)
        self.display.setStyleSheet(f"""
            QLineEdit {{
                font-size: 18px;
                font-weight: bold;
                padding: 10px;
                background-color: {ModernStyleManager.COLORS['background']};
                border: 2px solid {ModernStyleManager.COLORS['primary']};
                border-radius: 6px;
            }}
        """)

        # أزرار الحاسبة
        buttons_layout = QGridLayout()

        # تعريف الأزرار
        buttons = [
            ('C', 0, 0), ('±', 0, 1), ('%', 0, 2), ('÷', 0, 3),
            ('7', 1, 0), ('8', 1, 1), ('9', 1, 2), ('×', 1, 3),
            ('4', 2, 0), ('5', 2, 1), ('6', 2, 2), ('-', 2, 3),
            ('1', 3, 0), ('2', 3, 1), ('3', 3, 2), ('+', 3, 3),
            ('0', 4, 0, 1, 2), ('.', 4, 2), ('=', 4, 3)
        ]

        for button_data in buttons:
            text = button_data[0]
            row = button_data[1]
            col = button_data[2]
            row_span = button_data[3] if len(button_data) > 3 else 1
            col_span = button_data[4] if len(button_data) > 4 else 1

            button = QPushButton(text)
            button.setMinimumSize(50, 40)
            button.clicked.connect(lambda checked, t=text: self.button_clicked(t))

            # تنسيق الأزرار
            if text in ['C', '±', '%']:
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {ModernStyleManager.COLORS['secondary']};
                        color: white;
                        font-weight: bold;
                        border-radius: 6px;
                    }}
                    QPushButton:hover {{
                        background-color: {ModernStyleManager.COLORS['primary']};
                    }}
                """)
            elif text in ['÷', '×', '-', '+', '=']:
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {ModernStyleManager.COLORS['warning']};
                        color: white;
                        font-weight: bold;
                        border-radius: 6px;
                    }}
                    QPushButton:hover {{
                        background-color: {ModernStyleManager.COLORS['danger']};
                    }}
                """)
            else:
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {ModernStyleManager.COLORS['card']};
                        color: {ModernStyleManager.COLORS['text']};
                        border: 1px solid {ModernStyleManager.COLORS['primary']};
                        border-radius: 6px;
                    }}
                    QPushButton:hover {{
                        background-color: {ModernStyleManager.COLORS['background']};
                    }}
                """)

            buttons_layout.addWidget(button, row, col, row_span, col_span)

        layout.addWidget(self.display)
        layout.addLayout(buttons_layout)

    def button_clicked(self, text):
        """معالجة النقر على الأزرار"""
        if text.isdigit() or text == '.':
            if self.display_text == "0":
                self.display_text = text
            else:
                self.display_text += text
        elif text == 'C':
            self.display_text = "0"
            self.current_value = 0.0
        elif text == '±':
            if self.display_text != "0":
                if self.display_text.startswith('-'):
                    self.display_text = self.display_text[1:]
                else:
                    self.display_text = '-' + self.display_text
        # يمكن إضافة المزيد من العمليات هنا

        self.display.setText(self.display_text)


class EnhancedCostCalculatorWidget(QWidget):
    """حاسبة التكلفة المحسنة"""

    # إشارات
    project_saved = Signal(str)  # إشارة حفظ المشروع
    budget_exceeded = Signal(float)  # إشارة تجاوز الميزانية

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_project = None
        self.project_templates = {}
        self.load_templates()
        self.init_ui()
        self.setup_connections()

    def init_ui(self):
        """تهيئة واجهة المستخدم المحسنة"""
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # المقسم الرئيسي
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # اللوحة اليسرى - الإدخال والتحكم
        left_panel = self.create_input_panel()
        main_splitter.addWidget(left_panel)

        # اللوحة الوسطى - النتائج والرسوم البيانية
        center_panel = self.create_results_panel()
        main_splitter.addWidget(center_panel)

        # اللوحة اليمنى - الأدوات المساعدة
        right_panel = self.create_tools_panel()
        main_splitter.addWidget(right_panel)

        # تعيين النسب
        main_splitter.setSizes([400, 500, 300])

        main_layout.addWidget(main_splitter)

    def create_input_panel(self) -> QWidget:
        """إنشاء لوحة الإدخال"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # عنوان اللوحة
        title_label = QLabel("🧮 حاسبة التكلفة المتقدمة")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['title']}px;
                font-weight: bold;
                color: {ModernStyleManager.COLORS['primary']};
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {ModernStyleManager.COLORS['primary']},
                    stop:1 {ModernStyleManager.COLORS['secondary']});
                color: white;
                border-radius: 10px;
                margin-bottom: 10px;
            }}
        """)

        # تبويبات الإدخال
        input_tabs = QTabWidget()

        # تبويب معلومات المشروع
        project_tab = self.create_project_info_tab()
        input_tabs.addTab(project_tab, "📋 معلومات المشروع")

        # تبويب المواد
        materials_tab = self.create_materials_tab()
        input_tabs.addTab(materials_tab, "🔨 المواد")

        # تبويب العمالة
        labor_tab = self.create_labor_tab()
        input_tabs.addTab(labor_tab, "👷 العمالة")

        # تبويب المرافق
        utilities_tab = self.create_utilities_tab()
        input_tabs.addTab(utilities_tab, "⚡ المرافق")

        # تبويب الإعدادات المتقدمة
        advanced_tab = self.create_advanced_settings_tab()
        input_tabs.addTab(advanced_tab, "⚙️ إعدادات متقدمة")

        layout.addWidget(title_label)
        layout.addWidget(input_tabs)

        return panel

    def create_project_info_tab(self) -> QWidget:
        """إنشاء تبويب معلومات المشروع"""
        tab = QWidget()
        layout = QFormLayout(tab)

        # اسم المشروع
        self.project_name_edit = QLineEdit()
        self.project_name_edit.setPlaceholderText("أدخل اسم المشروع")
        layout.addRow("اسم المشروع:", self.project_name_edit)

        # اسم العميل
        self.client_name_edit = QLineEdit()
        self.client_name_edit.setPlaceholderText("أدخل اسم العميل")
        layout.addRow("اسم العميل:", self.client_name_edit)

        # نوع العميل
        self.client_type_combo = QComboBox()
        for client_type in ClientType:
            self.client_type_combo.addItem(client_type.value, client_type)
        layout.addRow("نوع العميل:", self.client_type_combo)

        # حجم المشروع
        self.project_size_combo = QComboBox()
        for project_size in ProjectSize:
            self.project_size_combo.addItem(project_size.value, project_size)
        layout.addRow("حجم المشروع:", self.project_size_combo)

        # الميزانية المحددة
        self.budget_limit_spin = QDoubleSpinBox()
        self.budget_limit_spin.setRange(0, *********)
        self.budget_limit_spin.setSuffix(" د.ل")
        self.budget_limit_spin.setSpecialValueText("غير محدد")
        layout.addRow("الميزانية المحددة:", self.budget_limit_spin)

        # تاريخ التسليم
        self.deadline_edit = QDateEdit()
        self.deadline_edit.setDate(QDate.currentDate().addDays(30))
        self.deadline_edit.setCalendarPopup(True)
        layout.addRow("تاريخ التسليم:", self.deadline_edit)

        # ملاحظات المشروع
        self.project_notes_edit = QTextEdit()
        self.project_notes_edit.setMaximumHeight(100)
        self.project_notes_edit.setPlaceholderText("ملاحظات إضافية حول المشروع...")
        layout.addRow("ملاحظات:", self.project_notes_edit)

        return tab

    def create_materials_tab(self) -> QWidget:
        """إنشاء تبويب المواد"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # أزرار التحكم
        controls_layout = QHBoxLayout()

        add_material_btn = QPushButton("➕ إضافة مادة")
        add_material_btn.setIcon(IconsManager.get_action_icon('add_item'))
        add_material_btn.clicked.connect(self.add_material)

        edit_material_btn = QPushButton("✏️ تعديل")
        edit_material_btn.setIcon(IconsManager.get_action_icon('edit'))
        edit_material_btn.clicked.connect(self.edit_material)

        delete_material_btn = QPushButton("🗑️ حذف")
        delete_material_btn.setIcon(IconsManager.get_action_icon('delete'))
        delete_material_btn.clicked.connect(self.delete_material)

        import_materials_btn = QPushButton("📥 استيراد من المخزون")
        import_materials_btn.setIcon(IconsManager.get_action_icon('import'))
        import_materials_btn.clicked.connect(self.import_from_inventory)

        controls_layout.addWidget(add_material_btn)
        controls_layout.addWidget(edit_material_btn)
        controls_layout.addWidget(delete_material_btn)
        controls_layout.addWidget(import_materials_btn)
        controls_layout.addStretch()

        # جدول المواد
        self.materials_table = QTableWidget()
        self.materials_table.setColumnCount(8)
        self.materials_table.setHorizontalHeaderLabels([
            "المادة", "النوع", "الكمية", "الوحدة", "سعر الوحدة",
            "تكلفة التوصيل", "نسبة الهدر", "الإجمالي"
        ])

        # تحسين عرض الجدول
        header = self.materials_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.materials_table.setAlternatingRowColors(True)
        self.materials_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)

        layout.addLayout(controls_layout)
        layout.addWidget(self.materials_table)

        return tab

    def create_labor_tab(self) -> QWidget:
        """إنشاء تبويب العمالة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # أزرار التحكم
        controls_layout = QHBoxLayout()

        add_labor_btn = QPushButton("➕ إضافة مهمة")
        add_labor_btn.setIcon(IconsManager.get_action_icon('add_item'))
        add_labor_btn.clicked.connect(self.add_labor)

        edit_labor_btn = QPushButton("✏️ تعديل")
        edit_labor_btn.setIcon(IconsManager.get_action_icon('edit'))
        edit_labor_btn.clicked.connect(self.edit_labor)

        delete_labor_btn = QPushButton("🗑️ حذف")
        delete_labor_btn.setIcon(IconsManager.get_action_icon('delete'))
        delete_labor_btn.clicked.connect(self.delete_labor)

        controls_layout.addWidget(add_labor_btn)
        controls_layout.addWidget(edit_labor_btn)
        controls_layout.addWidget(delete_labor_btn)
        controls_layout.addStretch()

        # جدول العمالة
        self.labor_table = QTableWidget()
        self.labor_table.setColumnCount(7)
        self.labor_table.setHorizontalHeaderLabels([
            "المهمة", "مستوى المهارة", "الساعات", "أجر الساعة",
            "ساعات إضافية", "معامل الصعوبة", "الإجمالي"
        ])

        # تحسين عرض الجدول
        header = self.labor_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.labor_table.setAlternatingRowColors(True)
        self.labor_table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)

        layout.addLayout(controls_layout)
        layout.addWidget(self.labor_table)

        return tab

    def create_utilities_tab(self) -> QWidget:
        """إنشاء تبويب المرافق"""
        tab = QWidget()
        layout = QFormLayout(tab)

        # الكهرباء
        utilities_group = QGroupBox("⚡ تكاليف المرافق")
        utilities_layout = QFormLayout(utilities_group)

        self.electricity_kwh_spin = QDoubleSpinBox()
        self.electricity_kwh_spin.setRange(0, 99999)
        self.electricity_kwh_spin.setSuffix(" كيلو واط ساعة")
        utilities_layout.addRow("استهلاك الكهرباء:", self.electricity_kwh_spin)

        self.electricity_rate_spin = QDoubleSpinBox()
        self.electricity_rate_spin.setRange(0, 999)
        self.electricity_rate_spin.setValue(0.15)
        self.electricity_rate_spin.setSuffix(" د.ل/كيلو واط ساعة")
        utilities_layout.addRow("سعر الكيلو واط ساعة:", self.electricity_rate_spin)

        # المياه
        self.water_cubic_spin = QDoubleSpinBox()
        self.water_cubic_spin.setRange(0, 99999)
        self.water_cubic_spin.setSuffix(" متر مكعب")
        utilities_layout.addRow("استهلاك المياه:", self.water_cubic_spin)

        self.water_rate_spin = QDoubleSpinBox()
        self.water_rate_spin.setRange(0, 999)
        self.water_rate_spin.setValue(2.0)
        self.water_rate_spin.setSuffix(" د.ل/متر مكعب")
        utilities_layout.addRow("سعر المتر المكعب للمياه:", self.water_rate_spin)

        # الغاز
        self.gas_cubic_spin = QDoubleSpinBox()
        self.gas_cubic_spin.setRange(0, 99999)
        self.gas_cubic_spin.setSuffix(" متر مكعب")
        utilities_layout.addRow("استهلاك الغاز:", self.gas_cubic_spin)

        self.gas_rate_spin = QDoubleSpinBox()
        self.gas_rate_spin.setRange(0, 999)
        self.gas_rate_spin.setValue(1.5)
        self.gas_rate_spin.setSuffix(" د.ل/متر مكعب")
        utilities_layout.addRow("سعر المتر المكعب للغاز:", self.gas_rate_spin)

        # الإنترنت
        self.internet_days_spin = QDoubleSpinBox()
        self.internet_days_spin.setRange(0, 365)
        self.internet_days_spin.setSuffix(" يوم")
        utilities_layout.addRow("أيام استخدام الإنترنت:", self.internet_days_spin)

        self.internet_rate_spin = QDoubleSpinBox()
        self.internet_rate_spin.setRange(0, 999)
        self.internet_rate_spin.setValue(5.0)
        self.internet_rate_spin.setSuffix(" د.ل/يوم")
        utilities_layout.addRow("تكلفة الإنترنت اليومية:", self.internet_rate_spin)

        layout.addWidget(utilities_group)

        return tab

    def create_advanced_settings_tab(self) -> QWidget:
        """إنشاء تبويب الإعدادات المتقدمة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # إعدادات الأرباح والضرائب
        profit_group = QGroupBox("💰 إعدادات الأرباح والضرائب")
        profit_layout = QFormLayout(profit_group)

        self.overhead_percentage_spin = QDoubleSpinBox()
        self.overhead_percentage_spin.setRange(0, 100)
        self.overhead_percentage_spin.setValue(15.0)
        self.overhead_percentage_spin.setSuffix("%")
        profit_layout.addRow("نسبة التكاليف العامة:", self.overhead_percentage_spin)

        self.profit_margin_spin = QDoubleSpinBox()
        self.profit_margin_spin.setRange(0, 100)
        self.profit_margin_spin.setValue(25.0)
        self.profit_margin_spin.setSuffix("%")
        profit_layout.addRow("هامش الربح:", self.profit_margin_spin)

        self.tax_rate_spin = QDoubleSpinBox()
        self.tax_rate_spin.setRange(0, 100)
        self.tax_rate_spin.setValue(0.0)
        self.tax_rate_spin.setSuffix("%")
        profit_layout.addRow("معدل الضريبة:", self.tax_rate_spin)

        self.discount_percentage_spin = QDoubleSpinBox()
        self.discount_percentage_spin.setRange(0, 100)
        self.discount_percentage_spin.setValue(0.0)
        self.discount_percentage_spin.setSuffix("%")
        profit_layout.addRow("خصم إضافي:", self.discount_percentage_spin)

        layout.addWidget(profit_group)

        # إعدادات ROI
        roi_group = QGroupBox("📈 حساب عائد الاستثمار (ROI)")
        roi_layout = QFormLayout(roi_group)

        self.initial_investment_spin = QDoubleSpinBox()
        self.initial_investment_spin.setRange(0, *********)
        self.initial_investment_spin.setSuffix(" د.ل")
        roi_layout.addRow("الاستثمار الأولي:", self.initial_investment_spin)

        self.annual_revenue_spin = QDoubleSpinBox()
        self.annual_revenue_spin.setRange(0, *********)
        self.annual_revenue_spin.setSuffix(" د.ل")
        roi_layout.addRow("الإيرادات السنوية المتوقعة:", self.annual_revenue_spin)

        self.annual_costs_spin = QDoubleSpinBox()
        self.annual_costs_spin.setRange(0, *********)
        self.annual_costs_spin.setSuffix(" د.ل")
        roi_layout.addRow("التكاليف السنوية:", self.annual_costs_spin)

        self.project_lifespan_spin = QSpinBox()
        self.project_lifespan_spin.setRange(1, 50)
        self.project_lifespan_spin.setValue(5)
        self.project_lifespan_spin.setSuffix(" سنة")
        roi_layout.addRow("عمر المشروع:", self.project_lifespan_spin)

        self.discount_rate_spin = QDoubleSpinBox()
        self.discount_rate_spin.setRange(0, 1)
        self.discount_rate_spin.setValue(0.1)
        self.discount_rate_spin.setDecimals(3)
        roi_layout.addRow("معدل الخصم:", self.discount_rate_spin)

        layout.addWidget(roi_group)

        return tab

    def create_results_panel(self) -> QWidget:
        """إنشاء لوحة النتائج"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # عنوان اللوحة
        title_label = QLabel("📊 النتائج والتحليل")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                font-weight: bold;
                color: {ModernStyleManager.COLORS['primary']};
                padding: 10px;
                background-color: {ModernStyleManager.COLORS['card']};
                border: 2px solid {ModernStyleManager.COLORS['primary']};
                border-radius: 8px;
                margin-bottom: 10px;
            }}
        """)

        # تبويبات النتائج
        results_tabs = QTabWidget()

        # تبويب ملخص التكاليف
        summary_tab = self.create_cost_summary_tab()
        results_tabs.addTab(summary_tab, "💰 ملخص التكاليف")

        # تبويب الرسوم البيانية
        charts_tab = self.create_charts_tab()
        results_tabs.addTab(charts_tab, "📈 الرسوم البيانية")

        # تبويب تحليل ROI
        roi_tab = self.create_roi_analysis_tab()
        results_tabs.addTab(roi_tab, "📊 تحليل ROI")

        # تبويب المقارنات
        comparison_tab = self.create_comparison_tab()
        results_tabs.addTab(comparison_tab, "⚖️ المقارنات")

        layout.addWidget(title_label)
        layout.addWidget(results_tabs)

        return panel

    def create_cost_summary_tab(self) -> QWidget:
        """إنشاء تبويب ملخص التكاليف"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # ملخص سريع
        summary_label = QLabel("ملخص التكاليف")
        summary_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        layout.addWidget(summary_label)

        return tab

    def create_charts_tab(self) -> QWidget:
        """إنشاء تبويب الرسوم البيانية"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # الرسم البياني
        self.chart_widget = CostChartWidget()
        layout.addWidget(self.chart_widget)

        return tab

    def create_roi_analysis_tab(self) -> QWidget:
        """إنشاء تبويب تحليل ROI"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # تحليل ROI
        roi_label = QLabel("تحليل عائد الاستثمار")
        roi_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        layout.addWidget(roi_label)

        return tab

    def create_comparison_tab(self) -> QWidget:
        """إنشاء تبويب المقارنات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # المقارنات
        comparison_label = QLabel("مقارنة البدائل")
        comparison_label.setStyleSheet("font-weight: bold; font-size: 14px;")
        layout.addWidget(comparison_label)

        return tab

    def create_tools_panel(self) -> QWidget:
        """إنشاء لوحة الأدوات"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # عنوان اللوحة
        title_label = QLabel("🛠️ الأدوات المساعدة")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                font-weight: bold;
                color: {ModernStyleManager.COLORS['primary']};
                padding: 10px;
                background-color: {ModernStyleManager.COLORS['card']};
                border: 2px solid {ModernStyleManager.COLORS['primary']};
                border-radius: 8px;
                margin-bottom: 10px;
            }}
        """)

        # آلة حاسبة مدمجة
        calculator_group = QGroupBox("🧮 آلة حاسبة")
        calculator_layout = QVBoxLayout(calculator_group)

        self.built_in_calculator = BuiltInCalculatorWidget()
        calculator_layout.addWidget(self.built_in_calculator)

        layout.addWidget(title_label)
        layout.addWidget(calculator_group)
        layout.addStretch()

        return panel

    def setup_connections(self):
        """إعداد الاتصالات"""
        # يمكن إضافة اتصالات الإشارات هنا
        pass

    def load_templates(self):
        """تحميل قوالب المشاريع"""
        # يمكن تحميل القوالب من ملف JSON
        pass

    # دوال الأزرار
    def add_material(self):
        """إضافة مادة جديدة"""
        QMessageBox.information(self, "إضافة مادة", "سيتم فتح حوار إضافة مادة قريباً")

    def edit_material(self):
        """تعديل مادة"""
        QMessageBox.information(self, "تعديل مادة", "سيتم فتح حوار تعديل المادة قريباً")

    def delete_material(self):
        """حذف مادة"""
        QMessageBox.information(self, "حذف مادة", "سيتم حذف المادة المحددة قريباً")

    def import_from_inventory(self):
        """استيراد من المخزون"""
        QMessageBox.information(self, "استيراد من المخزون", "سيتم ربط نظام المخزون قريباً")

    def add_labor(self):
        """إضافة مهمة عمالة"""
        QMessageBox.information(self, "إضافة مهمة", "سيتم فتح حوار إضافة مهمة قريباً")

    def edit_labor(self):
        """تعديل مهمة عمالة"""
        QMessageBox.information(self, "تعديل مهمة", "سيتم فتح حوار تعديل المهمة قريباً")

    def delete_labor(self):
        """حذف مهمة عمالة"""
        QMessageBox.information(self, "حذف مهمة", "سيتم حذف المهمة المحددة قريباً")