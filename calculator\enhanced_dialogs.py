#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حوارات حاسبة التكلفة المحسنة
Enhanced Cost Calculator Dialogs
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLabel, 
    QLineEdit, QComboBox, QDoubleSpinBox, QSpinBox, QTextEdit,
    QPushButton, QDialogButtonBox, QGroupBox, QCheckBox,
    QTabWidget, QWidget, QMessageBox, QFileDialog, QListWidget,
    QListWidgetItem, QTreeWidget, QTreeWidgetItem
)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QIcon

from .enhanced_cost_calculator import (
    EnhancedMaterialCost, EnhancedLaborCost, MaterialType, 
    SkillLevel, ProjectTemplate
)

try:
    from ui.modern_styles import ModernStyleManager, CurrencyFormatter
    from ui.icons_manager import IconsManager
except ImportError:
    class ModernStyleManager:
        COLORS = {'primary': '#2C3E50', 'secondary': '#3498DB', 'card': '#FFFFFF'}
        FONT_SIZES = {'normal': 12, 'heading': 14}
    
    class IconsManager:
        @staticmethod
        def get_action_icon(name): return QIcon()
    
    class CurrencyFormatter:
        @staticmethod
        def format_currency(amount): return f"{amount:,.2f} د.ل"


class EnhancedMaterialDialog(QDialog):
    """حوار إضافة/تعديل المواد المحسن"""
    
    def __init__(self, material=None, parent=None):
        super().__init__(parent)
        self.material = material
        self.is_edit_mode = material is not None
        self.init_ui()
        if self.material:
            self.load_material_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("تعديل المادة" if self.is_edit_mode else "إضافة مادة جديدة")
        self.setModal(True)
        self.resize(500, 600)
        
        layout = QVBoxLayout(self)
        
        # تبويبات البيانات
        tabs = QTabWidget()
        
        # تبويب المعلومات الأساسية
        basic_tab = QWidget()
        basic_layout = QFormLayout(basic_tab)
        
        # اسم المادة
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("اسم المادة")
        basic_layout.addRow("اسم المادة *:", self.name_edit)
        
        # نوع المادة
        self.material_type_combo = QComboBox()
        for material_type in MaterialType:
            self.material_type_combo.addItem(material_type.value, material_type)
        self.material_type_combo.currentTextChanged.connect(self.update_waste_percentage)
        basic_layout.addRow("نوع المادة:", self.material_type_combo)
        
        # الكمية
        self.quantity_spin = QDoubleSpinBox()
        self.quantity_spin.setRange(0.001, 999999.999)
        self.quantity_spin.setDecimals(3)
        self.quantity_spin.setValue(1.0)
        self.quantity_spin.valueChanged.connect(self.calculate_total)
        basic_layout.addRow("الكمية:", self.quantity_spin)
        
        # الوحدة
        self.unit_combo = QComboBox()
        self.unit_combo.setEditable(True)
        self.unit_combo.addItems([
            "متر", "متر مربع", "متر مكعب", "كيلوجرام", "جرام", 
            "قطعة", "لوح", "عبوة", "لتر", "مليلتر"
        ])
        basic_layout.addRow("الوحدة:", self.unit_combo)
        
        # سعر الوحدة
        self.unit_cost_spin = QDoubleSpinBox()
        self.unit_cost_spin.setRange(0.01, 999999.99)
        self.unit_cost_spin.setDecimals(2)
        self.unit_cost_spin.setSuffix(" د.ل")
        self.unit_cost_spin.valueChanged.connect(self.calculate_total)
        basic_layout.addRow("سعر الوحدة:", self.unit_cost_spin)
        
        tabs.addTab(basic_tab, "المعلومات الأساسية")
        
        # تبويب التفاصيل المتقدمة
        advanced_tab = QWidget()
        advanced_layout = QFormLayout(advanced_tab)
        
        # المورد
        self.supplier_edit = QLineEdit()
        self.supplier_edit.setPlaceholderText("اسم المورد")
        advanced_layout.addRow("المورد:", self.supplier_edit)
        
        # تكلفة التوصيل
        self.delivery_cost_spin = QDoubleSpinBox()
        self.delivery_cost_spin.setRange(0, 99999.99)
        self.delivery_cost_spin.setDecimals(2)
        self.delivery_cost_spin.setSuffix(" د.ل")
        self.delivery_cost_spin.valueChanged.connect(self.calculate_total)
        advanced_layout.addRow("تكلفة التوصيل:", self.delivery_cost_spin)
        
        # نسبة الهدر
        self.waste_percentage_spin = QDoubleSpinBox()
        self.waste_percentage_spin.setRange(0, 50)
        self.waste_percentage_spin.setDecimals(1)
        self.waste_percentage_spin.setValue(10.0)
        self.waste_percentage_spin.setSuffix("%")
        self.waste_percentage_spin.valueChanged.connect(self.calculate_total)
        advanced_layout.addRow("نسبة الهدر:", self.waste_percentage_spin)
        
        # ملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")
        advanced_layout.addRow("ملاحظات:", self.notes_edit)
        
        tabs.addTab(advanced_tab, "التفاصيل المتقدمة")
        
        # تبويب الحساب
        calculation_tab = QWidget()
        calc_layout = QFormLayout(calculation_tab)
        
        # التكلفة الأساسية
        self.base_cost_label = QLabel("0.00 د.ل")
        self.base_cost_label.setStyleSheet("font-weight: bold; color: #2C3E50;")
        calc_layout.addRow("التكلفة الأساسية:", self.base_cost_label)
        
        # تكلفة الهدر
        self.waste_cost_label = QLabel("0.00 د.ل")
        self.waste_cost_label.setStyleSheet("font-weight: bold; color: #E67E22;")
        calc_layout.addRow("تكلفة الهدر:", self.waste_cost_label)
        
        # تكلفة التوصيل
        self.delivery_label = QLabel("0.00 د.ل")
        self.delivery_label.setStyleSheet("font-weight: bold; color: #8E44AD;")
        calc_layout.addRow("تكلفة التوصيل:", self.delivery_label)
        
        # الإجمالي
        self.total_cost_label = QLabel("0.00 د.ل")
        self.total_cost_label.setStyleSheet(f"""
            font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
            font-weight: bold;
            color: {ModernStyleManager.COLORS['primary']};
            padding: 8px;
            background-color: {ModernStyleManager.COLORS['card']};
            border: 2px solid {ModernStyleManager.COLORS['primary']};
            border-radius: 6px;
        """)
        calc_layout.addRow("الإجمالي:", self.total_cost_label)
        
        tabs.addTab(calculation_tab, "الحساب")
        
        layout.addWidget(tabs)
        
        # أزرار التحكم
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        
        ok_button = buttons.button(QDialogButtonBox.StandardButton.Ok)
        ok_button.setText("حفظ")
        
        cancel_button = buttons.button(QDialogButtonBox.StandardButton.Cancel)
        cancel_button.setText("إلغاء")
        
        layout.addWidget(buttons)
        
        # حساب التكلفة الأولية
        self.calculate_total()
    
    def update_waste_percentage(self):
        """تحديث نسبة الهدر حسب نوع المادة"""
        material_type = self.material_type_combo.currentData()
        if material_type:
            # إنشاء كائن مؤقت للحصول على نسبة الهدر
            temp_material = EnhancedMaterialCost(
                id="", name="", material_type=material_type,
                quantity=1, unit="", unit_cost=1
            )
            waste_percentage = temp_material.get_waste_percentage_by_type()
            self.waste_percentage_spin.setValue(waste_percentage)
    
    def calculate_total(self):
        """حساب التكلفة الإجمالية"""
        quantity = self.quantity_spin.value()
        unit_cost = self.unit_cost_spin.value()
        delivery_cost = self.delivery_cost_spin.value()
        waste_percentage = self.waste_percentage_spin.value()
        
        # التكلفة الأساسية
        base_cost = quantity * unit_cost
        
        # تكلفة الهدر
        waste_cost = base_cost * (waste_percentage / 100)
        
        # الإجمالي
        total_cost = base_cost + waste_cost + delivery_cost
        
        # تحديث التسميات
        self.base_cost_label.setText(CurrencyFormatter.format_currency(base_cost))
        self.waste_cost_label.setText(CurrencyFormatter.format_currency(waste_cost))
        self.delivery_label.setText(CurrencyFormatter.format_currency(delivery_cost))
        self.total_cost_label.setText(CurrencyFormatter.format_currency(total_cost))
    
    def load_material_data(self):
        """تحميل بيانات المادة للتعديل"""
        if self.material:
            self.name_edit.setText(self.material.name)
            
            # تعيين نوع المادة
            for i in range(self.material_type_combo.count()):
                if self.material_type_combo.itemData(i) == self.material.material_type:
                    self.material_type_combo.setCurrentIndex(i)
                    break
            
            self.quantity_spin.setValue(self.material.quantity)
            self.unit_combo.setCurrentText(self.material.unit)
            self.unit_cost_spin.setValue(self.material.unit_cost)
            self.supplier_edit.setText(self.material.supplier)
            self.delivery_cost_spin.setValue(self.material.delivery_cost)
            self.waste_percentage_spin.setValue(self.material.waste_percentage)
            self.notes_edit.setPlainText(self.material.notes)
            
            self.calculate_total()
    
    def get_material_data(self) -> EnhancedMaterialCost:
        """الحصول على بيانات المادة من النموذج"""
        material_id = self.material.id if self.material else ""
        
        return EnhancedMaterialCost(
            id=material_id,
            name=self.name_edit.text().strip(),
            material_type=self.material_type_combo.currentData(),
            quantity=self.quantity_spin.value(),
            unit=self.unit_combo.currentText().strip(),
            unit_cost=self.unit_cost_spin.value(),
            supplier=self.supplier_edit.text().strip(),
            delivery_cost=self.delivery_cost_spin.value(),
            waste_percentage=self.waste_percentage_spin.value(),
            notes=self.notes_edit.toPlainText().strip()
        )
    
    def accept(self):
        """التحقق من صحة البيانات قبل الحفظ"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المادة")
            self.name_edit.setFocus()
            return
        
        if self.quantity_spin.value() <= 0:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال كمية صحيحة")
            self.quantity_spin.setFocus()
            return
        
        if self.unit_cost_spin.value() <= 0:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال سعر وحدة صحيح")
            self.unit_cost_spin.setFocus()
            return
        
        if not self.unit_combo.currentText().strip():
            QMessageBox.warning(self, "خطأ", "يرجى تحديد الوحدة")
            self.unit_combo.setFocus()
            return
        
        super().accept()


class EnhancedLaborDialog(QDialog):
    """حوار إضافة/تعديل العمالة المحسن"""
    
    def __init__(self, labor=None, parent=None):
        super().__init__(parent)
        self.labor = labor
        self.is_edit_mode = labor is not None
        self.init_ui()
        if self.labor:
            self.load_labor_data()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("تعديل مهمة العمالة" if self.is_edit_mode else "إضافة مهمة عمالة جديدة")
        self.setModal(True)
        self.resize(500, 500)
        
        layout = QVBoxLayout(self)
        
        # تبويبات البيانات
        tabs = QTabWidget()
        
        # تبويب المعلومات الأساسية
        basic_tab = QWidget()
        basic_layout = QFormLayout(basic_tab)
        
        # اسم المهمة
        self.task_name_edit = QLineEdit()
        self.task_name_edit.setPlaceholderText("اسم المهمة")
        basic_layout.addRow("اسم المهمة *:", self.task_name_edit)
        
        # مستوى المهارة
        self.skill_level_combo = QComboBox()
        for skill_level in SkillLevel:
            self.skill_level_combo.addItem(skill_level.value, skill_level)
        self.skill_level_combo.currentTextChanged.connect(self.calculate_total)
        basic_layout.addRow("مستوى المهارة:", self.skill_level_combo)
        
        # عدد الساعات
        self.hours_spin = QDoubleSpinBox()
        self.hours_spin.setRange(0.1, 999.9)
        self.hours_spin.setDecimals(1)
        self.hours_spin.setValue(8.0)
        self.hours_spin.setSuffix(" ساعة")
        self.hours_spin.valueChanged.connect(self.calculate_total)
        basic_layout.addRow("عدد الساعات:", self.hours_spin)
        
        # أجر الساعة
        self.hourly_rate_spin = QDoubleSpinBox()
        self.hourly_rate_spin.setRange(1.0, 9999.99)
        self.hourly_rate_spin.setDecimals(2)
        self.hourly_rate_spin.setValue(15.0)
        self.hourly_rate_spin.setSuffix(" د.ل/ساعة")
        self.hourly_rate_spin.valueChanged.connect(self.calculate_total)
        basic_layout.addRow("أجر الساعة:", self.hourly_rate_spin)
        
        tabs.addTab(basic_tab, "المعلومات الأساسية")
        
        # تبويب الإعدادات المتقدمة
        advanced_tab = QWidget()
        advanced_layout = QFormLayout(advanced_tab)
        
        # معامل الصعوبة
        self.difficulty_spin = QDoubleSpinBox()
        self.difficulty_spin.setRange(0.5, 5.0)
        self.difficulty_spin.setDecimals(1)
        self.difficulty_spin.setValue(1.0)
        self.difficulty_spin.setSingleStep(0.1)
        self.difficulty_spin.valueChanged.connect(self.calculate_total)
        advanced_layout.addRow("معامل الصعوبة:", self.difficulty_spin)
        
        # شرح معامل الصعوبة
        difficulty_info = QLabel("0.5 = سهل جداً | 1.0 = عادي | 2.0 = صعب | 3.0+ = معقد جداً")
        difficulty_info.setStyleSheet("color: #7f8c8d; font-size: 11px; font-style: italic;")
        advanced_layout.addRow("", difficulty_info)
        
        # الساعات الإضافية
        self.overtime_hours_spin = QDoubleSpinBox()
        self.overtime_hours_spin.setRange(0, 99.9)
        self.overtime_hours_spin.setDecimals(1)
        self.overtime_hours_spin.setValue(0.0)
        self.overtime_hours_spin.setSuffix(" ساعة")
        self.overtime_hours_spin.valueChanged.connect(self.calculate_total)
        advanced_layout.addRow("الساعات الإضافية:", self.overtime_hours_spin)
        
        # معامل الساعات الإضافية
        self.overtime_multiplier_spin = QDoubleSpinBox()
        self.overtime_multiplier_spin.setRange(1.0, 3.0)
        self.overtime_multiplier_spin.setDecimals(1)
        self.overtime_multiplier_spin.setValue(1.5)
        self.overtime_multiplier_spin.valueChanged.connect(self.calculate_total)
        advanced_layout.addRow("معامل الساعات الإضافية:", self.overtime_multiplier_spin)
        
        # ملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")
        advanced_layout.addRow("ملاحظات:", self.notes_edit)
        
        tabs.addTab(advanced_tab, "الإعدادات المتقدمة")
        
        layout.addWidget(tabs)
        
        # عرض التكلفة الإجمالية
        self.total_label = QLabel("0.00 د.ل")
        self.total_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                font-weight: bold;
                color: {ModernStyleManager.COLORS['primary']};
                padding: 8px;
                background-color: {ModernStyleManager.COLORS['card']};
                border: 2px solid {ModernStyleManager.COLORS['primary']};
                border-radius: 6px;
                text-align: center;
            }}
        """)
        
        total_layout = QHBoxLayout()
        total_layout.addWidget(QLabel("التكلفة الإجمالية:"))
        total_layout.addWidget(self.total_label)
        layout.addLayout(total_layout)
        
        # أزرار التحكم
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        
        ok_button = buttons.button(QDialogButtonBox.StandardButton.Ok)
        ok_button.setText("حفظ")
        
        cancel_button = buttons.button(QDialogButtonBox.StandardButton.Cancel)
        cancel_button.setText("إلغاء")
        
        layout.addWidget(buttons)
        
        # حساب التكلفة الأولية
        self.calculate_total()
    
    def calculate_total(self):
        """حساب التكلفة الإجمالية"""
        # إنشاء كائن مؤقت للحساب
        temp_labor = EnhancedLaborCost(
            id="",
            task_name="temp",
            skill_level=self.skill_level_combo.currentData() or SkillLevel.INTERMEDIATE,
            hours=self.hours_spin.value(),
            hourly_rate=self.hourly_rate_spin.value(),
            difficulty_multiplier=self.difficulty_spin.value(),
            overtime_hours=self.overtime_hours_spin.value(),
            overtime_rate_multiplier=self.overtime_multiplier_spin.value()
        )
        
        total = temp_labor.calculate_total_cost()
        self.total_label.setText(CurrencyFormatter.format_currency(total))
    
    def load_labor_data(self):
        """تحميل بيانات العمالة للتعديل"""
        if self.labor:
            self.task_name_edit.setText(self.labor.task_name)
            
            # تعيين مستوى المهارة
            for i in range(self.skill_level_combo.count()):
                if self.skill_level_combo.itemData(i) == self.labor.skill_level:
                    self.skill_level_combo.setCurrentIndex(i)
                    break
            
            self.hours_spin.setValue(self.labor.hours)
            self.hourly_rate_spin.setValue(self.labor.hourly_rate)
            self.difficulty_spin.setValue(self.labor.difficulty_multiplier)
            self.overtime_hours_spin.setValue(self.labor.overtime_hours)
            self.overtime_multiplier_spin.setValue(self.labor.overtime_rate_multiplier)
            self.notes_edit.setPlainText(self.labor.notes)
            
            self.calculate_total()
    
    def get_labor_data(self) -> EnhancedLaborCost:
        """الحصول على بيانات العمالة من النموذج"""
        labor_id = self.labor.id if self.labor else ""
        
        return EnhancedLaborCost(
            id=labor_id,
            task_name=self.task_name_edit.text().strip(),
            skill_level=self.skill_level_combo.currentData(),
            hours=self.hours_spin.value(),
            hourly_rate=self.hourly_rate_spin.value(),
            difficulty_multiplier=self.difficulty_spin.value(),
            overtime_hours=self.overtime_hours_spin.value(),
            overtime_rate_multiplier=self.overtime_multiplier_spin.value(),
            notes=self.notes_edit.toPlainText().strip()
        )
    
    def accept(self):
        """التحقق من صحة البيانات قبل الحفظ"""
        if not self.task_name_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المهمة")
            self.task_name_edit.setFocus()
            return
        
        if self.hours_spin.value() <= 0:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال عدد ساعات صحيح")
            self.hours_spin.setFocus()
            return
        
        if self.hourly_rate_spin.value() <= 0:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال أجر ساعة صحيح")
            self.hourly_rate_spin.setFocus()
            return
        
        super().accept()
