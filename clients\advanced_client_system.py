#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة العملاء المتقدم - PySide6
Advanced Client Management System - PySide6
"""

import sqlite3
import json
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QTableWidget, QTableWidgetItem, QHeaderView, QLineEdit,
    QComboBox, QTextEdit, QDateEdit, QSpinBox, QDoubleSpinBox,
    QGroupBox, QFormLayout, QDialog, QDialogButtonBox,
    QMessageBox, QFileDialog, QTabWidget, QSplitter,
    QTreeWidget, QTreeWidgetItem, QProgressBar, QCheckBox,
    QAbstractItemView
)
from PySide6.QtCore import Qt, QDate, Signal, QTimer, QThread
from PySide6.QtGui import QFont, QColor, QPixmap, QIcon

from ui.modern_styles import ModernStyleManager
from ui.icons_manager import IconsManager


@dataclass
class Client:
    """بيانات العميل المحسنة"""
    id: str
    name: str
    company: str
    phone: str
    email: str
    address: str
    city: str
    country: str
    postal_code: str
    tax_number: str
    payment_terms: int  # أيام الدفع
    discount_rate: float  # نسبة الخصم
    credit_limit: float  # حد الائتمان (د.ل)
    notes: str
    created_date: str
    last_contact: str
    total_orders: int = 0
    total_revenue: float = 0.0  # إجمالي الإيرادات (د.ل)
    status: str = "نشط"  # نشط، معلق، محظور
    priority: str = "عادي"  # عالي، عادي، منخفض
    source: str = "مباشر"  # مصدر العميل
    assigned_to: str = ""  # المسؤول عن العميل

    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.created_date:
            self.created_date = datetime.now().isoformat()


@dataclass
class ClientContact:
    """سجل التواصل مع العميل"""
    id: str
    client_id: str
    contact_type: str  # مكالمة، إيميل، اجتماع، زيارة، واتساب
    subject: str
    description: str
    contact_date: str
    follow_up_date: str
    status: str  # مكتمل، معلق، ملغي
    created_by: str
    attachments: List[str] = None

    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if self.attachments is None:
            self.attachments = []


@dataclass
class ClientProject:
    """مشروع العميل"""
    id: str
    client_id: str
    project_name: str
    description: str
    start_date: str
    end_date: str
    budget: float  # الميزانية (د.ل)
    status: str  # مقترح، قيد التنفيذ، مكتمل، ملغي
    progress: int  # نسبة الإنجاز
    assigned_team: List[str] = None

    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if self.assigned_team is None:
            self.assigned_team = []


class ClientDatabaseManager:
    """مدير قاعدة بيانات العملاء المحسن"""

    def __init__(self, db_path: str = "clients_advanced.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """تهيئة قاعدة البيانات المحسنة"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # جدول العملاء المحسن
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS clients (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                company TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                city TEXT,
                country TEXT,
                postal_code TEXT,
                tax_number TEXT,
                payment_terms INTEGER DEFAULT 30,
                discount_rate REAL DEFAULT 0.0,
                credit_limit REAL DEFAULT 0.0,
                notes TEXT,
                created_date TEXT,
                last_contact TEXT,
                total_orders INTEGER DEFAULT 0,
                total_revenue REAL DEFAULT 0.0,
                status TEXT DEFAULT 'نشط',
                priority TEXT DEFAULT 'عادي',
                source TEXT DEFAULT 'مباشر',
                assigned_to TEXT
            )
        ''')

        # جدول التواصل مع العملاء المحسن
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS client_contacts (
                id TEXT PRIMARY KEY,
                client_id TEXT,
                contact_type TEXT,
                subject TEXT,
                description TEXT,
                contact_date TEXT,
                follow_up_date TEXT,
                status TEXT,
                created_by TEXT,
                attachments TEXT,
                FOREIGN KEY (client_id) REFERENCES clients (id)
            )
        ''')

        # جدول مشاريع العملاء
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS client_projects (
                id TEXT PRIMARY KEY,
                client_id TEXT,
                project_name TEXT,
                description TEXT,
                start_date TEXT,
                end_date TEXT,
                budget REAL,
                status TEXT,
                progress INTEGER DEFAULT 0,
                assigned_team TEXT,
                FOREIGN KEY (client_id) REFERENCES clients (id)
            )
        ''')

        # جدول المهام والمتابعات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS client_tasks (
                id TEXT PRIMARY KEY,
                client_id TEXT,
                task_title TEXT,
                task_description TEXT,
                due_date TEXT,
                priority TEXT,
                status TEXT,
                assigned_to TEXT,
                created_date TEXT,
                FOREIGN KEY (client_id) REFERENCES clients (id)
            )
        ''')

        # جدول الملفات والمرفقات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS client_attachments (
                id TEXT PRIMARY KEY,
                client_id TEXT,
                file_name TEXT,
                file_path TEXT,
                file_type TEXT,
                upload_date TEXT,
                uploaded_by TEXT,
                FOREIGN KEY (client_id) REFERENCES clients (id)
            )
        ''')

        conn.commit()
        conn.close()

    def add_client(self, client: Client) -> bool:
        """إضافة عميل جديد"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO clients
                (id, name, company, phone, email, address, city, country, postal_code,
                 tax_number, payment_terms, discount_rate, credit_limit,
                 notes, created_date, last_contact, total_orders, total_revenue,
                 status, priority, source, assigned_to)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                client.id, client.name, client.company, client.phone, client.email,
                client.address, client.city, client.country, client.postal_code,
                client.tax_number, client.payment_terms, client.discount_rate,
                client.credit_limit, client.notes, client.created_date,
                client.last_contact, client.total_orders, client.total_revenue,
                client.status, client.priority, client.source, client.assigned_to
            ))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"خطأ في إضافة العميل: {e}")
            return False

    def get_all_clients(self) -> List[Dict[str, Any]]:
        """الحصول على جميع العملاء"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM clients ORDER BY name')
            clients_data = cursor.fetchall()

            clients = []
            columns = [desc[0] for desc in cursor.description]

            for client_data in clients_data:
                client_dict = dict(zip(columns, client_data))
                clients.append(client_dict)

            conn.close()
            return clients

        except Exception as e:
            print(f"خطأ في تحميل العملاء: {e}")
            return []

    def get_client_by_id(self, client_id: str) -> Dict[str, Any]:
        """الحصول على عميل بواسطة المعرف"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM clients WHERE id = ?', (client_id,))
            client_data = cursor.fetchone()

            if client_data:
                columns = [desc[0] for desc in cursor.description]
                client_dict = dict(zip(columns, client_data))
                conn.close()
                return client_dict

            conn.close()
            return {}

        except Exception as e:
            print(f"خطأ في تحميل العميل: {e}")
            return {}

    def update_client(self, client: Client) -> bool:
        """تحديث بيانات العميل"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE clients SET
                name = ?, company = ?, phone = ?, email = ?, address = ?,
                city = ?, country = ?, postal_code = ?, tax_number = ?,
                payment_terms = ?, discount_rate = ?, credit_limit = ?,
                notes = ?, status = ?, priority = ?, source = ?, assigned_to = ?
                WHERE id = ?
            ''', (
                client.name, client.company, client.phone, client.email, client.address,
                client.city, client.country, client.postal_code, client.tax_number,
                client.payment_terms, client.discount_rate, client.credit_limit,
                client.notes, client.status, client.priority, client.source,
                client.assigned_to, client.id
            ))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"خطأ في تحديث العميل: {e}")
            return False

    def delete_client(self, client_id: str) -> bool:
        """حذف عميل"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # حذف سجلات التواصل المرتبطة
            cursor.execute('DELETE FROM client_contacts WHERE client_id = ?', (client_id,))

            # حذف المشاريع المرتبطة
            cursor.execute('DELETE FROM client_projects WHERE client_id = ?', (client_id,))

            # حذف المهام المرتبطة
            cursor.execute('DELETE FROM client_tasks WHERE client_id = ?', (client_id,))

            # حذف المرفقات المرتبطة
            cursor.execute('DELETE FROM client_attachments WHERE client_id = ?', (client_id,))

            # حذف العميل
            cursor.execute('DELETE FROM clients WHERE id = ?', (client_id,))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"خطأ في حذف العميل: {e}")
            return False

    def search_clients(self, search_term: str, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """البحث المتقدم في العملاء"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            query = "SELECT * FROM clients WHERE 1=1"
            params = []

            # البحث النصي
            if search_term:
                search_pattern = f"%{search_term}%"
                query += " AND (name LIKE ? OR company LIKE ? OR phone LIKE ? OR email LIKE ?)"
                params.extend([search_pattern] * 4)

            # تطبيق الفلاتر
            if filters:
                if filters.get('status'):
                    query += " AND status = ?"
                    params.append(filters['status'])

                if filters.get('priority'):
                    query += " AND priority = ?"
                    params.append(filters['priority'])

                if filters.get('city'):
                    query += " AND city = ?"
                    params.append(filters['city'])

            query += " ORDER BY name"

            cursor.execute(query, params)
            clients_data = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]

            clients = []
            for client_data in clients_data:
                client_dict = dict(zip(columns, client_data))
                clients.append(client_dict)

            conn.close()
            return clients

        except Exception as e:
            print(f"خطأ في البحث: {e}")
            return []

    def add_client_contact(self, contact: ClientContact) -> bool:
        """إضافة سجل تواصل جديد"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO client_contacts
                (id, client_id, contact_type, subject, description, contact_date,
                 follow_up_date, status, created_by, attachments)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                contact.id, contact.client_id, contact.contact_type, contact.subject,
                contact.description, contact.contact_date, contact.follow_up_date,
                contact.status, contact.created_by, json.dumps(contact.attachments)
            ))

            # تحديث تاريخ آخر تواصل للعميل
            cursor.execute('''
                UPDATE clients SET last_contact = ? WHERE id = ?
            ''', (contact.contact_date, contact.client_id))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"خطأ في إضافة سجل التواصل: {e}")
            return False

    def get_client_contacts(self, client_id: str) -> List[Dict[str, Any]]:
        """الحصول على سجلات التواصل للعميل"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM client_contacts
                WHERE client_id = ?
                ORDER BY contact_date DESC
            ''', (client_id,))

            contacts_data = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]

            contacts = []
            for contact_data in contacts_data:
                contact_dict = dict(zip(columns, contact_data))
                # تحويل JSON للمرفقات
                if contact_dict.get('attachments'):
                    contact_dict['attachments'] = json.loads(contact_dict['attachments'])
                contacts.append(contact_dict)

            conn.close()
            return contacts

        except Exception as e:
            print(f"خطأ في تحميل سجلات التواصل: {e}")
            return []

    def update_client_stats(self, client_id: str, order_amount: float = 0):
        """تحديث إحصائيات العميل"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE clients
                SET total_orders = total_orders + 1,
                    total_revenue = total_revenue + ?,
                    last_contact = ?
                WHERE id = ?
            ''', (order_amount, datetime.now().isoformat(), client_id))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"خطأ في تحديث إحصائيات العميل: {e}")
            return False

    def get_client_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات العملاء"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            stats = {}

            # إجمالي العملاء
            cursor.execute("SELECT COUNT(*) FROM clients")
            stats['total_clients'] = cursor.fetchone()[0]

            # العملاء النشطون
            cursor.execute("SELECT COUNT(*) FROM clients WHERE status = 'نشط'")
            stats['active_clients'] = cursor.fetchone()[0]

            # إجمالي الإيرادات
            cursor.execute("SELECT SUM(total_revenue) FROM clients")
            result = cursor.fetchone()[0]
            stats['total_revenue'] = result if result else 0.0

            # متوسط قيمة العميل
            if stats['total_clients'] > 0:
                stats['avg_client_value'] = stats['total_revenue'] / stats['total_clients']
            else:
                stats['avg_client_value'] = 0.0

            # العملاء حسب الأولوية
            cursor.execute("SELECT priority, COUNT(*) FROM clients GROUP BY priority")
            priority_data = cursor.fetchall()
            stats['clients_by_priority'] = dict(priority_data)

            # العملاء حسب المدينة
            cursor.execute("SELECT city, COUNT(*) FROM clients GROUP BY city ORDER BY COUNT(*) DESC LIMIT 5")
            city_data = cursor.fetchall()
            stats['top_cities'] = dict(city_data)

            conn.close()
            return stats

        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات: {e}")
            return {}


class AdvancedClientManagerWidget(QWidget):
    """واجهة إدارة العملاء المتقدمة"""

    client_selected = Signal(str)  # إشارة اختيار عميل
    client_updated = Signal()  # إشارة تحديث العميل

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = ClientDatabaseManager()
        self.current_client_id = None
        self.init_ui()
        self.load_clients()
        self.setup_auto_refresh()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)

        # شريط الأدوات العلوي
        self.create_toolbar(layout)

        # المحتوى الرئيسي
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # قائمة العملاء
        self.create_clients_list(main_splitter)

        # تفاصيل العميل
        self.create_client_details(main_splitter)

        # تعيين النسب
        main_splitter.setSizes([400, 600])

        layout.addWidget(main_splitter)

        # شريط الحالة
        self.create_status_bar(layout)

    def create_toolbar(self, parent_layout):
        """إنشاء شريط الأدوات"""
        toolbar_widget = QWidget()
        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(0, 0, 0, 0)

        # البحث المتقدم
        search_group = QGroupBox("البحث والفلترة")
        search_layout = QHBoxLayout(search_group)

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث بالاسم، الشركة، الهاتف، أو الإيميل...")
        self.search_edit.textChanged.connect(self.filter_clients)

        self.status_filter = QComboBox()
        self.status_filter.addItems(["الكل", "نشط", "معلق", "محظور"])
        self.status_filter.currentTextChanged.connect(self.apply_filters)

        self.priority_filter = QComboBox()
        self.priority_filter.addItems(["الكل", "عالي", "عادي", "منخفض"])
        self.priority_filter.currentTextChanged.connect(self.apply_filters)

        search_layout.addWidget(QLabel("البحث:"))
        search_layout.addWidget(self.search_edit)
        search_layout.addWidget(QLabel("الحالة:"))
        search_layout.addWidget(self.status_filter)
        search_layout.addWidget(QLabel("الأولوية:"))
        search_layout.addWidget(self.priority_filter)

        # أزرار الإدارة
        buttons_group = QGroupBox("الإدارة")
        buttons_layout = QHBoxLayout(buttons_group)

        add_client_btn = QPushButton("إضافة عميل")
        add_client_btn.setIcon(IconsManager.get_standard_icon('add'))
        add_client_btn.clicked.connect(self.add_new_client)

        edit_client_btn = QPushButton("تعديل")
        edit_client_btn.setIcon(IconsManager.get_standard_icon('edit'))
        edit_client_btn.clicked.connect(self.edit_selected_client)

        delete_client_btn = QPushButton("حذف")
        delete_client_btn.setIcon(IconsManager.get_standard_icon('delete'))
        delete_client_btn.clicked.connect(self.delete_selected_client)

        export_btn = QPushButton("تصدير")
        export_btn.setIcon(IconsManager.get_standard_icon('export'))
        export_btn.clicked.connect(self.export_clients)

        buttons_layout.addWidget(add_client_btn)
        buttons_layout.addWidget(edit_client_btn)
        buttons_layout.addWidget(delete_client_btn)
        buttons_layout.addWidget(export_btn)

        toolbar_layout.addWidget(search_group)
        toolbar_layout.addWidget(buttons_group)

        parent_layout.addWidget(toolbar_widget)
    def filter_clients(self):
        """فلترة العملاء"""
        search_text = self.search_edit.text().lower()

        # تطبيق الفلترة على جدول العملاء
        for row in range(self.clients_table.rowCount()):
            show_row = True

            if search_text:
                # البحث في الاسم والشركة والبريد الإلكتروني
                name = self.clients_table.item(row, 0).text().lower() if self.clients_table.item(row, 0) else ""
                company = self.clients_table.item(row, 1).text().lower() if self.clients_table.item(row, 1) else ""
                email = self.clients_table.item(row, 3).text().lower() if self.clients_table.item(row, 3) else ""

                if not (search_text in name or search_text in company or search_text in email):
                    show_row = False

            self.clients_table.setRowHidden(row, not show_row)

    def apply_filters(self):
        """تطبيق الفلاتر"""
        self.filter_clients()

    def refresh_clients_list(self):
        """تحديث قائمة العملاء"""
        try:
            clients = self.db_manager.get_all_clients()
            self.populate_clients_table(clients)
            self.update_statistics()
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحديث قائمة العملاء: {e}")

    def export_clients_data(self):
        """تصدير بيانات العملاء"""
        try:
            from PySide6.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "تصدير بيانات العملاء",
                "clients_export.csv",
                "CSV Files (*.csv);;Excel Files (*.xlsx)"
            )

            if file_path:
                clients = self.db_manager.get_all_clients()

                if file_path.endswith('.csv'):
                    # تصدير CSV
                    import csv
                    with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                        writer = csv.writer(csvfile)

                        # كتابة العناوين
                        headers = ['الاسم', 'الشركة', 'الهاتف', 'البريد الإلكتروني',
                                 'المدينة', 'إجمالي الإيرادات (د.ل)']
                        writer.writerow(headers)

                        # كتابة البيانات
                        for client in clients:
                            from ui.modern_styles import CurrencyFormatter
                            revenue = CurrencyFormatter.format_currency(client.get('total_revenue', 0))

                            row = [
                                client.get('name', ''),
                                client.get('company', ''),
                                client.get('phone', ''),
                                client.get('email', ''),
                                client.get('city', ''),
                                revenue
                            ]
                            writer.writerow(row)

                QMessageBox.information(self, "تصدير ناجح", f"تم تصدير بيانات {len(clients)} عميل إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تصدير البيانات: {e}")

    def import_clients_data(self):
        """استيراد بيانات العملاء"""
        QMessageBox.information(self, "استيراد البيانات",
                               "سيتم تنفيذ استيراد بيانات العملاء قريباً\n"
                               "الميزات القادمة:\n"
                               "• استيراد من ملفات CSV و Excel\n"
                               "• التحقق من صحة البيانات\n"
                               "• دمج البيانات المكررة")

    def generate_client_report(self):
        """إنشاء تقرير العميل المختار"""
        if not self.current_client_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل لإنشاء التقرير")
            return

        try:
            client = self.db_manager.get_client_by_id(self.current_client_id)
            if client:
                from ui.modern_styles import CurrencyFormatter

                report_text = f"""
📊 تقرير العميل

👤 الاسم: {client.get('name', 'غير محدد')}
🏢 الشركة: {client.get('company', 'غير محدد')}
📞 الهاتف: {client.get('phone', 'غير محدد')}
📧 البريد: {client.get('email', 'غير محدد')}
🏙️ المدينة: {client.get('city', 'غير محدد')}

💰 إجمالي الإيرادات: {CurrencyFormatter.format_currency(client.get('total_revenue', 0))}
📈 إجمالي الطلبات: {client.get('total_orders', 0)}
💳 حد الائتمان: {CurrencyFormatter.format_currency(client.get('credit_limit', 0))}
📅 تاريخ الإنشاء: {client.get('created_date', 'غير محدد')}

📝 ملاحظات: {client.get('notes', 'لا توجد ملاحظات')}
                """

                QMessageBox.information(self, f"تقرير العميل - {client.get('name', 'غير محدد')}", report_text)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في إنشاء تقرير العميل: {e}")

    def add_new_client(self):
        """إضافة عميل جديد"""
        dialog = ClientEditDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            client_data = dialog.get_client_data()

            # إنشاء كائن العميل
            client = Client(
                id="",  # سيتم إنشاؤه تلقائياً
                name=client_data['name'],
                company=client_data['company'],
                phone=client_data['phone'],
                email=client_data['email'],
                address=client_data['address'],
                city=client_data['city'],
                country=client_data['country'],
                postal_code=client_data['postal_code'],
                tax_number=client_data['tax_number'],
                payment_terms=client_data['payment_terms'],
                discount_rate=client_data['discount_rate'],
                credit_limit=client_data['credit_limit'],
                notes=client_data['notes'],
                created_date="",  # سيتم إنشاؤه تلقائياً
                last_contact="",
                status=client_data['status'],
                priority=client_data['priority'],
                source=client_data['source'],
                assigned_to=client_data['assigned_to']
            )

            if self.db_manager.add_client(client):
                QMessageBox.information(self, "نجح", "تم إضافة العميل بنجاح")
                self.refresh_clients_list()
                self.client_updated.emit()
            else:
                QMessageBox.warning(self, "خطأ", "فشل في إضافة العميل")

    def edit_selected_client(self):
        """تعديل العميل المختار"""
        if not self.current_client_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل للتعديل")
            return

        # تحميل بيانات العميل الحالية
        client_data = self.db_manager.get_client_by_id(self.current_client_id)
        if not client_data:
            QMessageBox.warning(self, "خطأ", "لم يتم العثور على بيانات العميل")
            return

        dialog = ClientEditDialog(self, client_data)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            updated_data = dialog.get_client_data()

            # إنشاء كائن العميل المحدث
            client = Client(
                id=self.current_client_id,
                name=updated_data['name'],
                company=updated_data['company'],
                phone=updated_data['phone'],
                email=updated_data['email'],
                address=updated_data['address'],
                city=updated_data['city'],
                country=updated_data['country'],
                postal_code=updated_data['postal_code'],
                tax_number=updated_data['tax_number'],
                payment_terms=updated_data['payment_terms'],
                discount_rate=updated_data['discount_rate'],
                credit_limit=updated_data['credit_limit'],
                notes=updated_data['notes'],
                created_date=client_data['created_date'],  # الاحتفاظ بالتاريخ الأصلي
                last_contact=client_data['last_contact'],
                total_orders=client_data['total_orders'],
                total_revenue=client_data['total_revenue'],
                status=updated_data['status'],
                priority=updated_data['priority'],
                source=updated_data['source'],
                assigned_to=updated_data['assigned_to']
            )

            if self.db_manager.update_client(client):
                QMessageBox.information(self, "نجح", "تم تحديث بيانات العميل بنجاح")
                self.refresh_clients_list()
                self.load_client_details(self.current_client_id)
                self.client_updated.emit()
            else:
                QMessageBox.warning(self, "خطأ", "فشل في تحديث بيانات العميل")

    def delete_selected_client(self):
        """حذف العميل المختار"""
        if not self.current_client_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل للحذف")
            return

        # الحصول على اسم العميل للتأكيد
        client_data = self.db_manager.get_client_by_id(self.current_client_id)
        client_name = client_data.get('name', 'غير معروف') if client_data else 'غير معروف'

        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف العميل '{client_name}'؟\n\n"
            "⚠️ تحذير: سيتم حذف جميع البيانات المرتبطة بهذا العميل:\n"
            "• سجلات التواصل\n"
            "• المشاريع\n"
            "• المهام\n"
            "• المرفقات\n\n"
            "هذا الإجراء لا يمكن التراجع عنه!",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            if self.db_manager.delete_client(self.current_client_id):
                QMessageBox.information(self, "تم الحذف", f"تم حذف العميل '{client_name}' بنجاح")
                self.current_client_id = None
                self.refresh_clients_list()
                self.clear_client_details()
                self.client_updated.emit()
            else:
                QMessageBox.warning(self, "خطأ", "فشل في حذف العميل")

    def view_client_projects(self):
        """عرض مشاريع العميل المختار"""
        if not self.current_client_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عميل لعرض مشاريعه")
            return

        client_data = self.db_manager.get_client_by_id(self.current_client_id)
        client_name = client_data.get('name', 'غير معروف') if client_data else 'غير معروف'

        # عرض نافذة مشاريع العميل
        projects_dialog = ClientProjectsDialog(self, self.current_client_id, client_name)
        projects_dialog.exec()

    def clear_filters(self):
        """مسح جميع الفلاتر"""
        self.search_edit.clear()
        self.status_filter.setCurrentIndex(0)
        self.priority_filter.setCurrentIndex(0)
        self.refresh_clients_list()

    def clear_client_details(self):
        """مسح تفاصيل العميل"""
        self.client_name_label.setText("-")
        self.client_company_label.setText("-")
        self.client_phone_label.setText("-")
        self.client_email_label.setText("-")
        self.client_city_label.setText("-")
        self.client_revenue_label.setText("-")

    def export_clients(self):
        """تصدير العملاء (اختصار لـ export_clients_data)"""
        self.export_clients_data()

    def create_clients_list(self, parent_splitter):
        """إنشاء قائمة العملاء"""
        clients_widget = QWidget()
        layout = QVBoxLayout(clients_widget)

        # عنوان القائمة
        title_label = QLabel("قائمة العملاء")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; margin: 10px;")

        # إنشاء جدول العملاء
        self.clients_table = QTableWidget()
        self.clients_table.setColumnCount(6)
        self.clients_table.setHorizontalHeaderLabels([
            "الاسم", "الشركة", "الهاتف", "البريد الإلكتروني", "المدينة", "الإيرادات (د.ل)"
        ])

        # تعديل عرض الأعمدة
        header = self.clients_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)

        self.clients_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.clients_table.setAlternatingRowColors(True)
        self.clients_table.setSortingEnabled(True)
        self.clients_table.itemSelectionChanged.connect(self.on_client_selected)

        layout.addWidget(title_label)
        layout.addWidget(self.clients_table)

        parent_splitter.addWidget(clients_widget)

    def on_client_selected(self):
        """عند اختيار عميل"""
        current_row = self.clients_table.currentRow()
        if current_row >= 0:
            client_id = self.clients_table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)
            if client_id:
                self.current_client_id = client_id
                self.load_client_details(client_id)
                self.client_selected.emit(client_id)

    def populate_clients_table(self, clients):
        """ملء جدول العملاء"""
        self.clients_table.setRowCount(len(clients))

        for row, client in enumerate(clients):
            self.clients_table.setItem(row, 0, QTableWidgetItem(client.get('name', '')))
            self.clients_table.setItem(row, 1, QTableWidgetItem(client.get('company', '')))
            self.clients_table.setItem(row, 2, QTableWidgetItem(client.get('phone', '')))
            self.clients_table.setItem(row, 3, QTableWidgetItem(client.get('email', '')))
            self.clients_table.setItem(row, 4, QTableWidgetItem(client.get('city', '')))

            # تنسيق العملة
            from ui.modern_styles import CurrencyFormatter
            revenue = CurrencyFormatter.format_currency(client.get('total_revenue', 0))
            self.clients_table.setItem(row, 5, QTableWidgetItem(revenue))

            # حفظ معرف العميل
            self.clients_table.item(row, 0).setData(Qt.ItemDataRole.UserRole, client.get('id'))

    def create_client_details(self, parent_splitter):
        """إنشاء لوحة تفاصيل العميل"""
        details_widget = QWidget()
        layout = QVBoxLayout(details_widget)

        # تفاصيل العميل
        details_group = QGroupBox("تفاصيل العميل")
        details_layout = QFormLayout(details_group)

        self.client_name_label = QLabel("-")
        self.client_company_label = QLabel("-")
        self.client_phone_label = QLabel("-")
        self.client_email_label = QLabel("-")
        self.client_city_label = QLabel("-")
        self.client_revenue_label = QLabel("-")

        details_layout.addRow("الاسم:", self.client_name_label)
        details_layout.addRow("الشركة:", self.client_company_label)
        details_layout.addRow("الهاتف:", self.client_phone_label)
        details_layout.addRow("البريد:", self.client_email_label)
        details_layout.addRow("المدينة:", self.client_city_label)
        details_layout.addRow("الإيرادات:", self.client_revenue_label)

        layout.addWidget(details_group)
        layout.addStretch()
        parent_splitter.addWidget(details_widget)

    def load_client_details(self, client_id: str):
        """تحميل تفاصيل العميل"""
        client = self.db_manager.get_client_by_id(client_id)
        if client:
            from ui.modern_styles import CurrencyFormatter

            self.client_name_label.setText(client.get('name', '-'))
            self.client_company_label.setText(client.get('company', '-'))
            self.client_phone_label.setText(client.get('phone', '-'))
            self.client_email_label.setText(client.get('email', '-'))
            self.client_city_label.setText(client.get('city', '-'))
            self.client_revenue_label.setText(CurrencyFormatter.format_currency(client.get('total_revenue', 0)))

    def load_clients(self):
        """تحميل العملاء"""
        clients = self.db_manager.get_all_clients()
        self.populate_clients_table(clients)
        self.update_statistics()

    def update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            stats = self.db_manager.get_client_statistics()

            from ui.modern_styles import CurrencyFormatter

            # تحديث تسميات الإحصائيات
            total_clients = stats.get('total_clients', 0)
            active_clients = stats.get('active_clients', 0)
            total_revenue = stats.get('total_revenue', 0.0)

            self.total_clients_label.setText(f"إجمالي العملاء: {total_clients}")
            self.active_clients_label.setText(f"العملاء النشطون: {active_clients}")
            self.total_revenue_label.setText(f"إجمالي الإيرادات: {CurrencyFormatter.format_currency(total_revenue)}")

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات: {e}")

    def setup_auto_refresh(self):
        """إعداد التحديث التلقائي"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_clients_list)
        self.refresh_timer.start(60000)  # تحديث كل دقيقة

    def create_status_bar(self, parent_layout):
        """إنشاء شريط الحالة"""
        status_widget = QWidget()
        status_layout = QHBoxLayout(status_widget)

        self.total_clients_label = QLabel("إجمالي العملاء: 0")
        self.active_clients_label = QLabel("العملاء النشطون: 0")
        self.total_revenue_label = QLabel("إجمالي الإيرادات: 0.00 د.ل")

        status_layout.addWidget(self.total_clients_label)
        status_layout.addWidget(self.active_clients_label)
        status_layout.addWidget(self.total_revenue_label)
        status_layout.addStretch()

        parent_layout.addWidget(status_widget)


class ClientEditDialog(QDialog):
    """حوار تحرير بيانات العميل"""

    def __init__(self, parent=None, client_data=None):
        super().__init__(parent)
        self.client_data = client_data or {}
        self.init_ui()
        self.load_data()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إضافة/تعديل عميل" if not self.client_data else "تعديل بيانات العميل")
        self.setModal(True)
        self.resize(500, 600)

        layout = QVBoxLayout(self)

        # تبويبات البيانات
        tabs = QTabWidget()

        # تبويب البيانات الأساسية
        basic_tab = QWidget()
        basic_layout = QFormLayout(basic_tab)

        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("اسم العميل الكامل")
        basic_layout.addRow("الاسم *:", self.name_edit)

        self.company_edit = QLineEdit()
        self.company_edit.setPlaceholderText("اسم الشركة")
        basic_layout.addRow("الشركة:", self.company_edit)

        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("رقم الهاتف")
        basic_layout.addRow("الهاتف *:", self.phone_edit)

        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("البريد الإلكتروني")
        basic_layout.addRow("الإيميل:", self.email_edit)

        tabs.addTab(basic_tab, "البيانات الأساسية")

        # تبويب العنوان
        address_tab = QWidget()
        address_layout = QFormLayout(address_tab)

        self.address_edit = QTextEdit()
        self.address_edit.setMaximumHeight(80)
        self.address_edit.setPlaceholderText("العنوان التفصيلي")
        address_layout.addRow("العنوان:", self.address_edit)

        self.city_combo = QComboBox()
        self.city_combo.setEditable(True)
        self.city_combo.addItems(["طرابلس", "بنغازي", "مصراتة", "الزاوية", "صبراتة", "زليتن", "أجدابيا"])
        address_layout.addRow("المدينة:", self.city_combo)

        self.country_edit = QLineEdit()
        self.country_edit.setText("ليبيا")
        address_layout.addRow("البلد:", self.country_edit)

        self.postal_code_edit = QLineEdit()
        self.postal_code_edit.setPlaceholderText("الرمز البريدي")
        address_layout.addRow("الرمز البريدي:", self.postal_code_edit)

        tabs.addTab(address_tab, "العنوان")

        # تبويب البيانات المالية
        financial_tab = QWidget()
        financial_layout = QFormLayout(financial_tab)

        self.tax_number_edit = QLineEdit()
        self.tax_number_edit.setPlaceholderText("الرقم الضريبي")
        financial_layout.addRow("الرقم الضريبي:", self.tax_number_edit)

        self.payment_terms_spin = QSpinBox()
        self.payment_terms_spin.setRange(0, 365)
        self.payment_terms_spin.setValue(30)
        self.payment_terms_spin.setSuffix(" يوم")
        financial_layout.addRow("شروط الدفع:", self.payment_terms_spin)

        self.discount_rate_spin = QDoubleSpinBox()
        self.discount_rate_spin.setRange(0.0, 100.0)
        self.discount_rate_spin.setSuffix("%")
        self.discount_rate_spin.setDecimals(2)
        financial_layout.addRow("نسبة الخصم:", self.discount_rate_spin)

        self.credit_limit_spin = QDoubleSpinBox()
        self.credit_limit_spin.setRange(0.0, *********.99)
        self.credit_limit_spin.setSuffix(" د.ل")
        self.credit_limit_spin.setDecimals(2)
        financial_layout.addRow("حد الائتمان:", self.credit_limit_spin)

        tabs.addTab(financial_tab, "البيانات المالية")

        # تبويب الإعدادات
        settings_tab = QWidget()
        settings_layout = QFormLayout(settings_tab)

        self.status_combo = QComboBox()
        self.status_combo.addItems(["نشط", "معلق", "محظور"])
        settings_layout.addRow("الحالة:", self.status_combo)

        self.priority_combo = QComboBox()
        self.priority_combo.addItems(["عالي", "عادي", "منخفض"])
        settings_layout.addRow("الأولوية:", self.priority_combo)

        self.source_combo = QComboBox()
        self.source_combo.setEditable(True)
        self.source_combo.addItems(["مباشر", "إحالة", "إعلان", "موقع إلكتروني", "معرض", "أخرى"])
        settings_layout.addRow("مصدر العميل:", self.source_combo)

        self.assigned_to_edit = QLineEdit()
        self.assigned_to_edit.setPlaceholderText("المسؤول عن العميل")
        settings_layout.addRow("المسؤول:", self.assigned_to_edit)

        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")
        settings_layout.addRow("ملاحظات:", self.notes_edit)

        tabs.addTab(settings_tab, "الإعدادات")

        layout.addWidget(tabs)

        # أزرار الحوار
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        buttons.button(QDialogButtonBox.StandardButton.Ok).setText("حفظ")
        buttons.button(QDialogButtonBox.StandardButton.Cancel).setText("إلغاء")

        layout.addWidget(buttons)

    def load_data(self):
        """تحميل البيانات الموجودة"""
        if self.client_data:
            self.name_edit.setText(self.client_data.get('name', ''))
            self.company_edit.setText(self.client_data.get('company', ''))
            self.phone_edit.setText(self.client_data.get('phone', ''))
            self.email_edit.setText(self.client_data.get('email', ''))
            self.address_edit.setPlainText(self.client_data.get('address', ''))

            city = self.client_data.get('city', '')
            if city:
                index = self.city_combo.findText(city)
                if index >= 0:
                    self.city_combo.setCurrentIndex(index)
                else:
                    self.city_combo.setCurrentText(city)

            self.country_edit.setText(self.client_data.get('country', 'ليبيا'))
            self.postal_code_edit.setText(self.client_data.get('postal_code', ''))
            self.tax_number_edit.setText(self.client_data.get('tax_number', ''))
            self.payment_terms_spin.setValue(self.client_data.get('payment_terms', 30))
            self.discount_rate_spin.setValue(self.client_data.get('discount_rate', 0.0))
            self.credit_limit_spin.setValue(self.client_data.get('credit_limit', 0.0))

            status = self.client_data.get('status', 'نشط')
            status_index = self.status_combo.findText(status)
            if status_index >= 0:
                self.status_combo.setCurrentIndex(status_index)

            priority = self.client_data.get('priority', 'عادي')
            priority_index = self.priority_combo.findText(priority)
            if priority_index >= 0:
                self.priority_combo.setCurrentIndex(priority_index)

            source = self.client_data.get('source', 'مباشر')
            source_index = self.source_combo.findText(source)
            if source_index >= 0:
                self.source_combo.setCurrentIndex(source_index)
            else:
                self.source_combo.setCurrentText(source)

            self.assigned_to_edit.setText(self.client_data.get('assigned_to', ''))
            self.notes_edit.setPlainText(self.client_data.get('notes', ''))

    def get_client_data(self):
        """الحصول على بيانات العميل"""
        return {
            'name': self.name_edit.text().strip(),
            'company': self.company_edit.text().strip(),
            'phone': self.phone_edit.text().strip(),
            'email': self.email_edit.text().strip(),
            'address': self.address_edit.toPlainText().strip(),
            'city': self.city_combo.currentText().strip(),
            'country': self.country_edit.text().strip(),
            'postal_code': self.postal_code_edit.text().strip(),
            'tax_number': self.tax_number_edit.text().strip(),
            'payment_terms': self.payment_terms_spin.value(),
            'discount_rate': self.discount_rate_spin.value(),
            'credit_limit': self.credit_limit_spin.value(),
            'status': self.status_combo.currentText(),
            'priority': self.priority_combo.currentText(),
            'source': self.source_combo.currentText().strip(),
            'assigned_to': self.assigned_to_edit.text().strip(),
            'notes': self.notes_edit.toPlainText().strip()
        }

    def accept(self):
        """التحقق من صحة البيانات قبل الحفظ"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم العميل")
            self.name_edit.setFocus()
            return

        if not self.phone_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال رقم الهاتف")
            self.phone_edit.setFocus()
            return

        super().accept()


class ClientProjectsDialog(QDialog):
    """حوار عرض مشاريع العميل"""

    def __init__(self, parent=None, client_id=None, client_name=""):
        super().__init__(parent)
        self.client_id = client_id
        self.client_name = client_name
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle(f"مشاريع العميل - {self.client_name}")
        self.setModal(True)
        self.resize(800, 600)

        layout = QVBoxLayout(self)

        # عنوان
        title_label = QLabel(f"مشاريع العميل: {self.client_name}")
        title_label.setStyleSheet("font-weight: bold; font-size: 16px; margin: 10px;")
        layout.addWidget(title_label)

        # جدول المشاريع
        self.projects_table = QTableWidget()
        self.projects_table.setColumnCount(6)
        self.projects_table.setHorizontalHeaderLabels([
            "اسم المشروع", "الوصف", "تاريخ البداية", "تاريخ النهاية", "الميزانية (د.ل)", "الحالة"
        ])

        # تحسين عرض الجدول
        header = self.projects_table.horizontalHeader()
        header.setStretchLastSection(True)
        self.projects_table.setAlternatingRowColors(True)

        layout.addWidget(self.projects_table)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        add_project_btn = QPushButton("إضافة مشروع")
        add_project_btn.clicked.connect(self.add_project)

        edit_project_btn = QPushButton("تعديل مشروع")
        edit_project_btn.clicked.connect(self.edit_project)

        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(self.close)

        buttons_layout.addWidget(add_project_btn)
        buttons_layout.addWidget(edit_project_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(close_btn)

        layout.addWidget(QWidget())  # spacer
        layout.addLayout(buttons_layout)

        # تحميل المشاريع
        self.load_projects()

    def load_projects(self):
        """تحميل مشاريع العميل"""
        # مؤقتاً - عرض رسالة
        self.projects_table.setRowCount(1)
        self.projects_table.setItem(0, 0, QTableWidgetItem("لا توجد مشاريع حالياً"))
        self.projects_table.setItem(0, 1, QTableWidgetItem("سيتم إضافة نظام إدارة المشاريع قريباً"))
        self.projects_table.setItem(0, 2, QTableWidgetItem("-"))
        self.projects_table.setItem(0, 3, QTableWidgetItem("-"))
        self.projects_table.setItem(0, 4, QTableWidgetItem("-"))
        self.projects_table.setItem(0, 5, QTableWidgetItem("-"))

    def add_project(self):
        """إضافة مشروع جديد"""
        QMessageBox.information(self, "إضافة مشروع", "سيتم تنفيذ إضافة المشاريع قريباً")

    def edit_project(self):
        """تعديل مشروع"""
        QMessageBox.information(self, "تعديل مشروع", "سيتم تنفيذ تعديل المشاريع قريباً")
