#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة العملاء المكتمل لتطبيق تصميم الأثاث
Complete Client Management System for Furniture Design Application
"""

import os
import json
import sqlite3
from datetime import datetime, date
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QLineEdit, QTextEdit, QComboBox, QDateEdit, QSpinBox,
    QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox,
    QDialog, QFormLayout, QDialogButtonBox, QGroupBox,
    QCheckBox, QDoubleSpinBox, QTabWidget, QSplitter,
    QTreeWidget, QTreeWidgetItem, QProgressBar
)
from PyQt6.Qt<PERSON>ore import Qt, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, pyqt<PERSON>ign<PERSON>, Q<PERSON>imer
from PyQt6.QtGui import QFont, QColor, QPixmap, QPainter

from ui.modern_styles import ModernStyleManager
from ui.icons_manager import IconsManager


@dataclass
class Client:
    """بيانات العميل"""
    id: int
    name: str
    company: str
    phone: str
    email: str
    address: str
    city: str
    country: str
    client_type: str  # individual, company, contractor
    credit_limit: float
    current_balance: float
    discount_rate: float
    payment_terms: int  # أيام الدفع
    tax_number: str
    notes: str
    status: str  # active, inactive, blocked
    created_date: str
    last_contact: str
    total_orders: int = 0
    total_revenue: float = 0.0

    def __post_init__(self):
        if not self.created_date:
            self.created_date = datetime.now().isoformat()


@dataclass
class ClientContact:
    """جهة اتصال العميل"""
    id: int
    client_id: int
    name: str
    position: str
    phone: str
    email: str
    is_primary: bool
    notes: str


@dataclass
class ClientOrder:
    """طلب العميل"""
    id: int
    client_id: int
    order_number: str
    order_date: str
    delivery_date: str
    status: str  # pending, confirmed, in_production, completed, cancelled
    total_amount: float
    paid_amount: float
    items_count: int
    notes: str


class ClientDialog(QDialog):
    """نافذة إضافة/تعديل العميل"""

    def __init__(self, client: Optional[Client] = None, parent=None):
        super().__init__(parent)
        self.client = client
        self.is_edit_mode = client is not None
        self.init_ui()
        if self.client:
            self.load_client_data()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("تعديل العميل" if self.is_edit_mode else "إضافة عميل جديد")
        self.setModal(True)
        self.resize(600, 700)

        layout = QVBoxLayout(self)

        # تبويبات البيانات
        tabs = QTabWidget()

        # تبويب البيانات الأساسية
        basic_tab = self.create_basic_info_tab()
        tabs.addTab(basic_tab, "البيانات الأساسية")

        # تبويب بيانات الاتصال
        contact_tab = self.create_contact_info_tab()
        tabs.addTab(contact_tab, "بيانات الاتصال")

        # تبويب البيانات المالية
        financial_tab = self.create_financial_info_tab()
        tabs.addTab(financial_tab, "البيانات المالية")

        # تبويب الملاحظات
        notes_tab = self.create_notes_tab()
        tabs.addTab(notes_tab, "الملاحظات")

        layout.addWidget(tabs)

        # أزرار التحكم
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)

        # تخصيص الأزرار
        ok_button = buttons.button(QDialogButtonBox.StandardButton.Ok)
        ok_button.setText("حفظ")
        ok_button.setIcon(IconsManager.get_standard_icon('save'))

        cancel_button = buttons.button(QDialogButtonBox.StandardButton.Cancel)
        cancel_button.setText("إلغاء")
        cancel_button.setIcon(IconsManager.get_standard_icon('close'))

        layout.addWidget(buttons)

    def create_basic_info_tab(self):
        """إنشاء تبويب البيانات الأساسية"""
        tab = QWidget()
        layout = QFormLayout(tab)
        layout.setSpacing(ModernStyleManager.SPACING['md'])

        # اسم العميل
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("أدخل اسم العميل")
        layout.addRow("اسم العميل *:", self.name_edit)

        # اسم الشركة
        self.company_edit = QLineEdit()
        self.company_edit.setPlaceholderText("اسم الشركة (اختياري)")
        layout.addRow("اسم الشركة:", self.company_edit)

        # نوع العميل
        self.client_type_combo = QComboBox()
        self.client_type_combo.addItems(["فرد", "شركة", "مقاول"])
        layout.addRow("نوع العميل:", self.client_type_combo)

        # الرقم الضريبي
        self.tax_number_edit = QLineEdit()
        self.tax_number_edit.setPlaceholderText("الرقم الضريبي")
        layout.addRow("الرقم الضريبي:", self.tax_number_edit)

        # الحالة
        self.status_combo = QComboBox()
        self.status_combo.addItems(["نشط", "غير نشط", "محظور"])
        layout.addRow("الحالة:", self.status_combo)

        return tab

    def create_contact_info_tab(self):
        """إنشاء تبويب بيانات الاتصال"""
        tab = QWidget()
        layout = QFormLayout(tab)
        layout.setSpacing(ModernStyleManager.SPACING['md'])

        # رقم الهاتف
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("+966 50 123 4567")
        layout.addRow("رقم الهاتف *:", self.phone_edit)

        # البريد الإلكتروني
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("<EMAIL>")
        layout.addRow("البريد الإلكتروني:", self.email_edit)

        # العنوان
        self.address_edit = QTextEdit()
        self.address_edit.setMaximumHeight(80)
        self.address_edit.setPlaceholderText("العنوان التفصيلي")
        layout.addRow("العنوان:", self.address_edit)

        # المدينة
        self.city_edit = QLineEdit()
        self.city_edit.setPlaceholderText("الرياض")
        layout.addRow("المدينة:", self.city_edit)

        # الدولة
        self.country_combo = QComboBox()
        self.country_combo.addItems([
            "السعودية", "الإمارات", "الكويت", "قطر", "البحرين", "عمان", "الأردن", "مصر"
        ])
        layout.addRow("الدولة:", self.country_combo)

        return tab

    def create_financial_info_tab(self):
        """إنشاء تبويب البيانات المالية"""
        tab = QWidget()
        layout = QFormLayout(tab)
        layout.setSpacing(ModernStyleManager.SPACING['md'])

        # الحد الائتماني
        self.credit_limit_spin = QDoubleSpinBox()
        self.credit_limit_spin.setRange(0, 999999999)
        self.credit_limit_spin.setSuffix(" ريال")
        self.credit_limit_spin.setValue(50000)
        layout.addRow("الحد الائتماني:", self.credit_limit_spin)

        # الرصيد الحالي
        self.current_balance_spin = QDoubleSpinBox()
        self.current_balance_spin.setRange(-999999999, 999999999)
        self.current_balance_spin.setSuffix(" ريال")
        layout.addRow("الرصيد الحالي:", self.current_balance_spin)

        # نسبة الخصم
        self.discount_rate_spin = QDoubleSpinBox()
        self.discount_rate_spin.setRange(0, 100)
        self.discount_rate_spin.setSuffix(" %")
        self.discount_rate_spin.setValue(0)
        layout.addRow("نسبة الخصم:", self.discount_rate_spin)

        # شروط الدفع
        self.payment_terms_spin = QSpinBox()
        self.payment_terms_spin.setRange(0, 365)
        self.payment_terms_spin.setSuffix(" يوم")
        self.payment_terms_spin.setValue(30)
        layout.addRow("شروط الدفع:", self.payment_terms_spin)

        return tab

    def create_notes_tab(self):
        """إنشاء تبويب الملاحظات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("أدخل أي ملاحظات إضافية حول العميل...")
        layout.addWidget(self.notes_edit)

        return tab

    def load_client_data(self):
        """تحميل بيانات العميل للتعديل"""
        if not self.client:
            return

        self.name_edit.setText(self.client.name)
        self.company_edit.setText(self.client.company)
        self.phone_edit.setText(self.client.phone)
        self.email_edit.setText(self.client.email)
        self.address_edit.setPlainText(self.client.address)
        self.city_edit.setText(self.client.city)
        self.tax_number_edit.setText(self.client.tax_number)
        self.notes_edit.setPlainText(self.client.notes)

        # تعيين القيم المالية
        self.credit_limit_spin.setValue(self.client.credit_limit)
        self.current_balance_spin.setValue(self.client.current_balance)
        self.discount_rate_spin.setValue(self.client.discount_rate)
        self.payment_terms_spin.setValue(self.client.payment_terms)

        # تعيين القوائم المنسدلة
        client_types = {"individual": 0, "company": 1, "contractor": 2}
        self.client_type_combo.setCurrentIndex(client_types.get(self.client.client_type, 0))

        statuses = {"active": 0, "inactive": 1, "blocked": 2}
        self.status_combo.setCurrentIndex(statuses.get(self.client.status, 0))

        # تعيين الدولة
        country_index = self.country_combo.findText(self.client.country)
        if country_index >= 0:
            self.country_combo.setCurrentIndex(country_index)

    def get_client_data(self) -> Client:
        """الحصول على بيانات العميل من النموذج"""
        client_types = ["individual", "company", "contractor"]
        statuses = ["active", "inactive", "blocked"]

        return Client(
            id=self.client.id if self.client else 0,
            name=self.name_edit.text().strip(),
            company=self.company_edit.text().strip(),
            phone=self.phone_edit.text().strip(),
            email=self.email_edit.text().strip(),
            address=self.address_edit.toPlainText().strip(),
            city=self.city_edit.text().strip(),
            country=self.country_combo.currentText(),
            client_type=client_types[self.client_type_combo.currentIndex()],
            credit_limit=self.credit_limit_spin.value(),
            current_balance=self.current_balance_spin.value(),
            discount_rate=self.discount_rate_spin.value(),
            payment_terms=self.payment_terms_spin.value(),
            tax_number=self.tax_number_edit.text().strip(),
            notes=self.notes_edit.toPlainText().strip(),
            status=statuses[self.status_combo.currentIndex()],
            created_date=self.client.created_date if self.client else datetime.now().isoformat(),
            last_contact=self.client.last_contact if self.client else "",
            total_orders=self.client.total_orders if self.client else 0,
            total_revenue=self.client.total_revenue if self.client else 0.0
        )

    def validate_data(self) -> bool:
        """التحقق من صحة البيانات"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم العميل")
            self.name_edit.setFocus()
            return False

        if not self.phone_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال رقم الهاتف")
            self.phone_edit.setFocus()
            return False

        return True

    def accept(self):
        """قبول النموذج"""
        if self.validate_data():
            super().accept()


class ClientDatabase:
    """قاعدة بيانات العملاء"""

    def __init__(self, db_path: str = "clients.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """تهيئة قاعدة البيانات"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # جدول العملاء
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS clients (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    company TEXT,
                    phone TEXT NOT NULL,
                    email TEXT,
                    address TEXT,
                    city TEXT,
                    country TEXT,
                    client_type TEXT,
                    credit_limit REAL,
                    current_balance REAL,
                    discount_rate REAL,
                    payment_terms INTEGER,
                    tax_number TEXT,
                    notes TEXT,
                    status TEXT,
                    created_date TEXT,
                    last_contact TEXT,
                    total_orders INTEGER DEFAULT 0,
                    total_revenue REAL DEFAULT 0.0
                )
            """)

            # جدول جهات الاتصال
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS client_contacts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    client_id INTEGER,
                    name TEXT,
                    position TEXT,
                    phone TEXT,
                    email TEXT,
                    is_primary BOOLEAN,
                    notes TEXT,
                    FOREIGN KEY (client_id) REFERENCES clients (id)
                )
            """)

            # جدول الطلبات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS client_orders (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    client_id INTEGER,
                    order_number TEXT,
                    order_date TEXT,
                    delivery_date TEXT,
                    status TEXT,
                    total_amount REAL,
                    paid_amount REAL,
                    items_count INTEGER,
                    notes TEXT,
                    FOREIGN KEY (client_id) REFERENCES clients (id)
                )
            """)

            conn.commit()

    def add_client(self, client: Client) -> int:
        """إضافة عميل جديد"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO clients (
                    name, company, phone, email, address, city, country,
                    client_type, credit_limit, current_balance, discount_rate,
                    payment_terms, tax_number, notes, status, created_date,
                    last_contact, total_orders, total_revenue
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                client.name, client.company, client.phone, client.email,
                client.address, client.city, client.country, client.client_type,
                client.credit_limit, client.current_balance, client.discount_rate,
                client.payment_terms, client.tax_number, client.notes,
                client.status, client.created_date, client.last_contact,
                client.total_orders, client.total_revenue
            ))
            return cursor.lastrowid

    def update_client(self, client: Client):
        """تحديث بيانات العميل"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE clients SET
                    name=?, company=?, phone=?, email=?, address=?, city=?, country=?,
                    client_type=?, credit_limit=?, current_balance=?, discount_rate=?,
                    payment_terms=?, tax_number=?, notes=?, status=?
                WHERE id=?
            """, (
                client.name, client.company, client.phone, client.email,
                client.address, client.city, client.country, client.client_type,
                client.credit_limit, client.current_balance, client.discount_rate,
                client.payment_terms, client.tax_number, client.notes,
                client.status, client.id
            ))

    def delete_client(self, client_id: int):
        """حذف عميل"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM clients WHERE id=?", (client_id,))
            cursor.execute("DELETE FROM client_contacts WHERE client_id=?", (client_id,))
            cursor.execute("DELETE FROM client_orders WHERE client_id=?", (client_id,))

    def get_all_clients(self) -> List[Client]:
        """الحصول على جميع العملاء"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM clients ORDER BY name")
            rows = cursor.fetchall()

            clients = []
            for row in rows:
                client = Client(
                    id=row[0], name=row[1], company=row[2], phone=row[3],
                    email=row[4], address=row[5], city=row[6], country=row[7],
                    client_type=row[8], credit_limit=row[9], current_balance=row[10],
                    discount_rate=row[11], payment_terms=row[12], tax_number=row[13],
                    notes=row[14], status=row[15], created_date=row[16],
                    last_contact=row[17], total_orders=row[18], total_revenue=row[19]
                )
                clients.append(client)

            return clients

    def search_clients(self, search_term: str) -> List[Client]:
        """البحث في العملاء"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM clients
                WHERE name LIKE ? OR company LIKE ? OR phone LIKE ? OR email LIKE ?
                ORDER BY name
            """, (f"%{search_term}%", f"%{search_term}%", f"%{search_term}%", f"%{search_term}%"))

            rows = cursor.fetchall()
            clients = []
            for row in rows:
                client = Client(
                    id=row[0], name=row[1], company=row[2], phone=row[3],
                    email=row[4], address=row[5], city=row[6], country=row[7],
                    client_type=row[8], credit_limit=row[9], current_balance=row[10],
                    discount_rate=row[11], payment_terms=row[12], tax_number=row[13],
                    notes=row[14], status=row[15], created_date=row[16],
                    last_contact=row[17], total_orders=row[18], total_revenue=row[19]
                )
                clients.append(client)

            return clients


class ClientManagerWidget(QWidget):
    """واجهة إدارة العملاء الرئيسية"""

    def __init__(self):
        super().__init__()
        self.db = ClientDatabase()
        self.clients = []
        self.selected_client = None
        self.init_ui()
        self.load_clients()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(ModernStyleManager.SPACING['md'])
        layout.setContentsMargins(
            ModernStyleManager.SPACING['lg'],
            ModernStyleManager.SPACING['lg'],
            ModernStyleManager.SPACING['lg'],
            ModernStyleManager.SPACING['lg']
        )

        # شريط الأدوات العلوي
        toolbar_layout = QHBoxLayout()

        # عنوان الصفحة
        title_label = QLabel("🧑‍💼 إدارة العملاء")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['title']}px;
                font-weight: bold;
                color: {ModernStyleManager.COLORS['primary']};
                margin-bottom: {ModernStyleManager.SPACING['md']}px;
            }}
        """)

        # شريط البحث
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔍 البحث في العملاء...")
        self.search_edit.textChanged.connect(self.search_clients)
        self.search_edit.setProperty("inputSize", "medium")

        # أزرار التحكم
        self.add_client_btn = QPushButton("إضافة عميل")
        self.add_client_btn.setIcon(IconsManager.get_standard_icon('add'))
        self.add_client_btn.clicked.connect(self.add_client)
        self.add_client_btn.setProperty("buttonSize", "medium")

        self.edit_client_btn = QPushButton("تعديل")
        self.edit_client_btn.setIcon(IconsManager.get_standard_icon('edit'))
        self.edit_client_btn.clicked.connect(self.edit_client)
        self.edit_client_btn.setEnabled(False)
        self.edit_client_btn.setProperty("buttonSize", "medium")

        self.delete_client_btn = QPushButton("حذف")
        self.delete_client_btn.setIcon(IconsManager.get_standard_icon('delete'))
        self.delete_client_btn.clicked.connect(self.delete_client)
        self.delete_client_btn.setEnabled(False)
        self.delete_client_btn.setProperty("buttonSize", "medium")

        self.refresh_btn = QPushButton("تحديث")
        self.refresh_btn.setIcon(IconsManager.get_standard_icon('refresh'))
        self.refresh_btn.clicked.connect(self.load_clients)
        self.refresh_btn.setProperty("buttonSize", "medium")

        toolbar_layout.addWidget(title_label)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.search_edit)
        toolbar_layout.addSpacing(ModernStyleManager.SPACING['md'])
        toolbar_layout.addWidget(self.add_client_btn)
        toolbar_layout.addWidget(self.edit_client_btn)
        toolbar_layout.addWidget(self.delete_client_btn)
        toolbar_layout.addWidget(self.refresh_btn)

        layout.addLayout(toolbar_layout)

        # المقسم الرئيسي
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # جدول العملاء
        self.create_clients_table(main_splitter)

        # لوحة تفاصيل العميل
        self.create_client_details_panel(main_splitter)

        # تعيين النسب
        main_splitter.setSizes([600, 400])

        layout.addWidget(main_splitter)

        # شريط الحالة
        status_layout = QHBoxLayout()

        self.status_label = QLabel("جاهز")
        self.clients_count_label = QLabel("العملاء: 0")

        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.clients_count_label)

        layout.addLayout(status_layout)

    def create_clients_table(self, parent):
        """إنشاء جدول العملاء"""
        # إنشاء الجدول
        self.clients_table = QTableWidget()
        self.clients_table.setColumnCount(8)
        self.clients_table.setHorizontalHeaderLabels([
            "الاسم", "الشركة", "الهاتف", "البريد الإلكتروني",
            "المدينة", "النوع", "الحالة", "إجمالي الطلبات"
        ])

        # تخصيص الجدول
        header = self.clients_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)  # الاسم
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # الشركة
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # الهاتف
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)  # البريد
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # المدينة
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # النوع
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # الحالة
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.ResizeToContents)  # الطلبات

        # إعدادات الجدول
        self.clients_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.clients_table.setAlternatingRowColors(True)
        self.clients_table.setSortingEnabled(True)

        # ربط الأحداث
        self.clients_table.selectionModel().selectionChanged.connect(self.on_client_selected)
        self.clients_table.doubleClicked.connect(self.edit_client)

        parent.addWidget(self.clients_table)

    def create_client_details_panel(self, parent):
        """إنشاء لوحة تفاصيل العميل"""
        details_widget = QWidget()
        details_widget.setMaximumWidth(400)
        details_widget.setMinimumWidth(300)

        layout = QVBoxLayout(details_widget)
        layout.setSpacing(ModernStyleManager.SPACING['md'])

        # عنوان اللوحة
        details_title = QLabel("تفاصيل العميل")
        details_title.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                font-weight: bold;
                color: {ModernStyleManager.COLORS['primary']};
                padding: {ModernStyleManager.SPACING['md']}px;
                background-color: {ModernStyleManager.COLORS['card']};
                border-radius: {ModernStyleManager.COMPONENT_SIZES['border_radius']}px;
                margin-bottom: {ModernStyleManager.SPACING['md']}px;
            }}
        """)
        layout.addWidget(details_title)

        # تبويبات التفاصيل
        self.details_tabs = QTabWidget()

        # تبويب المعلومات الأساسية
        self.basic_info_tab = self.create_basic_info_display()
        self.details_tabs.addTab(self.basic_info_tab, "المعلومات")

        # تبويب الإحصائيات
        self.stats_tab = self.create_stats_display()
        self.details_tabs.addTab(self.stats_tab, "الإحصائيات")

        layout.addWidget(self.details_tabs)

        # رسالة عدم وجود تحديد
        self.no_selection_label = QLabel("اختر عميلاً لعرض التفاصيل")
        self.no_selection_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.no_selection_label.setStyleSheet(f"""
            QLabel {{
                color: {ModernStyleManager.COLORS['text_muted']};
                font-size: {ModernStyleManager.FONT_SIZES['body']}px;
                padding: {ModernStyleManager.SPACING['xl']}px;
            }}
        """)
        layout.addWidget(self.no_selection_label)

        # إخفاء التفاصيل في البداية
        self.details_tabs.hide()

        parent.addWidget(details_widget)

    def create_basic_info_display(self):
        """إنشاء عرض المعلومات الأساسية"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setSpacing(ModernStyleManager.SPACING['sm'])

        # إنشاء التسميات
        self.client_name_label = QLabel()
        self.client_company_label = QLabel()
        self.client_phone_label = QLabel()
        self.client_email_label = QLabel()
        self.client_address_label = QLabel()
        self.client_type_label = QLabel()
        self.client_status_label = QLabel()
        self.client_created_label = QLabel()

        # تطبيق الأنماط
        labels = [
            self.client_name_label, self.client_company_label,
            self.client_phone_label, self.client_email_label,
            self.client_address_label, self.client_type_label,
            self.client_status_label, self.client_created_label
        ]

        for label in labels:
            label.setWordWrap(True)
            label.setStyleSheet(f"""
                QLabel {{
                    padding: {ModernStyleManager.SPACING['sm']}px;
                    background-color: {ModernStyleManager.COLORS['surface']};
                    border-radius: {ModernStyleManager.COMPONENT_SIZES['border_radius']}px;
                    font-size: {ModernStyleManager.FONT_SIZES['body']}px;
                }}
            """)
            layout.addWidget(label)

        layout.addStretch()
        return tab

    def create_stats_display(self):
        """إنشاء عرض الإحصائيات"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # بطاقات الإحصائيات
        self.credit_limit_label = QLabel()
        self.current_balance_label = QLabel()
        self.discount_rate_label = QLabel()
        self.payment_terms_label = QLabel()

        stats_labels = [
            self.credit_limit_label, self.current_balance_label,
            self.discount_rate_label, self.payment_terms_label
        ]

        for label in stats_labels:
            label.setStyleSheet(f"""
                QLabel {{
                    padding: {ModernStyleManager.SPACING['md']}px;
                    background-color: {ModernStyleManager.COLORS['card']};
                    border-radius: {ModernStyleManager.COMPONENT_SIZES['card_radius']}px;
                    font-size: {ModernStyleManager.FONT_SIZES['body']}px;
                    font-weight: bold;
                    margin-bottom: {ModernStyleManager.SPACING['sm']}px;
                }}
            """)
            layout.addWidget(label)

        layout.addStretch()
        return tab

    def load_clients(self):
        """تحميل العملاء"""
        try:
            self.status_label.setText("جاري تحميل العملاء...")
            self.clients = self.db.get_all_clients()
            self.populate_clients_table()
            self.clients_count_label.setText(f"العملاء: {len(self.clients)}")
            self.status_label.setText("تم تحميل العملاء بنجاح")

            # مسح التحديد
            self.selected_client = None
            self.update_client_details()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل العملاء: {str(e)}")
            self.status_label.setText("فشل في تحميل العملاء")

    def populate_clients_table(self):
        """ملء جدول العملاء"""
        self.clients_table.setRowCount(len(self.clients))

        for row, client in enumerate(self.clients):
            # الاسم
            name_item = QTableWidgetItem(client.name)
            self.clients_table.setItem(row, 0, name_item)

            # الشركة
            company_item = QTableWidgetItem(client.company or "-")
            self.clients_table.setItem(row, 1, company_item)

            # الهاتف
            phone_item = QTableWidgetItem(client.phone)
            self.clients_table.setItem(row, 2, phone_item)

            # البريد الإلكتروني
            email_item = QTableWidgetItem(client.email or "-")
            self.clients_table.setItem(row, 3, email_item)

            # المدينة
            city_item = QTableWidgetItem(client.city or "-")
            self.clients_table.setItem(row, 4, city_item)

            # النوع
            type_names = {"individual": "فرد", "company": "شركة", "contractor": "مقاول"}
            type_item = QTableWidgetItem(type_names.get(client.client_type, client.client_type))
            self.clients_table.setItem(row, 5, type_item)

            # الحالة
            status_names = {"active": "نشط", "inactive": "غير نشط", "blocked": "محظور"}
            status_item = QTableWidgetItem(status_names.get(client.status, client.status))

            # تلوين الحالة
            if client.status == "active":
                status_item.setBackground(QColor("#27AE60"))
                status_item.setForeground(QColor("white"))
            elif client.status == "blocked":
                status_item.setBackground(QColor("#E74C3C"))
                status_item.setForeground(QColor("white"))
            else:
                status_item.setBackground(QColor("#F39C12"))
                status_item.setForeground(QColor("white"))

            self.clients_table.setItem(row, 6, status_item)

            # إجمالي الطلبات
            orders_item = QTableWidgetItem(str(client.total_orders))
            self.clients_table.setItem(row, 7, orders_item)

    def search_clients(self):
        """البحث في العملاء"""
        search_term = self.search_edit.text().strip()

        if not search_term:
            self.load_clients()
            return

        try:
            self.status_label.setText("جاري البحث...")
            self.clients = self.db.search_clients(search_term)
            self.populate_clients_table()
            self.clients_count_label.setText(f"نتائج البحث: {len(self.clients)}")
            self.status_label.setText(f"تم العثور على {len(self.clients)} عميل")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في البحث: {str(e)}")
            self.status_label.setText("فشل في البحث")

    def on_client_selected(self):
        """عند تحديد عميل"""
        selected_rows = self.clients_table.selectionModel().selectedRows()

        if selected_rows:
            row = selected_rows[0].row()
            if 0 <= row < len(self.clients):
                self.selected_client = self.clients[row]
                self.update_client_details()

                # تفعيل أزرار التحكم
                self.edit_client_btn.setEnabled(True)
                self.delete_client_btn.setEnabled(True)
        else:
            self.selected_client = None
            self.update_client_details()

            # تعطيل أزرار التحكم
            self.edit_client_btn.setEnabled(False)
            self.delete_client_btn.setEnabled(False)

    def update_client_details(self):
        """تحديث تفاصيل العميل"""
        if not self.selected_client:
            self.details_tabs.hide()
            self.no_selection_label.show()
            return

        self.no_selection_label.hide()
        self.details_tabs.show()

        client = self.selected_client

        # تحديث المعلومات الأساسية
        self.client_name_label.setText(f"👤 الاسم: {client.name}")
        self.client_company_label.setText(f"🏢 الشركة: {client.company or 'غير محدد'}")
        self.client_phone_label.setText(f"📞 الهاتف: {client.phone}")
        self.client_email_label.setText(f"📧 البريد: {client.email or 'غير محدد'}")
        self.client_address_label.setText(f"📍 العنوان: {client.address or 'غير محدد'}")

        type_names = {"individual": "فرد", "company": "شركة", "contractor": "مقاول"}
        self.client_type_label.setText(f"🏷️ النوع: {type_names.get(client.client_type, client.client_type)}")

        status_names = {"active": "نشط", "inactive": "غير نشط", "blocked": "محظور"}
        self.client_status_label.setText(f"🔄 الحالة: {status_names.get(client.status, client.status)}")

        # تنسيق التاريخ
        try:
            created_date = datetime.fromisoformat(client.created_date).strftime("%Y-%m-%d")
        except:
            created_date = client.created_date
        self.client_created_label.setText(f"📅 تاريخ الإنشاء: {created_date}")

        # تحديث الإحصائيات
        self.credit_limit_label.setText(f"💳 الحد الائتماني: {client.credit_limit:,.0f} ريال")

        balance_color = "#27AE60" if client.current_balance >= 0 else "#E74C3C"
        self.current_balance_label.setText(f"💰 الرصيد الحالي: {client.current_balance:,.0f} ريال")
        self.current_balance_label.setStyleSheet(f"""
            QLabel {{
                padding: {ModernStyleManager.SPACING['md']}px;
                background-color: {balance_color};
                color: white;
                border-radius: {ModernStyleManager.COMPONENT_SIZES['card_radius']}px;
                font-size: {ModernStyleManager.FONT_SIZES['body']}px;
                font-weight: bold;
                margin-bottom: {ModernStyleManager.SPACING['sm']}px;
            }}
        """)

        self.discount_rate_label.setText(f"🏷️ نسبة الخصم: {client.discount_rate}%")
        self.payment_terms_label.setText(f"⏰ شروط الدفع: {client.payment_terms} يوم")

    def add_client(self):
        """إضافة عميل جديد"""
        dialog = ClientDialog(parent=self)

        if dialog.exec() == QDialog.DialogCode.Accepted:
            try:
                client_data = dialog.get_client_data()
                client_id = self.db.add_client(client_data)

                QMessageBox.information(self, "نجح", f"تم إضافة العميل بنجاح\nرقم العميل: {client_id}")
                self.load_clients()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في إضافة العميل: {str(e)}")

    def edit_client(self):
        """تعديل العميل المحدد"""
        if not self.selected_client:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عميل للتعديل")
            return

        dialog = ClientDialog(client=self.selected_client, parent=self)

        if dialog.exec() == QDialog.DialogCode.Accepted:
            try:
                client_data = dialog.get_client_data()
                self.db.update_client(client_data)

                QMessageBox.information(self, "نجح", "تم تحديث بيانات العميل بنجاح")
                self.load_clients()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في تحديث العميل: {str(e)}")

    def delete_client(self):
        """حذف العميل المحدد"""
        if not self.selected_client:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عميل للحذف")
            return

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف العميل '{self.selected_client.name}'؟\n"
            "سيتم حذف جميع البيانات المرتبطة بهذا العميل.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                self.db.delete_client(self.selected_client.id)
                QMessageBox.information(self, "نجح", "تم حذف العميل بنجاح")
                self.load_clients()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف العميل: {str(e)}")