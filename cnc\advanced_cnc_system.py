#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام تكامل ماكينات CNC المتقدم - PySide6
Advanced CNC Machine Integration System - PySide6
"""

import os
import json
import uuid
import serial
import threading
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QTableWidget, QTableWidgetItem, QHeaderView, QLineEdit,
    QComboBox, QTextEdit, QDateEdit, QSpinBox, QDoubleSpinBox,
    QGroupBox, QFormLayout, QDialog, QDialogButtonBox,
    QMessageBox, QFileDialog, QTabWidget, QSplitter,
    QTreeWidget, QTreeWidgetItem, QProgressBar, Q<PERSON><PERSON><PERSON><PERSON>ox,
    QGridLayout, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, QPlainTextEdit, QListWidget
)
from PySide6.QtCore import Qt, QDate, Signal, QTimer, QThread, QObject
from PySide6.QtGui import QFont, QColor, QPixmap, QIcon, QTextCursor

from ui.modern_styles import ModernStyleManager
from ui.icons_manager import IconsManager


@dataclass
class CNCMachine:
    """ماكينة CNC"""
    id: str
    name: str
    model: str
    manufacturer: str
    serial_port: str
    baud_rate: int
    work_area_x: float  # منطقة العمل X (مم)
    work_area_y: float  # منطقة العمل Y (مم)
    work_area_z: float  # منطقة العمل Z (مم)
    spindle_speed_max: int  # أقصى سرعة للمغزل
    feed_rate_max: float  # أقصى معدل تغذية
    tool_changer: bool  # مغير الأدوات
    coolant_system: bool  # نظام التبريد
    status: str = "غير متصل"  # متصل، غير متصل، يعمل، متوقف، خطأ
    last_maintenance: str = ""
    notes: str = ""

    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())


@dataclass
class CNCJob:
    """مهمة CNC"""
    id: str
    name: str
    machine_id: str
    gcode_file: str
    material: str
    thickness: float
    estimated_time: int  # بالدقائق
    actual_time: int = 0
    status: str = "في الانتظار"  # في الانتظار، قيد التنفيذ، مكتمل، متوقف، خطأ
    progress: float = 0.0  # نسبة الإنجاز
    created_date: str = ""
    started_date: str = ""
    completed_date: str = ""
    notes: str = ""

    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.created_date:
            self.created_date = datetime.now().isoformat()


@dataclass
class GCodeCommand:
    """أمر G-Code"""
    line_number: int
    command: str
    x: Optional[float] = None
    y: Optional[float] = None
    z: Optional[float] = None
    feed_rate: Optional[float] = None
    spindle_speed: Optional[float] = None
    tool_number: Optional[int] = None
    comment: str = ""


class GCodeParser:
    """محلل G-Code"""

    def __init__(self):
        self.commands = []
        self.total_lines = 0
        self.estimated_time = 0

    def parse_file(self, file_path: str) -> List[GCodeCommand]:
        """تحليل ملف G-Code"""
        commands = []

        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                lines = file.readlines()

            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line or line.startswith(';'):
                    continue

                command = self.parse_line(line_num, line)
                if command:
                    commands.append(command)

            self.commands = commands
            self.total_lines = len(commands)
            self.estimate_execution_time()

            return commands

        except Exception as e:
            print(f"خطأ في تحليل ملف G-Code: {e}")
            return []

    def parse_line(self, line_number: int, line: str) -> Optional[GCodeCommand]:
        """تحليل سطر G-Code"""
        try:
            # إزالة التعليقات
            if ';' in line:
                comment_index = line.index(';')
                comment = line[comment_index+1:].strip()
                line = line[:comment_index].strip()
            else:
                comment = ""

            if not line:
                return None

            # تحليل الأوامر
            parts = line.split()
            command = parts[0] if parts else ""

            gcode_cmd = GCodeCommand(
                line_number=line_number,
                command=command,
                comment=comment
            )

            # استخراج المعاملات
            for part in parts[1:]:
                if part.startswith('X'):
                    gcode_cmd.x = float(part[1:])
                elif part.startswith('Y'):
                    gcode_cmd.y = float(part[1:])
                elif part.startswith('Z'):
                    gcode_cmd.z = float(part[1:])
                elif part.startswith('F'):
                    gcode_cmd.feed_rate = float(part[1:])
                elif part.startswith('S'):
                    gcode_cmd.spindle_speed = float(part[1:])
                elif part.startswith('T'):
                    gcode_cmd.tool_number = int(part[1:])

            return gcode_cmd

        except Exception as e:
            print(f"خطأ في تحليل السطر {line_number}: {e}")
            return None

    def estimate_execution_time(self):
        """تقدير وقت التنفيذ"""
        total_time = 0
        current_feed_rate = 1000  # معدل تغذية افتراضي
        last_position = [0, 0, 0]

        for cmd in self.commands:
            if cmd.feed_rate:
                current_feed_rate = cmd.feed_rate

            if cmd.command.startswith('G0') or cmd.command.startswith('G1'):
                # حساب المسافة والوقت
                new_position = [
                    cmd.x if cmd.x is not None else last_position[0],
                    cmd.y if cmd.y is not None else last_position[1],
                    cmd.z if cmd.z is not None else last_position[2]
                ]

                distance = ((new_position[0] - last_position[0])**2 +
                           (new_position[1] - last_position[1])**2 +
                           (new_position[2] - last_position[2])**2)**0.5

                time_for_move = distance / current_feed_rate * 60  # بالثواني
                total_time += time_for_move

                last_position = new_position

        self.estimated_time = int(total_time / 60)  # بالدقائق


class CNCCommunicator(QObject):
    """واجهة التواصل مع ماكينة CNC"""

    status_changed = Signal(str)
    progress_updated = Signal(float)
    message_received = Signal(str)
    error_occurred = Signal(str)

    def __init__(self, machine: CNCMachine):
        super().__init__()
        self.machine = machine
        self.serial_connection = None
        self.is_connected = False
        self.is_running = False
        self.current_job = None

    def connect_to_machine(self) -> bool:
        """الاتصال بالماكينة"""
        try:
            self.serial_connection = serial.Serial(
                port=self.machine.serial_port,
                baudrate=self.machine.baud_rate,
                timeout=1
            )

            self.is_connected = True
            self.status_changed.emit("متصل")
            self.message_received.emit("تم الاتصال بالماكينة بنجاح")
            return True

        except Exception as e:
            self.error_occurred.emit(f"فشل الاتصال: {str(e)}")
            return False

    def disconnect_from_machine(self):
        """قطع الاتصال"""
        if self.serial_connection and self.serial_connection.is_open:
            self.serial_connection.close()

        self.is_connected = False
        self.status_changed.emit("غير متصل")
        self.message_received.emit("تم قطع الاتصال")

    def send_command(self, command: str) -> bool:
        """إرسال أمر للماكينة"""
        if not self.is_connected or not self.serial_connection:
            self.error_occurred.emit("الماكينة غير متصلة")
            return False

        try:
            self.serial_connection.write(f"{command}\n".encode())
            response = self.serial_connection.readline().decode().strip()
            self.message_received.emit(f"أرسل: {command} | استقبل: {response}")
            return True

        except Exception as e:
            self.error_occurred.emit(f"خطأ في الإرسال: {str(e)}")
            return False

    def start_job(self, job: CNCJob, gcode_commands: List[GCodeCommand]):
        """بدء تنفيذ مهمة"""
        if not self.is_connected:
            self.error_occurred.emit("الماكينة غير متصلة")
            return

        self.current_job = job
        self.is_running = True
        self.status_changed.emit("يعمل")

        # تشغيل المهمة في خيط منفصل
        self.job_thread = threading.Thread(target=self._execute_job, args=(gcode_commands,))
        self.job_thread.start()

    def _execute_job(self, commands: List[GCodeCommand]):
        """تنفيذ المهمة"""
        try:
            total_commands = len(commands)

            for i, cmd in enumerate(commands):
                if not self.is_running:
                    break

                # إرسال الأمر
                success = self.send_command(cmd.command)
                if not success:
                    break

                # تحديث التقدم
                progress = (i + 1) / total_commands * 100
                self.progress_updated.emit(progress)

                # انتظار قصير
                time.sleep(0.1)

            if self.is_running:
                self.status_changed.emit("مكتمل")
                self.message_received.emit("تم إنجاز المهمة بنجاح")
            else:
                self.status_changed.emit("متوقف")
                self.message_received.emit("تم إيقاف المهمة")

        except Exception as e:
            self.error_occurred.emit(f"خطأ في تنفيذ المهمة: {str(e)}")
            self.status_changed.emit("خطأ")

        finally:
            self.is_running = False

    def stop_job(self):
        """إيقاف المهمة"""
        self.is_running = False
        self.send_command("M0")  # أمر الإيقاف
        self.status_changed.emit("متوقف")

    def emergency_stop(self):
        """إيقاف طارئ"""
        self.is_running = False
        self.send_command("M112")  # إيقاف طارئ
        self.status_changed.emit("إيقاف طارئ")
        self.error_occurred.emit("تم تفعيل الإيقاف الطارئ")


class AdvancedCNCManagerWidget(QWidget):
    """واجهة إدارة ماكينات CNC المتقدمة"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.machines = {}  # قاموس الماكينات
        self.communicators = {}  # قاموس المتصلين
        self.current_machine_id = None
        self.gcode_parser = GCodeParser()
        self.init_ui()
        self.load_machines()
        self.init_default_machines()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)

        # شريط الأدوات
        self.create_toolbar(layout)

        # المحتوى الرئيسي
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # قائمة الماكينات
        self.create_machines_panel(main_splitter)

        # لوحة التحكم
        self.create_control_panel(main_splitter)

        # تعيين النسب
        main_splitter.setSizes([300, 700])

        layout.addWidget(main_splitter)

        # شريط الحالة
        self.create_status_bar(layout)

    def create_toolbar(self, parent_layout):
        """إنشاء شريط الأدوات"""
        toolbar_widget = QWidget()
        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(0, 0, 0, 0)

        # أزرار الإدارة
        add_machine_btn = QPushButton("إضافة ماكينة")
        add_machine_btn.setIcon(IconsManager.get_standard_icon('add'))
        add_machine_btn.clicked.connect(self.add_new_machine)

        load_gcode_btn = QPushButton("تحميل G-Code")
        load_gcode_btn.setIcon(IconsManager.get_standard_icon('open'))
        load_gcode_btn.clicked.connect(self.load_gcode_file)

        emergency_stop_btn = QPushButton("إيقاف طارئ")
        emergency_stop_btn.setIcon(IconsManager.get_standard_icon('stop'))
        emergency_stop_btn.setStyleSheet("QPushButton { background-color: #e74c3c; color: white; font-weight: bold; }")
        emergency_stop_btn.clicked.connect(self.emergency_stop_all)

        toolbar_layout.addWidget(add_machine_btn)
        toolbar_layout.addWidget(load_gcode_btn)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(emergency_stop_btn)

        parent_layout.addWidget(toolbar_widget)

    def load_machines(self):
        """تحميل الماكينات المحفوظة"""
        # هنا يمكن تحميل الماكينات من ملف أو قاعدة بيانات
        # للتجربة، سنضيف ماكينة افتراضية
        default_machine = CNCMachine(
            id="default_001",
            name="ماكينة CNC الرئيسية",
            model="CNC-3018",
            manufacturer="Generic",
            serial_port="COM3",
            baud_rate=115200,
            work_area_x=300,
            work_area_y=180,
            work_area_z=45,
            spindle_speed_max=10000,
            feed_rate_max=1000,
            tool_changer=False,
            coolant_system=False
        )
    def add_new_machine(self):
        """إضافة ماكينة جديدة"""
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ إضافة ماكينة جديدة قريباً")

    def load_gcode_file(self):
        """تحميل ملف G-Code"""
        try:
            from PySide6.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "اختر ملف G-Code",
                "",
                "G-Code Files (*.gcode *.nc *.cnc);;All Files (*)"
            )

            if file_path:
                # قراءة ملف G-Code
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        gcode_content = f.read()

                    # تحليل أساسي للملف
                    lines = gcode_content.split('\n')
                    total_lines = len(lines)

                    # إحصائيات بسيطة
                    g_commands = len([line for line in lines if line.strip().startswith('G')])
                    m_commands = len([line for line in lines if line.strip().startswith('M')])

                    # عرض معلومات الملف
                    info_text = f"""
📁 تم تحميل ملف G-Code بنجاح!

📄 اسم الملف: {os.path.basename(file_path)}
📊 إجمالي الأسطر: {total_lines}
⚙️ أوامر G: {g_commands}
🔧 أوامر M: {m_commands}

✅ الملف جاهز للمعالجة
                    """

                    QMessageBox.information(self, "تحميل G-Code", info_text)

                except Exception as e:
                    QMessageBox.warning(self, "خطأ", f"فشل في قراءة ملف G-Code: {e}")

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل ملف G-Code: {e}")

    def preview_gcode(self):
        """معاينة G-Code"""
        try:
            # فتح حوار اختيار ملف G-Code
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "اختر ملف G-Code للمعاينة",
                "",
                "G-Code Files (*.gcode *.nc *.cnc);;All Files (*)"
            )

            if file_path:
                # تحليل ملف G-Code
                commands = self.gcode_parser.parse_file(file_path)

                if commands:
                    # عرض حوار المعاينة
                    preview_dialog = GCodePreviewDialog(self, file_path, commands, self.gcode_parser)
                    preview_dialog.exec()
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في تحليل ملف G-Code")

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في معاينة G-Code: {e}")

    def start_cnc_job(self):
        """بدء مهمة CNC"""
        QMessageBox.information(self, "بدء المهمة",
                               "سيتم تنفيذ بدء مهمة CNC قريباً\n"
                               "الميزات القادمة:\n"
                               "• إرسال G-Code للماكينة\n"
                               "• مراقبة التقدم في الوقت الفعلي\n"
                               "• تحكم في السرعة والتوقف")

    def monitor_cnc_status(self):
        """مراقبة حالة CNC"""
        try:
            if not self.machines:
                QMessageBox.warning(self, "تحذير", "لا توجد ماكينات متاحة للمراقبة")
                return

            # عرض حوار مراقبة الحالة
            monitor_dialog = CNCStatusMonitorDialog(self, self.machines, self.communicators)
            monitor_dialog.exec()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في مراقبة الحالة: {e}")

    def add_new_machine(self):
        """إضافة ماكينة جديدة"""
        try:
            dialog = CNCMachineDialog(self)
            if dialog.exec() == QDialog.DialogCode.Accepted:
                machine_data = dialog.get_machine_data()

                # إنشاء كائن الماكينة
                machine = CNCMachine(
                    id="",  # سيتم إنشاؤه تلقائياً
                    name=machine_data['name'],
                    model=machine_data['model'],
                    manufacturer=machine_data['manufacturer'],
                    serial_port=machine_data['serial_port'],
                    baud_rate=machine_data['baud_rate'],
                    work_area_x=machine_data['work_area_x'],
                    work_area_y=machine_data['work_area_y'],
                    work_area_z=machine_data['work_area_z'],
                    spindle_speed_max=machine_data['spindle_speed_max'],
                    feed_rate_max=machine_data['feed_rate_max'],
                    tool_changer=machine_data['tool_changer'],
                    coolant_system=machine_data['coolant_system'],
                    notes=machine_data['notes']
                )

                # إضافة الماكينة
                self.machines[machine.id] = machine

                # إنشاء متصل للماكينة
                communicator = CNCCommunicator(machine)
                self.communicators[machine.id] = communicator

                # تحديث القائمة
                self.refresh_machines_list()

                QMessageBox.information(self, "نجح", f"تم إضافة الماكينة '{machine.name}' بنجاح")

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في إضافة الماكينة: {e}")

    def refresh_machines_list(self):
        """تحديث قائمة الماكينات"""
        try:
            self.machines_list.clear()

            for machine_id, machine in self.machines.items():
                # تحديد أيقونة الحالة
                if machine.status == "متصل":
                    icon = "🟢"
                elif machine.status == "يعمل":
                    icon = "🔵"
                elif machine.status == "خطأ":
                    icon = "🔴"
                else:
                    icon = "⚪"

                item_text = f"{icon} {machine.name} - {machine.status}"
                self.machines_list.addItem(item_text)

            # تحديث شريط الحالة
            self.update_status_bar()

        except Exception as e:
            print(f"خطأ في تحديث قائمة الماكينات: {e}")

    def emergency_stop_all(self):
        """إيقاف طوارئ لجميع الماكينات"""
        QMessageBox.warning(self, "إيقاف الطوارئ",
                           "🚨 تم تفعيل إيقاف الطوارئ!\n\n"
                           "سيتم إيقاف جميع الماكينات فوراً.\n"
                           "تأكد من سلامة المنطقة قبل إعادة التشغيل.")

    def reset_all_machines(self):
        """إعادة تعيين جميع الماكينات"""
        QMessageBox.information(self, "إعادة التعيين",
                               "سيتم تنفيذ إعادة تعيين الماكينات قريباً\n"
                               "الميزات القادمة:\n"
                               "• إعادة تعيين المحاور\n"
                               "• مسح الذاكرة\n"
                               "• إعادة المعايرة")

    def create_machines_panel(self, parent_splitter):
        """إنشاء لوحة الماكينات"""
        machines_widget = QWidget()
        layout = QVBoxLayout(machines_widget)

        # عنوان اللوحة
        title_label = QLabel("ماكينات CNC المتاحة")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; margin: 10px;")

        # قائمة الماكينات
        self.machines_list = QListWidget()

        # إضافة ماكينات تجريبية
        sample_machines = [
            "🔧 ماكينة CNC الرئيسية - متصلة",
            "⚙️ ماكينة التفريز - جاهزة",
            "🔩 ماكينة الخراطة - متوقفة",
            "⚡ ماكينة الليزر - قيد التشغيل"
        ]

        for machine in sample_machines:
            self.machines_list.addItem(machine)

        # أزرار التحكم
        buttons_widget = QWidget()
        buttons_layout = QHBoxLayout(buttons_widget)

        connect_btn = QPushButton("اتصال")
        connect_btn.clicked.connect(self.connect_machine)

        disconnect_btn = QPushButton("قطع الاتصال")
        disconnect_btn.clicked.connect(self.disconnect_machine)

        buttons_layout.addWidget(connect_btn)
        buttons_layout.addWidget(disconnect_btn)

        layout.addWidget(title_label)
        layout.addWidget(self.machines_list)
        layout.addWidget(buttons_widget)

        parent_splitter.addWidget(machines_widget)

    def connect_machine(self):
        """اتصال بالماكينة"""
        try:
            current_row = self.machines_list.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار ماكينة للاتصال بها")
                return

            # الحصول على معرف الماكينة
            machine_ids = list(self.machines.keys())
            if current_row >= len(machine_ids):
                QMessageBox.warning(self, "خطأ", "ماكينة غير صالحة")
                return

            machine_id = machine_ids[current_row]
            machine = self.machines[machine_id]

            # محاولة الاتصال
            if machine_id in self.communicators:
                communicator = self.communicators[machine_id]

                # محاكاة الاتصال (في التطبيق الحقيقي سيكون اتصال فعلي)
                success = True  # communicator.connect_to_machine()

                if success:
                    machine.status = "متصل"
                    self.refresh_machines_list()
                    QMessageBox.information(self, "نجح الاتصال", f"تم الاتصال بالماكينة '{machine.name}' بنجاح")
                else:
                    QMessageBox.warning(self, "فشل الاتصال", f"فشل في الاتصال بالماكينة '{machine.name}'")
            else:
                QMessageBox.warning(self, "خطأ", "متصل الماكينة غير متاح")

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في الاتصال: {e}")

    def disconnect_machine(self):
        """قطع الاتصال بالماكينة"""
        try:
            current_row = self.machines_list.currentRow()
            if current_row < 0:
                QMessageBox.warning(self, "تحذير", "يرجى اختيار ماكينة لقطع الاتصال عنها")
                return

            # الحصول على معرف الماكينة
            machine_ids = list(self.machines.keys())
            if current_row >= len(machine_ids):
                QMessageBox.warning(self, "خطأ", "ماكينة غير صالحة")
                return

            machine_id = machine_ids[current_row]
            machine = self.machines[machine_id]

            # قطع الاتصال
            if machine_id in self.communicators:
                communicator = self.communicators[machine_id]
                # communicator.disconnect_from_machine()

                machine.status = "غير متصل"
                self.refresh_machines_list()
                QMessageBox.information(self, "تم قطع الاتصال", f"تم قطع الاتصال عن الماكينة '{machine.name}'")
            else:
                QMessageBox.warning(self, "خطأ", "متصل الماكينة غير متاح")

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في قطع الاتصال: {e}")

    def create_control_panel(self, parent_splitter):
        """إنشاء لوحة التحكم"""
        control_widget = QWidget()
        layout = QVBoxLayout(control_widget)

        # عنوان لوحة التحكم
        title_label = QLabel("لوحة التحكم الرئيسية")
        title_label.setStyleSheet("font-weight: bold; font-size: 16px; margin: 10px;")

        # أزرار التحكم الرئيسية
        main_controls_group = QGroupBox("التحكم الرئيسي")
        main_controls_layout = QVBoxLayout(main_controls_group)

        start_btn = QPushButton("🚀 بدء التشغيل")
        start_btn.setStyleSheet("QPushButton { background-color: #27AE60; color: white; padding: 10px; }")
        start_btn.clicked.connect(self.start_cnc_job)

        pause_btn = QPushButton("⏸️ إيقاف مؤقت")
        pause_btn.setStyleSheet("QPushButton { background-color: #F39C12; color: white; padding: 10px; }")

        stop_btn = QPushButton("⏹️ إيقاف")
        stop_btn.setStyleSheet("QPushButton { background-color: #E74C3C; color: white; padding: 10px; }")

        emergency_btn = QPushButton("🚨 إيقاف طوارئ")
        emergency_btn.setStyleSheet("QPushButton { background-color: #C0392B; color: white; padding: 10px; font-weight: bold; }")
        emergency_btn.clicked.connect(self.emergency_stop_all)

        main_controls_layout.addWidget(start_btn)
        main_controls_layout.addWidget(pause_btn)
        main_controls_layout.addWidget(stop_btn)
        main_controls_layout.addWidget(emergency_btn)

        # معلومات الحالة
        status_group = QGroupBox("معلومات الحالة")
        status_layout = QFormLayout(status_group)

        self.status_label = QLabel("جاهز")
        self.progress_label = QLabel("0%")
        self.time_label = QLabel("00:00:00")

        status_layout.addRow("الحالة:", self.status_label)
        status_layout.addRow("التقدم:", self.progress_label)
        status_layout.addRow("الوقت:", self.time_label)

        layout.addWidget(title_label)
        layout.addWidget(main_controls_group)
        layout.addWidget(status_group)
        layout.addStretch()

        parent_splitter.addWidget(control_widget)

    def create_status_bar(self, parent_layout):
        """إنشاء شريط الحالة"""
        status_widget = QWidget()
        status_layout = QHBoxLayout(status_widget)
        status_layout.setContentsMargins(5, 5, 5, 5)

        # معلومات الحالة
        self.connection_status_label = QLabel("🔴 غير متصل")
        self.machine_count_label = QLabel("الماكينات: 0")
        self.active_jobs_label = QLabel("المهام النشطة: 0")
        self.total_runtime_label = QLabel("وقت التشغيل: 00:00:00")

        # تنسيق التسميات
        for label in [self.connection_status_label, self.machine_count_label,
                     self.active_jobs_label, self.total_runtime_label]:
            label.setStyleSheet("""
                QLabel {
                    padding: 5px 10px;
                    border: 1px solid #BDC3C7;
                    border-radius: 3px;
                    background-color: #ECF0F1;
                    font-size: 12px;
                }
            """)

        status_layout.addWidget(self.connection_status_label)
        status_layout.addWidget(self.machine_count_label)
        status_layout.addWidget(self.active_jobs_label)
        status_layout.addWidget(self.total_runtime_label)
        status_layout.addStretch()

        parent_layout.addWidget(status_widget)

    def update_status_bar(self):
        """تحديث شريط الحالة"""
        try:
            # تحديث عدد الماكينات
            machine_count = len(getattr(self, 'machines', {}))
            self.machine_count_label.setText(f"الماكينات: {machine_count}")

            # تحديث حالة الاتصال
            if machine_count > 0:
                self.connection_status_label.setText("🟢 متصل")
                self.connection_status_label.setStyleSheet("""
                    QLabel {
                        padding: 5px 10px;
                        border: 1px solid #27AE60;
                        border-radius: 3px;
                        background-color: #D5EFDB;
                        color: #27AE60;
                        font-size: 12px;
                        font-weight: bold;
                    }
                """)
            else:
                self.connection_status_label.setText("🔴 غير متصل")
                self.connection_status_label.setStyleSheet("""
                    QLabel {
                        padding: 5px 10px;
                        border: 1px solid #E74C3C;
                        border-radius: 3px;
                        background-color: #FADBD8;
                        color: #E74C3C;
                        font-size: 12px;
                        font-weight: bold;
                    }
                """)

        except Exception as e:
            print(f"خطأ في تحديث شريط الحالة: {e}")


    def init_default_machines(self):
        """تهيئة الماكينات الافتراضية"""
        try:
            # إنشاء ماكينة افتراضية
            default_machine = CNCMachine(
                id="default_001",
                name="ماكينة CNC الافتراضية",
                model="CNC-3018",
                manufacturer="Generic",
                serial_port="COM3",
                baud_rate=115200,
                work_area_x=300,
                work_area_y=180,
                work_area_z=45,
                spindle_speed_max=10000,
                feed_rate_max=1000,
                tool_changer=False,
                coolant_system=False
            )
            self.machines[default_machine.id] = default_machine
            print("✅ تم تهيئة الماكينة الافتراضية")

            # تحديث القائمة
            self.refresh_machines_list()

        except Exception as e:
            print(f"⚠️ تعذر تهيئة الماكينة الافتراضية: {e}")


class CNCMachineDialog(QDialog):
    """حوار إضافة/تعديل ماكينة CNC"""

    def __init__(self, parent=None, machine_data=None):
        super().__init__(parent)
        self.machine_data = machine_data or {}
        self.init_ui()
        self.load_data()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إضافة ماكينة CNC جديدة" if not self.machine_data else "تعديل ماكينة CNC")
        self.setModal(True)
        self.resize(500, 600)

        layout = QVBoxLayout(self)

        # تبويبات البيانات
        tabs = QTabWidget()

        # تبويب المعلومات الأساسية
        basic_tab = QWidget()
        basic_layout = QFormLayout(basic_tab)

        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("اسم الماكينة")
        basic_layout.addRow("الاسم *:", self.name_edit)

        self.model_edit = QLineEdit()
        self.model_edit.setPlaceholderText("طراز الماكينة")
        basic_layout.addRow("الطراز:", self.model_edit)

        self.manufacturer_edit = QLineEdit()
        self.manufacturer_edit.setPlaceholderText("الشركة المصنعة")
        basic_layout.addRow("الشركة المصنعة:", self.manufacturer_edit)

        tabs.addTab(basic_tab, "المعلومات الأساسية")

        # تبويب الاتصال
        connection_tab = QWidget()
        connection_layout = QFormLayout(connection_tab)

        self.serial_port_combo = QComboBox()
        self.serial_port_combo.setEditable(True)
        self.serial_port_combo.addItems(["COM1", "COM2", "COM3", "COM4", "/dev/ttyUSB0", "/dev/ttyACM0"])
        connection_layout.addRow("منفذ التسلسل *:", self.serial_port_combo)

        self.baud_rate_combo = QComboBox()
        self.baud_rate_combo.addItems(["9600", "19200", "38400", "57600", "115200", "250000"])
        self.baud_rate_combo.setCurrentText("115200")
        connection_layout.addRow("معدل البود:", self.baud_rate_combo)

        tabs.addTab(connection_tab, "الاتصال")

        # تبويب منطقة العمل
        workspace_tab = QWidget()
        workspace_layout = QFormLayout(workspace_tab)

        self.work_area_x_spin = QDoubleSpinBox()
        self.work_area_x_spin.setRange(0.0, 9999.99)
        self.work_area_x_spin.setSuffix(" مم")
        self.work_area_x_spin.setValue(300.0)
        workspace_layout.addRow("منطقة العمل X:", self.work_area_x_spin)

        self.work_area_y_spin = QDoubleSpinBox()
        self.work_area_y_spin.setRange(0.0, 9999.99)
        self.work_area_y_spin.setSuffix(" مم")
        self.work_area_y_spin.setValue(180.0)
        workspace_layout.addRow("منطقة العمل Y:", self.work_area_y_spin)

        self.work_area_z_spin = QDoubleSpinBox()
        self.work_area_z_spin.setRange(0.0, 9999.99)
        self.work_area_z_spin.setSuffix(" مم")
        self.work_area_z_spin.setValue(45.0)
        workspace_layout.addRow("منطقة العمل Z:", self.work_area_z_spin)

        tabs.addTab(workspace_tab, "منطقة العمل")

        # تبويب المواصفات
        specs_tab = QWidget()
        specs_layout = QFormLayout(specs_tab)

        self.spindle_speed_spin = QSpinBox()
        self.spindle_speed_spin.setRange(0, 50000)
        self.spindle_speed_spin.setSuffix(" RPM")
        self.spindle_speed_spin.setValue(10000)
        specs_layout.addRow("أقصى سرعة مغزل:", self.spindle_speed_spin)

        self.feed_rate_spin = QDoubleSpinBox()
        self.feed_rate_spin.setRange(0.0, 10000.0)
        self.feed_rate_spin.setSuffix(" مم/دقيقة")
        self.feed_rate_spin.setValue(1000.0)
        specs_layout.addRow("أقصى معدل تغذية:", self.feed_rate_spin)

        self.tool_changer_check = QCheckBox("مغير الأدوات")
        specs_layout.addRow("الميزات:", self.tool_changer_check)

        self.coolant_system_check = QCheckBox("نظام التبريد")
        specs_layout.addRow("", self.coolant_system_check)

        tabs.addTab(specs_tab, "المواصفات")

        # تبويب الملاحظات
        notes_tab = QWidget()
        notes_layout = QVBoxLayout(notes_tab)

        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("ملاحظات إضافية حول الماكينة...")
        notes_layout.addWidget(self.notes_edit)

        tabs.addTab(notes_tab, "ملاحظات")

        layout.addWidget(tabs)

        # أزرار الحوار
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        buttons.button(QDialogButtonBox.StandardButton.Ok).setText("حفظ")
        buttons.button(QDialogButtonBox.StandardButton.Cancel).setText("إلغاء")

        layout.addWidget(buttons)

    def load_data(self):
        """تحميل البيانات الموجودة"""
        if self.machine_data:
            self.name_edit.setText(self.machine_data.get('name', ''))
            self.model_edit.setText(self.machine_data.get('model', ''))
            self.manufacturer_edit.setText(self.machine_data.get('manufacturer', ''))
            self.serial_port_combo.setCurrentText(self.machine_data.get('serial_port', 'COM3'))
            self.baud_rate_combo.setCurrentText(str(self.machine_data.get('baud_rate', 115200)))
            self.work_area_x_spin.setValue(self.machine_data.get('work_area_x', 300.0))
            self.work_area_y_spin.setValue(self.machine_data.get('work_area_y', 180.0))
            self.work_area_z_spin.setValue(self.machine_data.get('work_area_z', 45.0))
            self.spindle_speed_spin.setValue(self.machine_data.get('spindle_speed_max', 10000))
            self.feed_rate_spin.setValue(self.machine_data.get('feed_rate_max', 1000.0))
            self.tool_changer_check.setChecked(self.machine_data.get('tool_changer', False))
            self.coolant_system_check.setChecked(self.machine_data.get('coolant_system', False))
            self.notes_edit.setPlainText(self.machine_data.get('notes', ''))

    def get_machine_data(self):
        """الحصول على بيانات الماكينة"""
        return {
            'name': self.name_edit.text().strip(),
            'model': self.model_edit.text().strip(),
            'manufacturer': self.manufacturer_edit.text().strip(),
            'serial_port': self.serial_port_combo.currentText().strip(),
            'baud_rate': int(self.baud_rate_combo.currentText()),
            'work_area_x': self.work_area_x_spin.value(),
            'work_area_y': self.work_area_y_spin.value(),
            'work_area_z': self.work_area_z_spin.value(),
            'spindle_speed_max': self.spindle_speed_spin.value(),
            'feed_rate_max': self.feed_rate_spin.value(),
            'tool_changer': self.tool_changer_check.isChecked(),
            'coolant_system': self.coolant_system_check.isChecked(),
            'notes': self.notes_edit.toPlainText().strip()
        }

    def accept(self):
        """التحقق من صحة البيانات قبل الحفظ"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم الماكينة")
            self.name_edit.setFocus()
            return

        if not self.serial_port_combo.currentText().strip():
            QMessageBox.warning(self, "خطأ", "يرجى تحديد منفذ التسلسل")
            self.serial_port_combo.setFocus()
            return

        super().accept()


class GCodePreviewDialog(QDialog):
    """حوار معاينة G-Code"""

    def __init__(self, parent=None, file_path="", commands=None, parser=None):
        super().__init__(parent)
        self.file_path = file_path
        self.commands = commands or []
        self.parser = parser
        self.init_ui()
        self.load_preview()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle(f"معاينة G-Code - {os.path.basename(self.file_path)}")
        self.setModal(True)
        self.resize(800, 600)

        layout = QVBoxLayout(self)

        # معلومات الملف
        info_group = QGroupBox("معلومات الملف")
        info_layout = QFormLayout(info_group)

        self.file_name_label = QLabel(os.path.basename(self.file_path))
        self.total_lines_label = QLabel(str(len(self.commands)))
        self.estimated_time_label = QLabel(f"{getattr(self.parser, 'estimated_time', 0)} دقيقة")

        info_layout.addRow("اسم الملف:", self.file_name_label)
        info_layout.addRow("عدد الأوامر:", self.total_lines_label)
        info_layout.addRow("الوقت المتوقع:", self.estimated_time_label)

        layout.addWidget(info_group)

        # معاينة الأوامر
        preview_group = QGroupBox("معاينة الأوامر")
        preview_layout = QVBoxLayout(preview_group)

        self.commands_table = QTableWidget()
        self.commands_table.setColumnCount(4)
        self.commands_table.setHorizontalHeaderLabels(["السطر", "الأمر", "المعاملات", "التعليق"])

        preview_layout.addWidget(self.commands_table)
        layout.addWidget(preview_group)

        # أزرار الحوار
        buttons = QDialogButtonBox(QDialogButtonBox.StandardButton.Close)
        buttons.rejected.connect(self.reject)
        buttons.button(QDialogButtonBox.StandardButton.Close).setText("إغلاق")

        layout.addWidget(buttons)

    def load_preview(self):
        """تحميل معاينة الأوامر"""
        try:
            self.commands_table.setRowCount(len(self.commands))

            for i, cmd in enumerate(self.commands):
                # رقم السطر
                self.commands_table.setItem(i, 0, QTableWidgetItem(str(cmd.line_number)))

                # الأمر
                self.commands_table.setItem(i, 1, QTableWidgetItem(cmd.command))

                # المعاملات
                params = []
                if cmd.x is not None:
                    params.append(f"X{cmd.x}")
                if cmd.y is not None:
                    params.append(f"Y{cmd.y}")
                if cmd.z is not None:
                    params.append(f"Z{cmd.z}")
                if cmd.feed_rate is not None:
                    params.append(f"F{cmd.feed_rate}")
                if cmd.spindle_speed is not None:
                    params.append(f"S{cmd.spindle_speed}")

                self.commands_table.setItem(i, 2, QTableWidgetItem(" ".join(params)))

                # التعليق
                self.commands_table.setItem(i, 3, QTableWidgetItem(cmd.comment))

            # تحسين عرض الجدول
            self.commands_table.resizeColumnsToContents()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحميل المعاينة: {e}")


class CNCStatusMonitorDialog(QDialog):
    """حوار مراقبة حالة ماكينات CNC"""

    def __init__(self, parent=None, machines=None, communicators=None):
        super().__init__(parent)
        self.machines = machines or {}
        self.communicators = communicators or {}
        self.init_ui()
        self.setup_timer()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("مراقبة حالة ماكينات CNC")
        self.setModal(True)
        self.resize(900, 600)

        layout = QVBoxLayout(self)

        # جدول الماكينات
        self.machines_table = QTableWidget()
        self.machines_table.setColumnCount(6)
        self.machines_table.setHorizontalHeaderLabels([
            "الاسم", "الطراز", "الحالة", "المهمة الحالية", "التقدم", "آخر تحديث"
        ])

        layout.addWidget(self.machines_table)

        # معلومات مفصلة
        details_group = QGroupBox("تفاصيل الماكينة المختارة")
        details_layout = QVBoxLayout(details_group)

        self.details_text = QPlainTextEdit()
        self.details_text.setReadOnly(True)
        self.details_text.setMaximumHeight(150)

        details_layout.addWidget(self.details_text)
        layout.addWidget(details_group)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        refresh_btn = QPushButton("تحديث")
        refresh_btn.clicked.connect(self.refresh_status)

        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(self.close)

        buttons_layout.addWidget(refresh_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)

        # تحميل البيانات الأولية
        self.refresh_status()

    def setup_timer(self):
        """إعداد مؤقت التحديث التلقائي"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.refresh_status)
        self.update_timer.start(5000)  # تحديث كل 5 ثوان

    def refresh_status(self):
        """تحديث حالة الماكينات"""
        try:
            self.machines_table.setRowCount(len(self.machines))

            for i, (machine_id, machine) in enumerate(self.machines.items()):
                # اسم الماكينة
                self.machines_table.setItem(i, 0, QTableWidgetItem(machine.name))

                # الطراز
                self.machines_table.setItem(i, 1, QTableWidgetItem(machine.model))

                # الحالة مع لون
                status_item = QTableWidgetItem(machine.status)
                if machine.status == "متصل":
                    status_item.setBackground(QColor(200, 255, 200))
                elif machine.status == "يعمل":
                    status_item.setBackground(QColor(200, 200, 255))
                elif machine.status == "خطأ":
                    status_item.setBackground(QColor(255, 200, 200))
                else:
                    status_item.setBackground(QColor(240, 240, 240))

                self.machines_table.setItem(i, 2, status_item)

                # المهمة الحالية
                current_job = "لا توجد مهمة"
                if machine_id in self.communicators:
                    communicator = self.communicators[machine_id]
                    if hasattr(communicator, 'current_job') and communicator.current_job:
                        current_job = communicator.current_job.name

                self.machines_table.setItem(i, 3, QTableWidgetItem(current_job))

                # التقدم
                progress = "0%"
                if machine.status == "يعمل":
                    progress = "45%"  # قيمة تجريبية

                self.machines_table.setItem(i, 4, QTableWidgetItem(progress))

                # آخر تحديث
                last_update = datetime.now().strftime("%H:%M:%S")
                self.machines_table.setItem(i, 5, QTableWidgetItem(last_update))

            # تحسين عرض الجدول
            self.machines_table.resizeColumnsToContents()

        except Exception as e:
            print(f"خطأ في تحديث حالة الماكينات: {e}")

    def closeEvent(self, event):
        """إيقاف المؤقت عند إغلاق النافذة"""
        if hasattr(self, 'update_timer'):
            self.update_timer.stop()
        super().closeEvent(event)
