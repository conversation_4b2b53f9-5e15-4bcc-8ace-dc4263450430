#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام تكامل ماكينات CNC المتقدم - PySide6
Advanced CNC Machine Integration System - PySide6
"""

import os
import json
import uuid
import serial
import threading
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QTableWidget, QTableWidgetItem, QHeaderView, QLineEdit,
    QComboBox, QTextEdit, QDateEdit, QSpinBox, QDoubleSpinBox,
    QGroupBox, QFormLayout, QDialog, QDialogButtonBox,
    QMessageBox, QFileDialog, QTabWidget, QSplitter,
    QTreeWidget, QTreeWidgetItem, QProgressBar, Q<PERSON><PERSON><PERSON><PERSON>ox,
    QGridLayout, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, QPlainTextEdit, QListWidget
)
from PySide6.QtCore import Qt, QDate, Signal, QTimer, QThread, QObject
from PySide6.QtGui import QFont, QColor, QPixmap, QIcon, QTextCursor

from ui.modern_styles import ModernStyleManager
from ui.icons_manager import IconsManager


@dataclass
class CNCMachine:
    """ماكينة CNC"""
    id: str
    name: str
    model: str
    manufacturer: str
    serial_port: str
    baud_rate: int
    work_area_x: float  # منطقة العمل X (مم)
    work_area_y: float  # منطقة العمل Y (مم)
    work_area_z: float  # منطقة العمل Z (مم)
    spindle_speed_max: int  # أقصى سرعة للمغزل
    feed_rate_max: float  # أقصى معدل تغذية
    tool_changer: bool  # مغير الأدوات
    coolant_system: bool  # نظام التبريد
    status: str = "غير متصل"  # متصل، غير متصل، يعمل، متوقف، خطأ
    last_maintenance: str = ""
    notes: str = ""

    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())


@dataclass
class CNCJob:
    """مهمة CNC"""
    id: str
    name: str
    machine_id: str
    gcode_file: str
    material: str
    thickness: float
    estimated_time: int  # بالدقائق
    actual_time: int = 0
    status: str = "في الانتظار"  # في الانتظار، قيد التنفيذ، مكتمل، متوقف، خطأ
    progress: float = 0.0  # نسبة الإنجاز
    created_date: str = ""
    started_date: str = ""
    completed_date: str = ""
    notes: str = ""

    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.created_date:
            self.created_date = datetime.now().isoformat()


@dataclass
class GCodeCommand:
    """أمر G-Code"""
    line_number: int
    command: str
    x: Optional[float] = None
    y: Optional[float] = None
    z: Optional[float] = None
    feed_rate: Optional[float] = None
    spindle_speed: Optional[float] = None
    tool_number: Optional[int] = None
    comment: str = ""


class GCodeParser:
    """محلل G-Code"""

    def __init__(self):
        self.commands = []
        self.total_lines = 0
        self.estimated_time = 0

    def parse_file(self, file_path: str) -> List[GCodeCommand]:
        """تحليل ملف G-Code"""
        commands = []

        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                lines = file.readlines()

            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line or line.startswith(';'):
                    continue

                command = self.parse_line(line_num, line)
                if command:
                    commands.append(command)

            self.commands = commands
            self.total_lines = len(commands)
            self.estimate_execution_time()

            return commands

        except Exception as e:
            print(f"خطأ في تحليل ملف G-Code: {e}")
            return []

    def parse_line(self, line_number: int, line: str) -> Optional[GCodeCommand]:
        """تحليل سطر G-Code"""
        try:
            # إزالة التعليقات
            if ';' in line:
                comment_index = line.index(';')
                comment = line[comment_index+1:].strip()
                line = line[:comment_index].strip()
            else:
                comment = ""

            if not line:
                return None

            # تحليل الأوامر
            parts = line.split()
            command = parts[0] if parts else ""

            gcode_cmd = GCodeCommand(
                line_number=line_number,
                command=command,
                comment=comment
            )

            # استخراج المعاملات
            for part in parts[1:]:
                if part.startswith('X'):
                    gcode_cmd.x = float(part[1:])
                elif part.startswith('Y'):
                    gcode_cmd.y = float(part[1:])
                elif part.startswith('Z'):
                    gcode_cmd.z = float(part[1:])
                elif part.startswith('F'):
                    gcode_cmd.feed_rate = float(part[1:])
                elif part.startswith('S'):
                    gcode_cmd.spindle_speed = float(part[1:])
                elif part.startswith('T'):
                    gcode_cmd.tool_number = int(part[1:])

            return gcode_cmd

        except Exception as e:
            print(f"خطأ في تحليل السطر {line_number}: {e}")
            return None

    def estimate_execution_time(self):
        """تقدير وقت التنفيذ"""
        total_time = 0
        current_feed_rate = 1000  # معدل تغذية افتراضي
        last_position = [0, 0, 0]

        for cmd in self.commands:
            if cmd.feed_rate:
                current_feed_rate = cmd.feed_rate

            if cmd.command.startswith('G0') or cmd.command.startswith('G1'):
                # حساب المسافة والوقت
                new_position = [
                    cmd.x if cmd.x is not None else last_position[0],
                    cmd.y if cmd.y is not None else last_position[1],
                    cmd.z if cmd.z is not None else last_position[2]
                ]

                distance = ((new_position[0] - last_position[0])**2 +
                           (new_position[1] - last_position[1])**2 +
                           (new_position[2] - last_position[2])**2)**0.5

                time_for_move = distance / current_feed_rate * 60  # بالثواني
                total_time += time_for_move

                last_position = new_position

        self.estimated_time = int(total_time / 60)  # بالدقائق


class CNCCommunicator(QObject):
    """واجهة التواصل مع ماكينة CNC"""

    status_changed = Signal(str)
    progress_updated = Signal(float)
    message_received = Signal(str)
    error_occurred = Signal(str)

    def __init__(self, machine: CNCMachine):
        super().__init__()
        self.machine = machine
        self.serial_connection = None
        self.is_connected = False
        self.is_running = False
        self.current_job = None

    def connect_to_machine(self) -> bool:
        """الاتصال بالماكينة"""
        try:
            self.serial_connection = serial.Serial(
                port=self.machine.serial_port,
                baudrate=self.machine.baud_rate,
                timeout=1
            )

            self.is_connected = True
            self.status_changed.emit("متصل")
            self.message_received.emit("تم الاتصال بالماكينة بنجاح")
            return True

        except Exception as e:
            self.error_occurred.emit(f"فشل الاتصال: {str(e)}")
            return False

    def disconnect_from_machine(self):
        """قطع الاتصال"""
        if self.serial_connection and self.serial_connection.is_open:
            self.serial_connection.close()

        self.is_connected = False
        self.status_changed.emit("غير متصل")
        self.message_received.emit("تم قطع الاتصال")

    def send_command(self, command: str) -> bool:
        """إرسال أمر للماكينة"""
        if not self.is_connected or not self.serial_connection:
            self.error_occurred.emit("الماكينة غير متصلة")
            return False

        try:
            self.serial_connection.write(f"{command}\n".encode())
            response = self.serial_connection.readline().decode().strip()
            self.message_received.emit(f"أرسل: {command} | استقبل: {response}")
            return True

        except Exception as e:
            self.error_occurred.emit(f"خطأ في الإرسال: {str(e)}")
            return False

    def start_job(self, job: CNCJob, gcode_commands: List[GCodeCommand]):
        """بدء تنفيذ مهمة"""
        if not self.is_connected:
            self.error_occurred.emit("الماكينة غير متصلة")
            return

        self.current_job = job
        self.is_running = True
        self.status_changed.emit("يعمل")

        # تشغيل المهمة في خيط منفصل
        self.job_thread = threading.Thread(target=self._execute_job, args=(gcode_commands,))
        self.job_thread.start()

    def _execute_job(self, commands: List[GCodeCommand]):
        """تنفيذ المهمة"""
        try:
            total_commands = len(commands)

            for i, cmd in enumerate(commands):
                if not self.is_running:
                    break

                # إرسال الأمر
                success = self.send_command(cmd.command)
                if not success:
                    break

                # تحديث التقدم
                progress = (i + 1) / total_commands * 100
                self.progress_updated.emit(progress)

                # انتظار قصير
                time.sleep(0.1)

            if self.is_running:
                self.status_changed.emit("مكتمل")
                self.message_received.emit("تم إنجاز المهمة بنجاح")
            else:
                self.status_changed.emit("متوقف")
                self.message_received.emit("تم إيقاف المهمة")

        except Exception as e:
            self.error_occurred.emit(f"خطأ في تنفيذ المهمة: {str(e)}")
            self.status_changed.emit("خطأ")

        finally:
            self.is_running = False

    def stop_job(self):
        """إيقاف المهمة"""
        self.is_running = False
        self.send_command("M0")  # أمر الإيقاف
        self.status_changed.emit("متوقف")

    def emergency_stop(self):
        """إيقاف طارئ"""
        self.is_running = False
        self.send_command("M112")  # إيقاف طارئ
        self.status_changed.emit("إيقاف طارئ")
        self.error_occurred.emit("تم تفعيل الإيقاف الطارئ")


class AdvancedCNCManagerWidget(QWidget):
    """واجهة إدارة ماكينات CNC المتقدمة"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.machines = {}  # قاموس الماكينات
        self.communicators = {}  # قاموس المتصلين
        self.current_machine_id = None
        self.gcode_parser = GCodeParser()
        self.init_ui()
        self.load_machines()
        self.init_default_machines()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)

        # شريط الأدوات
        self.create_toolbar(layout)

        # المحتوى الرئيسي
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # قائمة الماكينات
        self.create_machines_panel(main_splitter)

        # لوحة التحكم
        self.create_control_panel(main_splitter)

        # تعيين النسب
        main_splitter.setSizes([300, 700])

        layout.addWidget(main_splitter)

        # شريط الحالة
        self.create_status_bar(layout)

    def create_toolbar(self, parent_layout):
        """إنشاء شريط الأدوات"""
        toolbar_widget = QWidget()
        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(0, 0, 0, 0)

        # أزرار الإدارة
        add_machine_btn = QPushButton("إضافة ماكينة")
        add_machine_btn.setIcon(IconsManager.get_standard_icon('add'))
        add_machine_btn.clicked.connect(self.add_new_machine)

        load_gcode_btn = QPushButton("تحميل G-Code")
        load_gcode_btn.setIcon(IconsManager.get_standard_icon('open'))
        load_gcode_btn.clicked.connect(self.load_gcode_file)

        emergency_stop_btn = QPushButton("إيقاف طارئ")
        emergency_stop_btn.setIcon(IconsManager.get_standard_icon('stop'))
        emergency_stop_btn.setStyleSheet("QPushButton { background-color: #e74c3c; color: white; font-weight: bold; }")
        emergency_stop_btn.clicked.connect(self.emergency_stop_all)

        toolbar_layout.addWidget(add_machine_btn)
        toolbar_layout.addWidget(load_gcode_btn)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(emergency_stop_btn)

        parent_layout.addWidget(toolbar_widget)

    def load_machines(self):
        """تحميل الماكينات المحفوظة"""
        # هنا يمكن تحميل الماكينات من ملف أو قاعدة بيانات
        # للتجربة، سنضيف ماكينة افتراضية
        default_machine = CNCMachine(
            id="default_001",
            name="ماكينة CNC الرئيسية",
            model="CNC-3018",
            manufacturer="Generic",
            serial_port="COM3",
            baud_rate=115200,
            work_area_x=300,
            work_area_y=180,
            work_area_z=45,
            spindle_speed_max=10000,
            feed_rate_max=1000,
            tool_changer=False,
            coolant_system=False
        )
    def add_new_machine(self):
        """إضافة ماكينة جديدة"""
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ إضافة ماكينة جديدة قريباً")

    def load_gcode_file(self):
        """تحميل ملف G-Code"""
        try:
            from PySide6.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "اختر ملف G-Code",
                "",
                "G-Code Files (*.gcode *.nc *.cnc);;All Files (*)"
            )

            if file_path:
                # قراءة ملف G-Code
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        gcode_content = f.read()

                    # تحليل أساسي للملف
                    lines = gcode_content.split('\n')
                    total_lines = len(lines)

                    # إحصائيات بسيطة
                    g_commands = len([line for line in lines if line.strip().startswith('G')])
                    m_commands = len([line for line in lines if line.strip().startswith('M')])

                    # عرض معلومات الملف
                    info_text = f"""
📁 تم تحميل ملف G-Code بنجاح!

📄 اسم الملف: {os.path.basename(file_path)}
📊 إجمالي الأسطر: {total_lines}
⚙️ أوامر G: {g_commands}
🔧 أوامر M: {m_commands}

✅ الملف جاهز للمعالجة
                    """

                    QMessageBox.information(self, "تحميل G-Code", info_text)

                except Exception as e:
                    QMessageBox.warning(self, "خطأ", f"فشل في قراءة ملف G-Code: {e}")

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تحميل ملف G-Code: {e}")

    def preview_gcode(self):
        """معاينة G-Code"""
        QMessageBox.information(self, "معاينة G-Code",
                               "سيتم تنفيذ معاينة G-Code المتقدمة قريباً\n"
                               "الميزات القادمة:\n"
                               "• معاينة ثلاثية الأبعاد لمسار القطع\n"
                               "• تحليل الوقت المتوقع\n"
                               "• فحص الأخطاء والتحذيرات")

    def start_cnc_job(self):
        """بدء مهمة CNC"""
        QMessageBox.information(self, "بدء المهمة",
                               "سيتم تنفيذ بدء مهمة CNC قريباً\n"
                               "الميزات القادمة:\n"
                               "• إرسال G-Code للماكينة\n"
                               "• مراقبة التقدم في الوقت الفعلي\n"
                               "• تحكم في السرعة والتوقف")

    def monitor_cnc_status(self):
        """مراقبة حالة CNC"""
        QMessageBox.information(self, "مراقبة الحالة",
                               "سيتم تنفيذ مراقبة حالة CNC قريباً\n"
                               "الميزات القادمة:\n"
                               "• عرض الحالة الحية للماكينة\n"
                               "• إحصائيات الأداء\n"
                               "• تنبيهات الأخطاء والصيانة")

    def emergency_stop_all(self):
        """إيقاف طوارئ لجميع الماكينات"""
        QMessageBox.warning(self, "إيقاف الطوارئ",
                           "🚨 تم تفعيل إيقاف الطوارئ!\n\n"
                           "سيتم إيقاف جميع الماكينات فوراً.\n"
                           "تأكد من سلامة المنطقة قبل إعادة التشغيل.")

    def reset_all_machines(self):
        """إعادة تعيين جميع الماكينات"""
        QMessageBox.information(self, "إعادة التعيين",
                               "سيتم تنفيذ إعادة تعيين الماكينات قريباً\n"
                               "الميزات القادمة:\n"
                               "• إعادة تعيين المحاور\n"
                               "• مسح الذاكرة\n"
                               "• إعادة المعايرة")

    def create_machines_panel(self, parent_splitter):
        """إنشاء لوحة الماكينات"""
        machines_widget = QWidget()
        layout = QVBoxLayout(machines_widget)

        # عنوان اللوحة
        title_label = QLabel("ماكينات CNC المتاحة")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; margin: 10px;")

        # قائمة الماكينات
        self.machines_list = QListWidget()

        # إضافة ماكينات تجريبية
        sample_machines = [
            "🔧 ماكينة CNC الرئيسية - متصلة",
            "⚙️ ماكينة التفريز - جاهزة",
            "🔩 ماكينة الخراطة - متوقفة",
            "⚡ ماكينة الليزر - قيد التشغيل"
        ]

        for machine in sample_machines:
            self.machines_list.addItem(machine)

        # أزرار التحكم
        buttons_widget = QWidget()
        buttons_layout = QHBoxLayout(buttons_widget)

        connect_btn = QPushButton("اتصال")
        connect_btn.clicked.connect(self.connect_machine)

        disconnect_btn = QPushButton("قطع الاتصال")
        disconnect_btn.clicked.connect(self.disconnect_machine)

        buttons_layout.addWidget(connect_btn)
        buttons_layout.addWidget(disconnect_btn)

        layout.addWidget(title_label)
        layout.addWidget(self.machines_list)
        layout.addWidget(buttons_widget)

        parent_splitter.addWidget(machines_widget)

    def connect_machine(self):
        """اتصال بالماكينة"""
        QMessageBox.information(self, "اتصال", "سيتم تنفيذ الاتصال بالماكينة قريباً")

    def disconnect_machine(self):
        """قطع الاتصال بالماكينة"""
        QMessageBox.information(self, "قطع الاتصال", "سيتم تنفيذ قطع الاتصال قريباً")

    def create_control_panel(self, parent_splitter):
        """إنشاء لوحة التحكم"""
        control_widget = QWidget()
        layout = QVBoxLayout(control_widget)

        # عنوان لوحة التحكم
        title_label = QLabel("لوحة التحكم الرئيسية")
        title_label.setStyleSheet("font-weight: bold; font-size: 16px; margin: 10px;")

        # أزرار التحكم الرئيسية
        main_controls_group = QGroupBox("التحكم الرئيسي")
        main_controls_layout = QVBoxLayout(main_controls_group)

        start_btn = QPushButton("🚀 بدء التشغيل")
        start_btn.setStyleSheet("QPushButton { background-color: #27AE60; color: white; padding: 10px; }")
        start_btn.clicked.connect(self.start_cnc_job)

        pause_btn = QPushButton("⏸️ إيقاف مؤقت")
        pause_btn.setStyleSheet("QPushButton { background-color: #F39C12; color: white; padding: 10px; }")

        stop_btn = QPushButton("⏹️ إيقاف")
        stop_btn.setStyleSheet("QPushButton { background-color: #E74C3C; color: white; padding: 10px; }")

        emergency_btn = QPushButton("🚨 إيقاف طوارئ")
        emergency_btn.setStyleSheet("QPushButton { background-color: #C0392B; color: white; padding: 10px; font-weight: bold; }")
        emergency_btn.clicked.connect(self.emergency_stop_all)

        main_controls_layout.addWidget(start_btn)
        main_controls_layout.addWidget(pause_btn)
        main_controls_layout.addWidget(stop_btn)
        main_controls_layout.addWidget(emergency_btn)

        # معلومات الحالة
        status_group = QGroupBox("معلومات الحالة")
        status_layout = QFormLayout(status_group)

        self.status_label = QLabel("جاهز")
        self.progress_label = QLabel("0%")
        self.time_label = QLabel("00:00:00")

        status_layout.addRow("الحالة:", self.status_label)
        status_layout.addRow("التقدم:", self.progress_label)
        status_layout.addRow("الوقت:", self.time_label)

        layout.addWidget(title_label)
        layout.addWidget(main_controls_group)
        layout.addWidget(status_group)
        layout.addStretch()

        parent_splitter.addWidget(control_widget)

    def create_status_bar(self, parent_layout):
        """إنشاء شريط الحالة"""
        status_widget = QWidget()
        status_layout = QHBoxLayout(status_widget)
        status_layout.setContentsMargins(5, 5, 5, 5)

        # معلومات الحالة
        self.connection_status_label = QLabel("🔴 غير متصل")
        self.machine_count_label = QLabel("الماكينات: 0")
        self.active_jobs_label = QLabel("المهام النشطة: 0")
        self.total_runtime_label = QLabel("وقت التشغيل: 00:00:00")

        # تنسيق التسميات
        for label in [self.connection_status_label, self.machine_count_label,
                     self.active_jobs_label, self.total_runtime_label]:
            label.setStyleSheet("""
                QLabel {
                    padding: 5px 10px;
                    border: 1px solid #BDC3C7;
                    border-radius: 3px;
                    background-color: #ECF0F1;
                    font-size: 12px;
                }
            """)

        status_layout.addWidget(self.connection_status_label)
        status_layout.addWidget(self.machine_count_label)
        status_layout.addWidget(self.active_jobs_label)
        status_layout.addWidget(self.total_runtime_label)
        status_layout.addStretch()

        parent_layout.addWidget(status_widget)

    def update_status_bar(self):
        """تحديث شريط الحالة"""
        try:
            # تحديث عدد الماكينات
            machine_count = len(getattr(self, 'machines', {}))
            self.machine_count_label.setText(f"الماكينات: {machine_count}")

            # تحديث حالة الاتصال
            if machine_count > 0:
                self.connection_status_label.setText("🟢 متصل")
                self.connection_status_label.setStyleSheet("""
                    QLabel {
                        padding: 5px 10px;
                        border: 1px solid #27AE60;
                        border-radius: 3px;
                        background-color: #D5EFDB;
                        color: #27AE60;
                        font-size: 12px;
                        font-weight: bold;
                    }
                """)
            else:
                self.connection_status_label.setText("🔴 غير متصل")
                self.connection_status_label.setStyleSheet("""
                    QLabel {
                        padding: 5px 10px;
                        border: 1px solid #E74C3C;
                        border-radius: 3px;
                        background-color: #FADBD8;
                        color: #E74C3C;
                        font-size: 12px;
                        font-weight: bold;
                    }
                """)

        except Exception as e:
            print(f"خطأ في تحديث شريط الحالة: {e}")


    def init_default_machines(self):
        """تهيئة الماكينات الافتراضية"""
        try:
            # إنشاء ماكينة افتراضية
            default_machine = CNCMachine(
                id="default_001",
                name="ماكينة CNC الافتراضية",
                model="CNC-3018",
                manufacturer="Generic",
                serial_port="COM3",
                baud_rate=115200,
                work_area_x=300,
                work_area_y=180,
                work_area_z=45,
                spindle_speed_max=10000,
                feed_rate_max=1000,
                tool_changer=False,
                coolant_system=False
            )
            self.machines[default_machine.id] = default_machine
            print("✅ تم تهيئة الماكينة الافتراضية")
        except Exception as e:
            print(f"⚠️ تعذر تهيئة الماكينة الافتراضية: {e}")
