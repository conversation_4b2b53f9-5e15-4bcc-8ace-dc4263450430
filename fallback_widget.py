#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة احتياطية بسيطة
Simple Fallback Widget
"""

from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QMessageBox
from PySide6.QtCore import Qt

class FallbackWidget(QWidget):
    """واجهة احتياطية بسيطة"""
    
    def __init__(self, module_name="غير محدد", parent=None):
        super().__init__(parent)
        self.module_name = module_name
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        
        # رسالة الحالة
        status_label = QLabel(f"🚧 وحدة {self.module_name} قيد التطوير")
        status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        status_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #666;
                padding: 20px;
                border: 2px dashed #ccc;
                border-radius: 10px;
                background-color: #f9f9f9;
            }
        """)
        
        # زر المعلومات
        info_btn = QPushButton("معلومات الوحدة")
        info_btn.clicked.connect(self.show_info)
        
        layout.addStretch()
        layout.addWidget(status_label)
        layout.addWidget(info_btn)
        layout.addStretch()
    
    def show_info(self):
        """عرض معلومات الوحدة"""
        QMessageBox.information(
            self, 
            f"معلومات وحدة {self.module_name}",
            f"وحدة {self.module_name} قيد التطوير حالياً.\n"
            "سيتم إضافة الميزات الكاملة في التحديثات القادمة.\n\n"
            "الميزات المتاحة حالياً:\n"
            "• واجهة أساسية\n"
            "• دعم العملة الليبية\n"
            "• تكامل مع النظام الرئيسي"
        )
