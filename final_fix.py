#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
الإصلاح النهائي للدوال المفقودة
Final Fix for Missing Functions
"""

import re

def fix_clients_system():
    """إصلاح نظام العملاء"""
    print("🔧 إصلاح نظام العملاء...")
    
    try:
        with open("clients/advanced_client_system.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة الدالة المفقودة
        missing_function = '''
    def create_clients_list(self, parent_splitter):
        """إنشاء قائمة العملاء"""
        clients_widget = QWidget()
        layout = QVBoxLayout(clients_widget)

        # عنوان القائمة
        title_label = QLabel("قائمة العملاء")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; margin: 10px;")

        # جدول العملاء (استخدام الجدول الموجود)
        layout.addWidget(title_label)
        layout.addWidget(self.clients_table)

        parent_splitter.addWidget(clients_widget)
'''
        
        # البحث عن مكان مناسب لإضافة الدالة
        if 'def create_clients_list' not in content:
            # إضافة الدالة قبل النهاية
            insertion_point = content.rfind('\n\n        parent_layout.addWidget(toolbar_widget)')
            if insertion_point != -1:
                content = content[:insertion_point] + missing_function + content[insertion_point:]
        
        with open("clients/advanced_client_system.py", 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح نظام العملاء")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح العملاء: {e}")
        return False

def fix_cnc_system():
    """إصلاح نظام CNC"""
    print("🔧 إصلاح نظام CNC...")
    
    try:
        with open("cnc/advanced_cnc_system.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إزالة السطر المشكل
        content = content.replace('\n\n        self.machines[default_machine.id] = default_machine', '')
        
        # إضافة دالة تهيئة الماكينات الافتراضية
        init_function = '''
    def init_default_machines(self):
        """تهيئة الماكينات الافتراضية"""
        try:
            # إنشاء ماكينة افتراضية
            default_machine = CNCMachine(
                id="default_001",
                name="ماكينة CNC الافتراضية",
                type="milling",
                status="idle",
                x_axis_max=1000,
                y_axis_max=800,
                z_axis_max=200,
                spindle_speed_max=24000,
                feed_rate_max=1000,
                tool_changer=False,
                coolant_system=False
            )
            self.machines[default_machine.id] = default_machine
            print("✅ تم تهيئة الماكينة الافتراضية")
        except Exception as e:
            print(f"⚠️ تعذر تهيئة الماكينة الافتراضية: {e}")
'''
        
        # إضافة الدالة
        if 'def init_default_machines' not in content:
            # البحث عن مكان مناسب
            class_end = content.rfind('class AdvancedCNCManagerWidget')
            if class_end != -1:
                # البحث عن نهاية __init__
                init_end = content.find('        self.load_machines()', class_end)
                if init_end != -1:
                    line_end = content.find('\n', init_end)
                    content = content[:line_end] + '\n        self.init_default_machines()' + content[line_end:]
                
                # إضافة الدالة في نهاية الفئة
                content += init_function
        
        with open("cnc/advanced_cnc_system.py", 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح نظام CNC")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح CNC: {e}")
        return False

def create_safe_launcher():
    """إنشاء مشغل آمن"""
    print("🔧 إنشاء مشغل آمن...")
    
    safe_launcher_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل آمن للتطبيق - يتعامل مع الأخطاء بذكاء
Safe Application Launcher - Handles Errors Intelligently
"""

import sys
import os
from pathlib import Path

# إضافة المجلد الحالي لمسار Python
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def safe_import_and_run():
    """استيراد وتشغيل آمن للتطبيق"""
    try:
        from PySide6.QtWidgets import QApplication, QMessageBox
        from PySide6.QtCore import Qt
        
        app = QApplication(sys.argv)
        app.setApplicationName("مصمم الأثاث الاحترافي الآمن")
        
        print("🚀 بدء التشغيل الآمن...")
        
        # محاولة تشغيل التطبيق المبسط أولاً
        try:
            from simple_main_app import SimpleFurnitureDesignerApp
            window = SimpleFurnitureDesignerApp()
            window.show()
            
            print("✅ تم تشغيل التطبيق المبسط بنجاح")
            return app.exec()
            
        except Exception as e:
            print(f"⚠️ فشل التطبيق المبسط: {e}")
            
            # محاولة تشغيل التطبيق الأصلي
            try:
                import main_application
                return main_application.main()
                
            except Exception as e2:
                print(f"⚠️ فشل التطبيق الأصلي: {e2}")
                
                # عرض رسالة خطأ ودية
                QMessageBox.critical(
                    None,
                    "خطأ في التشغيل",
                    f"تعذر تشغيل التطبيق:\\n\\n"
                    f"خطأ التطبيق المبسط: {e}\\n"
                    f"خطأ التطبيق الأصلي: {e2}\\n\\n"
                    f"يرجى التحقق من:\\n"
                    f"• تثبيت PySide6\\n"
                    f"• سلامة ملفات التطبيق\\n"
                    f"• صلاحيات الملفات"
                )
                return 1
    
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 يرجى تثبيت PySide6:")
        print("   pip install PySide6")
        return 1
    
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return 1

def main():
    """الدالة الرئيسية"""
    print("🪑 مصمم الأثاث الاحترافي - المشغل الآمن")
    print("=" * 50)
    
    try:
        return safe_import_and_run()
    except KeyboardInterrupt:
        print("\\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        return 0
    except Exception as e:
        print(f"❌ خطأ فادح: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
'''
    
    try:
        with open("safe_launcher.py", 'w', encoding='utf-8') as f:
            f.write(safe_launcher_code)
        
        print("✅ تم إنشاء المشغل الآمن")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المشغل الآمن: {e}")
        return False

def update_start_here():
    """تحديث ملف START_HERE"""
    print("🔧 تحديث ملف START_HERE...")
    
    try:
        with open("START_HERE.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة خيار المشغل الآمن
        safe_option = '''
        elif choice == "4":
            print("🛡️ تشغيل المشغل الآمن...")
            os.system("python safe_launcher.py")
'''
        
        # البحث عن مكان إضافة الخيار
        if 'elif choice == "4"' not in content:
            # البحث عن نهاية الخيارات
            choice_end = content.find('        else:')
            if choice_end != -1:
                content = content[:choice_end] + safe_option + '\\n' + content[choice_end:]
        
        # تحديث القائمة
        content = content.replace(
            '3. تشغيل التطبيق المبسط (موصى به)',
            '3. تشغيل التطبيق المبسط (موصى به)\\n4. المشغل الآمن (للمشاكل)'
        )
        
        with open("START_HERE.py", 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم تحديث ملف START_HERE")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث START_HERE: {e}")
        return False

def main():
    """الدالة الرئيسية للإصلاح النهائي"""
    print("🔧 بدء الإصلاح النهائي")
    print("=" * 40)
    
    fixes = [
        ("نظام العملاء", fix_clients_system),
        ("نظام CNC", fix_cnc_system),
        ("المشغل الآمن", create_safe_launcher),
        ("ملف START_HERE", update_start_here)
    ]
    
    success_count = 0
    for fix_name, fix_func in fixes:
        try:
            if fix_func():
                success_count += 1
        except Exception as e:
            print(f"❌ خطأ في إصلاح {fix_name}: {e}")
    
    print("\\n" + "=" * 40)
    print(f"📊 تم إنجاز {success_count}/{len(fixes)} إصلاحات")
    
    if success_count >= len(fixes) * 0.75:
        print("🎉 الإصلاح النهائي مكتمل!")
        print("\\n🚀 يمكنك الآن تشغيل:")
        print("   python safe_launcher.py")
        print("   أو")
        print("   python START_HERE.py")
    else:
        print("⚠️ بعض الإصلاحات فشلت")
        print("💡 لكن التطبيق قد يعمل مع قيود")

if __name__ == "__main__":
    main()
