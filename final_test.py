#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي شامل للتطبيق بعد الإصلاحات
Final Comprehensive Test After Fixes
"""

import sys
import os
from pathlib import Path

# إضافة المجلد الحالي لمسار Python
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_pyside6_imports():
    """اختبار استيرادات PySide6"""
    print("1️⃣ اختبار استيرادات PySide6...")
    try:
        from PySide6.QtWidgets import QApplication, QWidget, QPushButton
        from PySide6.QtCore import Qt, Signal
        from PySide6.QtGui import QIcon, QFont
        print("✅ PySide6 يعمل بشكل صحيح")
        return True
    except ImportError as e:
        print(f"❌ خطأ في PySide6: {e}")
        return False

def test_currency_formatter():
    """اختبار منسق العملة الليبية"""
    print("\n2️⃣ اختبار منسق العملة الليبية...")
    try:
        from ui.modern_styles import CurrencyFormatter
        
        # اختبارات مختلفة
        tests = [
            (1234.56, "1,234.56 د.ل"),
            (1000000, "1.0م د.ل"),
            (5000, "5.0ألف د.ل"),
            (100, "100 د.ل")
        ]
        
        for amount, expected_short in tests:
            formatted = CurrencyFormatter.format_currency(amount)
            short_format = CurrencyFormatter.format_currency_short(amount)
            print(f"   {amount} → {formatted} | {short_format}")
        
        print("✅ منسق العملة يعمل بشكل صحيح")
        return True
    except Exception as e:
        print(f"❌ خطأ في منسق العملة: {e}")
        return False

def test_core_modules():
    """اختبار الوحدات الأساسية"""
    print("\n3️⃣ اختبار الوحدات الأساسية...")
    
    modules = [
        ("المعاين ثلاثي الأبعاد", "models.advanced_3d_viewer", "Advanced3DViewer"),
        ("نظام العملاء", "clients.advanced_client_system", "AdvancedClientManagerWidget"),
        ("نظام المخزون", "inventory.advanced_inventory_system", "AdvancedInventoryManagerWidget"),
        ("نظام CNC", "cnc.advanced_cnc_system", "AdvancedCNCManagerWidget"),
        ("نظام التقارير", "reports.advanced_reports_system", "AdvancedReportsWidget"),
        ("الأنماط الحديثة", "ui.modern_styles", "ModernStyleManager"),
        ("مدير الأيقونات", "ui.icons_manager", "IconsManager")
    ]
    
    success_count = 0
    for name, module_path, class_name in modules:
        try:
            module = __import__(module_path, fromlist=[class_name])
            getattr(module, class_name)
            print(f"✅ {name}: متاح")
            success_count += 1
        except Exception as e:
            print(f"⚠️ {name}: مشكلة - {e}")
    
    print(f"\n📊 النتيجة: {success_count}/{len(modules)} وحدة تعمل بشكل صحيح")
    return success_count >= len(modules) * 0.7  # 70% نجاح مقبول

def test_fixed_modules():
    """اختبار الوحدات المُصلحة"""
    print("\n4️⃣ اختبار الوحدات المُصلحة...")
    
    fixed_modules = [
        ("السحب والإفلات", "drag_drop.file_handler", "DragDropFileManager"),
        ("عروض الأسعار", "quotations.quotation_manager", "QuotationManager"),
        ("حاسبة التكلفة", "calculator.cost_calculator", "CostCalculator"),
        ("النسخ الاحتياطي", "backup.backup_manager", "BackupManager")
    ]
    
    success_count = 0
    for name, module_path, class_name in fixed_modules:
        try:
            module = __import__(module_path, fromlist=[class_name])
            getattr(module, class_name)
            print(f"✅ {name}: تم إصلاحه وهو متاح")
            success_count += 1
        except Exception as e:
            print(f"⚠️ {name}: لا يزال به مشكلة - {e}")
    
    print(f"\n📊 النتيجة: {success_count}/{len(fixed_modules)} وحدة مُصلحة")
    return success_count >= len(fixed_modules) * 0.5  # 50% نجاح مقبول للوحدات المُصلحة

def test_fallback_widget():
    """اختبار الواجهة الاحتياطية"""
    print("\n5️⃣ اختبار الواجهة الاحتياطية...")
    try:
        from fallback_widget import FallbackWidget
        print("✅ الواجهة الاحتياطية متاحة")
        return True
    except Exception as e:
        print(f"❌ خطأ في الواجهة الاحتياطية: {e}")
        return False

def test_application_startup():
    """اختبار بدء تشغيل التطبيق"""
    print("\n6️⃣ اختبار بدء تشغيل التطبيق...")
    try:
        # محاولة استيراد التطبيق الرئيسي
        import main_application
        print("✅ التطبيق الرئيسي قابل للاستيراد")
        
        # اختبار إنشاء QApplication
        from PySide6.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("✅ QApplication يعمل بشكل صحيح")
        return True
    except Exception as e:
        print(f"⚠️ مشكلة في بدء التشغيل: {e}")
        return False

def test_database_connections():
    """اختبار اتصالات قاعدة البيانات"""
    print("\n7️⃣ اختبار اتصالات قاعدة البيانات...")
    try:
        # اختبار قاعدة بيانات العملاء
        from clients.advanced_client_system import ClientDatabaseManager
        client_db = ClientDatabaseManager()
        print("✅ قاعدة بيانات العملاء متاحة")
        
        # اختبار قاعدة بيانات المخزون
        from inventory.advanced_inventory_system import InventoryDatabaseManager
        inventory_db = InventoryDatabaseManager()
        print("✅ قاعدة بيانات المخزون متاحة")
        
        return True
    except Exception as e:
        print(f"⚠️ مشكلة في قواعد البيانات: {e}")
        return False

def run_comprehensive_test():
    """تشغيل الاختبار الشامل"""
    print("🧪 بدء الاختبار النهائي الشامل")
    print("=" * 60)
    
    tests = [
        ("PySide6", test_pyside6_imports),
        ("منسق العملة", test_currency_formatter),
        ("الوحدات الأساسية", test_core_modules),
        ("الوحدات المُصلحة", test_fixed_modules),
        ("الواجهة الاحتياطية", test_fallback_widget),
        ("بدء التشغيل", test_application_startup),
        ("قواعد البيانات", test_database_connections)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    # تقييم النتائج
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    success_rate = (passed / total) * 100
    print(f"\n🎯 معدل النجاح: {passed}/{total} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print("🎉 ممتاز! التطبيق جاهز للاستخدام")
        print("\n🚀 لتشغيل التطبيق:")
        print("   python run_advanced_furniture_designer.py")
        print("   أو")
        print("   python START_HERE.py")
    elif success_rate >= 60:
        print("✅ جيد! التطبيق يعمل مع بعض القيود")
        print("\n💡 يُنصح بـ:")
        print("   python run_safe.py")
    else:
        print("⚠️ يحتاج إلى مزيد من الإصلاحات")
        print("\n🔧 جرب:")
        print("   python install_requirements.py")
        print("   ثم")
        print("   python fix_remaining_issues.py")
    
    return success_rate >= 60

def main():
    """الدالة الرئيسية"""
    try:
        success = run_comprehensive_test()
        
        print(f"\n{'='*60}")
        if success:
            print("🎯 الاختبار النهائي مكتمل بنجاح!")
            print("✅ التطبيق جاهز للاستخدام مع PySide6 والعملة الليبية")
        else:
            print("⚠️ الاختبار النهائي يحتاج إلى مراجعة")
            print("💡 راجع الأخطاء أعلاه وأعد المحاولة")
        
        return success
        
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع في الاختبار: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    print(f"\n{'='*60}")
    print("🏁 انتهى الاختبار النهائي")
    
    if not success:
        input("\n⏸️ اضغط Enter للخروج...")
    
    sys.exit(0 if success else 1)
