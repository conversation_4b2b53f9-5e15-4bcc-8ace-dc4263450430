#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مثبت المتطلبات التدريجي لتطبيق مصمم الأثاث الاحترافي
Progressive Requirements Installer for Professional Furniture Designer
"""

import subprocess
import sys
import os

def install_package(package_name, description=""):
    """تثبيت مكتبة واحدة مع معالجة الأخطاء"""
    try:
        print(f"🔄 تثبيت {package_name}...")
        if description:
            print(f"   📝 {description}")
        
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True, check=True)
        
        print(f"✅ تم تثبيت {package_name} بنجاح")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل تثبيت {package_name}")
        print(f"   خطأ: {e.stderr}")
        return False
    except Exception as e:
        print(f"❌ خطأ غير متوقع في تثبيت {package_name}: {e}")
        return False

def main():
    """الدالة الرئيسية للتثبيت التدريجي"""
    print("🚀 مثبت المتطلبات التدريجي - مصمم الأثاث الاحترافي المتقدم")
    print("=" * 60)
    
    # المكتبات الأساسية (مطلوبة)
    core_packages = [
        ("PySide6>=6.4.0", "إطار العمل الأساسي للواجهة الرسومية"),
        ("numpy>=1.20.0", "مكتبة الحوسبة العلمية الأساسية"),
        ("pandas>=1.3.0", "معالجة وتحليل البيانات"),
        ("matplotlib>=3.5.0", "الرسوم البيانية والتصور"),
        ("Pillow>=8.0.0", "معالجة الصور"),
        ("trimesh>=4.0.0", "معالجة النماذج ثلاثية الأبعاد"),
        ("openpyxl>=3.0.0", "تصدير ملفات Excel"),
        ("python-dateutil>=2.8.0", "معالجة التاريخ والوقت")
    ]
    
    # المكتبات المتقدمة (اختيارية)
    advanced_packages = [
        ("scipy>=1.7.0", "حوسبة علمية متقدمة"),
        ("seaborn>=0.11.0", "رسوم بيانية متقدمة"),
        ("reportlab>=3.6.0", "إنشاء ملفات PDF"),
        ("arabic-reshaper>=3.0.0", "دعم النصوص العربية في PDF"),
        ("python-bidi>=0.4.0", "ترتيب النصوص ثنائية الاتجاه"),
        ("pyserial>=3.5", "التواصل مع أجهزة CNC"),
        ("psutil>=5.8.0", "معلومات النظام"),
        ("qrcode>=7.3.0", "إنشاء رموز QR"),
        ("sympy>=1.9.0", "عمليات رياضية رمزية")
    ]
    
    print("\n📦 المرحلة 1: تثبيت المكتبات الأساسية")
    print("-" * 40)
    
    core_success = 0
    core_failed = []
    
    for package, description in core_packages:
        if install_package(package, description):
            core_success += 1
        else:
            core_failed.append(package)
        print()  # سطر فارغ للوضوح
    
    print(f"\n📊 نتائج المرحلة الأولى:")
    print(f"✅ نجح: {core_success}/{len(core_packages)}")
    if core_failed:
        print(f"❌ فشل: {len(core_failed)}")
        print("   المكتبات الفاشلة:", ", ".join(core_failed))
    
    # التحقق من إمكانية المتابعة
    if core_success < len(core_packages) * 0.7:  # إذا فشل أكثر من 30%
        print("\n⚠️ فشل في تثبيت معظم المكتبات الأساسية!")
        print("💡 يرجى:")
        print("   1. التأكد من اتصال الإنترنت")
        print("   2. تحديث pip: python -m pip install --upgrade pip")
        print("   3. المحاولة مرة أخرى")
        return False
    
    # سؤال المستخدم عن المكتبات المتقدمة
    print(f"\n🤔 هل تريد تثبيت المكتبات المتقدمة؟")
    print("   (ستحصل على ميزات إضافية مثل PDF والتقارير المتقدمة)")
    
    try:
        choice = input("   اكتب 'y' للموافقة أو 'n' للتخطي [y/N]: ").lower().strip()
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إلغاء التثبيت بواسطة المستخدم")
        return False
    
    if choice in ['y', 'yes', 'نعم', 'ن']:
        print("\n📦 المرحلة 2: تثبيت المكتبات المتقدمة")
        print("-" * 40)
        
        advanced_success = 0
        advanced_failed = []
        
        for package, description in advanced_packages:
            if install_package(package, description):
                advanced_success += 1
            else:
                advanced_failed.append(package)
            print()  # سطر فارغ للوضوح
        
        print(f"\n📊 نتائج المرحلة الثانية:")
        print(f"✅ نجح: {advanced_success}/{len(advanced_packages)}")
        if advanced_failed:
            print(f"❌ فشل: {len(advanced_failed)}")
            print("   المكتبات الفاشلة:", ", ".join(advanced_failed))
    else:
        print("\n⏭️ تم تخطي المكتبات المتقدمة")
        advanced_success = 0
        advanced_failed = []
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    print("🎯 ملخص التثبيت:")
    print(f"✅ المكتبات الأساسية: {core_success}/{len(core_packages)}")
    if choice in ['y', 'yes', 'نعم', 'ن']:
        print(f"✅ المكتبات المتقدمة: {advanced_success}/{len(advanced_packages)}")
    
    total_success = core_success + advanced_success
    total_packages = len(core_packages) + (len(advanced_packages) if choice in ['y', 'yes', 'نعم', 'ن'] else 0)
    
    if total_success == total_packages:
        print("\n🎉 تم تثبيت جميع المكتبات بنجاح!")
        print("✅ التطبيق جاهز للتشغيل")
        print("\n🚀 لتشغيل التطبيق:")
        print("   python run_advanced_furniture_designer.py")
        print("\n🧪 لاختبار التثبيت:")
        print("   python run_migration_test.py")
    elif core_success >= len(core_packages) * 0.8:
        print("\n✅ تم تثبيت معظم المكتبات بنجاح!")
        print("⚠️ بعض الميزات المتقدمة قد لا تعمل")
        print("💡 يمكنك تشغيل التطبيق والميزات الأساسية ستعمل")
        print("\n🚀 لتشغيل التطبيق:")
        print("   python run_advanced_furniture_designer.py")
    else:
        print("\n❌ فشل في تثبيت المكتبات الأساسية!")
        print("💡 يرجى مراجعة الأخطاء أعلاه وإعادة المحاولة")
        return False
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إلغاء التثبيت بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
