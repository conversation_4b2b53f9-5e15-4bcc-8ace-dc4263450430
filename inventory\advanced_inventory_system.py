#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام تتبع المخزون المتقدم - PySide6
Advanced Inventory Tracking System - PySide6
"""

import sqlite3
import json
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QTableWidget, QTableWidgetItem, QHeaderView, QLineEdit,
    QComboBox, QTextEdit, QDateEdit, QSpinBox, QDoubleSpinBox,
    QGroupBox, QFormLayout, QDialog, QDialogButtonBox,
    QMessageBox, QFileDialog, QTabWidget, QSplitter,
    QTreeWidget, QTreeWidgetItem, QProgressBar, QCheckBox,
    Q<PERSON>ridLayout, QFrame
)
from PySide6.QtCore import Qt, QDate, Signal, QTimer, QThread
from PySide6.QtGui import QFont, QColor, QPixmap, QIcon

from ui.modern_styles import ModernStyleManager
from ui.icons_manager import IconsManager


@dataclass
class InventoryItem:
    """عنصر المخزون"""
    id: str
    name: str
    category: str
    subcategory: str
    description: str
    unit: str  # وحدة القياس (متر، قطعة، كيلو، إلخ)
    current_stock: float
    minimum_stock: float  # الحد الأدنى للتنبيه
    maximum_stock: float  # الحد الأقصى
    unit_cost: float  # تكلفة الوحدة (د.ل)
    selling_price: float  # سعر البيع (د.ل)
    supplier: str  # المورد
    location: str  # موقع التخزين
    barcode: str  # الباركود
    notes: str
    created_date: str
    last_updated: str
    status: str = "متاح"  # متاح، نفد، معلق

    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.created_date:
            self.created_date = datetime.now().isoformat()
        if not self.last_updated:
            self.last_updated = datetime.now().isoformat()


@dataclass
class StockMovement:
    """حركة المخزون"""
    id: str
    item_id: str
    movement_type: str  # دخول، خروج، تعديل، تلف
    quantity: float
    unit_cost: float  # تكلفة الوحدة (د.ل)
    total_cost: float  # التكلفة الإجمالية (د.ل)
    reference_number: str  # رقم المرجع (فاتورة، طلب، إلخ)
    notes: str
    created_date: str
    created_by: str

    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.created_date:
            self.created_date = datetime.now().isoformat()


@dataclass
class Supplier:
    """المورد"""
    id: str
    name: str
    company: str
    phone: str
    email: str
    address: str
    payment_terms: int
    notes: str
    created_date: str
    status: str = "نشط"

    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.created_date:
            self.created_date = datetime.now().isoformat()


class InventoryDatabaseManager:
    """مدير قاعدة بيانات المخزون"""

    def __init__(self, db_path: str = "inventory_advanced.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """تهيئة قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # جدول عناصر المخزون
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventory_items (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                category TEXT,
                subcategory TEXT,
                description TEXT,
                unit TEXT,
                current_stock REAL DEFAULT 0,
                minimum_stock REAL DEFAULT 0,
                maximum_stock REAL DEFAULT 0,
                unit_cost REAL DEFAULT 0,
                selling_price REAL DEFAULT 0,
                supplier TEXT,
                location TEXT,
                barcode TEXT,
                notes TEXT,
                created_date TEXT,
                last_updated TEXT,
                status TEXT DEFAULT 'متاح'
            )
        ''')

        # جدول حركات المخزون
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_movements (
                id TEXT PRIMARY KEY,
                item_id TEXT,
                movement_type TEXT,
                quantity REAL,
                unit_cost REAL,
                total_cost REAL,
                reference_number TEXT,
                notes TEXT,
                created_date TEXT,
                created_by TEXT,
                FOREIGN KEY (item_id) REFERENCES inventory_items (id)
            )
        ''')

        # جدول الموردين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS suppliers (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                company TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                payment_terms INTEGER DEFAULT 30,
                notes TEXT,
                created_date TEXT,
                status TEXT DEFAULT 'نشط'
            )
        ''')

        # جدول فئات المخزون
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventory_categories (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                parent_category TEXT,
                description TEXT,
                created_date TEXT
            )
        ''')

        # جدول التنبيهات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventory_alerts (
                id TEXT PRIMARY KEY,
                item_id TEXT,
                alert_type TEXT,
                message TEXT,
                created_date TEXT,
                is_read INTEGER DEFAULT 0,
                FOREIGN KEY (item_id) REFERENCES inventory_items (id)
            )
        ''')

        # جدول طلبات الشراء
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS purchase_orders (
                id TEXT PRIMARY KEY,
                supplier_id TEXT,
                order_date TEXT,
                expected_delivery TEXT,
                total_amount REAL,  -- المبلغ الإجمالي (د.ل)
                status TEXT,
                notes TEXT,
                created_date TEXT,
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
            )
        ''')

        conn.commit()
        conn.close()

    def add_inventory_item(self, item: InventoryItem) -> bool:
        """إضافة عنصر جديد للمخزون"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO inventory_items
                (id, name, category, subcategory, description, unit, current_stock,
                 minimum_stock, maximum_stock, unit_cost, selling_price, supplier,
                 location, barcode, notes, created_date, last_updated, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                item.id, item.name, item.category, item.subcategory, item.description,
                item.unit, item.current_stock, item.minimum_stock, item.maximum_stock,
                item.unit_cost, item.selling_price, item.supplier, item.location,
                item.barcode, item.notes, item.created_date, item.last_updated, item.status
            ))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"خطأ في إضافة عنصر المخزون: {e}")
            return False

    def get_all_inventory_items(self) -> List[Dict[str, Any]]:
        """الحصول على جميع عناصر المخزون"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM inventory_items ORDER BY name')
            items_data = cursor.fetchall()

            items = []
            columns = [desc[0] for desc in cursor.description]

            for item_data in items_data:
                item_dict = dict(zip(columns, item_data))
                items.append(item_dict)

            conn.close()
            return items

        except Exception as e:
            print(f"خطأ في تحميل عناصر المخزون: {e}")
            return []

    def add_stock_movement(self, movement: StockMovement) -> bool:
        """إضافة حركة مخزون"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إضافة الحركة
            cursor.execute('''
                INSERT INTO stock_movements
                (id, item_id, movement_type, quantity, unit_cost, total_cost,
                 reference_number, notes, created_date, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                movement.id, movement.item_id, movement.movement_type, movement.quantity,
                movement.unit_cost, movement.total_cost, movement.reference_number,
                movement.notes, movement.created_date, movement.created_by
            ))

            # تحديث المخزون الحالي
            if movement.movement_type in ["دخول", "إرجاع"]:
                quantity_change = movement.quantity
            elif movement.movement_type in ["خروج", "تلف", "استهلاك"]:
                quantity_change = -movement.quantity
            else:  # تعديل
                quantity_change = movement.quantity

            cursor.execute('''
                UPDATE inventory_items
                SET current_stock = current_stock + ?,
                    last_updated = ?
                WHERE id = ?
            ''', (quantity_change, datetime.now().isoformat(), movement.item_id))

            # فحص التنبيهات
            self.check_stock_alerts(cursor, movement.item_id)

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"خطأ في إضافة حركة المخزون: {e}")
            return False

    def check_stock_alerts(self, cursor, item_id: str):
        """فحص تنبيهات المخزون"""
        try:
            # الحصول على بيانات العنصر
            cursor.execute('''
                SELECT current_stock, minimum_stock, maximum_stock, name
                FROM inventory_items WHERE id = ?
            ''', (item_id,))

            result = cursor.fetchone()
            if not result:
                return

            current_stock, minimum_stock, maximum_stock, item_name = result

            # فحص المخزون المنخفض
            if current_stock <= minimum_stock:
                alert_message = f"تنبيه: المخزون منخفض للعنصر '{item_name}'. الكمية الحالية: {current_stock}"
                cursor.execute('''
                    INSERT INTO inventory_alerts (id, item_id, alert_type, message, created_date)
                    VALUES (?, ?, ?, ?, ?)
                ''', (str(uuid.uuid4()), item_id, "مخزون منخفض", alert_message, datetime.now().isoformat()))

            # فحص نفاد المخزون
            if current_stock <= 0:
                alert_message = f"تحذير: نفد المخزون للعنصر '{item_name}'"
                cursor.execute('''
                    INSERT INTO inventory_alerts (id, item_id, alert_type, message, created_date)
                    VALUES (?, ?, ?, ?, ?)
                ''', (str(uuid.uuid4()), item_id, "نفاد مخزون", alert_message, datetime.now().isoformat()))

        except Exception as e:
            print(f"خطأ في فحص التنبيهات: {e}")

    def get_low_stock_items(self) -> List[Dict[str, Any]]:
        """الحصول على العناصر ذات المخزون المنخفض"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM inventory_items
                WHERE current_stock <= minimum_stock
                ORDER BY (current_stock / minimum_stock) ASC
            ''')

            items_data = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]

            items = []
            for item_data in items_data:
                item_dict = dict(zip(columns, item_data))
                items.append(item_dict)

            conn.close()
            return items

        except Exception as e:
            print(f"خطأ في تحميل العناصر منخفضة المخزون: {e}")
            return []

    def get_all_items(self) -> List[Dict[str, Any]]:
        """الحصول على جميع العناصر (اسم مختصر)"""
        return self.get_all_inventory_items()

    def get_item_by_id(self, item_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على عنصر بالمعرف"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM inventory_items WHERE id = ?', (item_id,))
            item_data = cursor.fetchone()

            if item_data:
                columns = [desc[0] for desc in cursor.description]
                item_dict = dict(zip(columns, item_data))
                conn.close()
                return item_dict

            conn.close()
            return None

        except Exception as e:
            print(f"خطأ في تحميل العنصر: {e}")
            return None

    def update_inventory_item(self, item: InventoryItem) -> bool:
        """تحديث عنصر المخزون"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE inventory_items SET
                name = ?, category = ?, subcategory = ?, description = ?, unit = ?,
                current_stock = ?, minimum_stock = ?, maximum_stock = ?, unit_cost = ?,
                selling_price = ?, supplier = ?, location = ?, barcode = ?, notes = ?,
                last_updated = ?, status = ?
                WHERE id = ?
            ''', (
                item.name, item.category, item.subcategory, item.description, item.unit,
                item.current_stock, item.minimum_stock, item.maximum_stock, item.unit_cost,
                item.selling_price, item.supplier, item.location, item.barcode, item.notes,
                item.last_updated, item.status, item.id
            ))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"خطأ في تحديث عنصر المخزون: {e}")
            return False

    def delete_inventory_item(self, item_id: str) -> bool:
        """حذف عنصر المخزون"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # حذف حركات المخزون المرتبطة
            cursor.execute('DELETE FROM stock_movements WHERE item_id = ?', (item_id,))

            # حذف التنبيهات المرتبطة
            cursor.execute('DELETE FROM inventory_alerts WHERE item_id = ?', (item_id,))

            # حذف العنصر
            cursor.execute('DELETE FROM inventory_items WHERE id = ?', (item_id,))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"خطأ في حذف عنصر المخزون: {e}")
            return False

    def search_inventory_items(self, search_term: str, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """البحث المتقدم في المخزون"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            query = "SELECT * FROM inventory_items WHERE 1=1"
            params = []

            # البحث النصي
            if search_term:
                search_pattern = f"%{search_term}%"
                query += " AND (name LIKE ? OR category LIKE ? OR supplier LIKE ? OR description LIKE ?)"
                params.extend([search_pattern] * 4)

            # تطبيق الفلاتر
            if filters:
                if filters.get('category'):
                    query += " AND category = ?"
                    params.append(filters['category'])

                if filters.get('status'):
                    if filters['status'] == 'مخزون منخفض':
                        query += " AND current_stock <= minimum_stock"
                    elif filters['status'] == 'نفد المخزون':
                        query += " AND current_stock <= 0"
                    elif filters['status'] == 'متاح':
                        query += " AND current_stock > minimum_stock"

            query += " ORDER BY name"

            cursor.execute(query, params)
            items_data = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]

            items = []
            for item_data in items_data:
                item_dict = dict(zip(columns, item_data))
                items.append(item_dict)

            conn.close()
            return items

        except Exception as e:
            print(f"خطأ في البحث: {e}")
            return []

    def get_inventory_statistics(self) -> Dict[str, Any]:
        """الحصول على إحصائيات المخزون"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            stats = {}

            # إجمالي العناصر
            cursor.execute("SELECT COUNT(*) FROM inventory_items")
            stats['total_items'] = cursor.fetchone()[0]

            # العناصر منخفضة المخزون
            cursor.execute("SELECT COUNT(*) FROM inventory_items WHERE current_stock <= minimum_stock")
            stats['low_stock_items'] = cursor.fetchone()[0]

            # العناصر نافدة المخزون
            cursor.execute("SELECT COUNT(*) FROM inventory_items WHERE current_stock <= 0")
            stats['out_of_stock_items'] = cursor.fetchone()[0]

            # إجمالي قيمة المخزون
            cursor.execute("SELECT SUM(current_stock * unit_cost) FROM inventory_items")
            result = cursor.fetchone()[0]
            stats['total_inventory_value'] = result if result else 0.0

            # العناصر حسب الفئة
            cursor.execute("SELECT category, COUNT(*) FROM inventory_items GROUP BY category")
            category_data = cursor.fetchall()
            stats['items_by_category'] = dict(category_data)

            conn.close()
            return stats

        except Exception as e:
            print(f"خطأ في تحميل الإحصائيات: {e}")
            return {}


class AdvancedInventoryManagerWidget(QWidget):
    """واجهة إدارة المخزون المتقدمة"""

    item_selected = Signal(str)  # إشارة اختيار عنصر
    item_updated = Signal()  # إشارة تحديث العنصر

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = InventoryDatabaseManager()
        self.current_item_id = None
        self.init_ui()
        self.load_inventory()
        self.setup_auto_refresh()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)

        # شريط الأدوات
        self.create_toolbar(layout)

        # المحتوى الرئيسي
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # قائمة المخزون
        self.create_inventory_panel(main_splitter)

        # تفاصيل العنصر
        self.create_details_panel(main_splitter)

        # تعيين النسب
        main_splitter.setSizes([600, 400])

        layout.addWidget(main_splitter)

        # شريط الحالة
        self.create_status_bar(layout)

    def create_toolbar(self, parent_layout):
        """إنشاء شريط الأدوات"""
        toolbar_widget = QWidget()
        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(0, 0, 0, 0)

        # البحث والفلترة
        search_group = QGroupBox("البحث والفلترة")
        search_layout = QHBoxLayout(search_group)

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث بالاسم، الفئة، أو المورد...")
        self.search_edit.textChanged.connect(self.filter_inventory)

        self.category_filter = QComboBox()
        self.category_filter.addItems(["جميع الفئات", "أخشاب", "معادن", "أقمشة", "مواد لاصقة", "أدوات"])
        self.category_filter.currentTextChanged.connect(self.apply_filters)

        self.status_filter = QComboBox()
        self.status_filter.addItems(["جميع الحالات", "متاح", "مخزون منخفض", "نفد المخزون"])
        self.status_filter.currentTextChanged.connect(self.apply_filters)

        search_layout.addWidget(QLabel("البحث:"))
        search_layout.addWidget(self.search_edit)
        search_layout.addWidget(QLabel("الفئة:"))
        search_layout.addWidget(self.category_filter)
        search_layout.addWidget(QLabel("الحالة:"))
        search_layout.addWidget(self.status_filter)

        # أزرار الإدارة
        actions_group = QGroupBox("الإجراءات")
        actions_layout = QHBoxLayout(actions_group)

        add_item_btn = QPushButton("إضافة عنصر")
        add_item_btn.setIcon(IconsManager.get_standard_icon('add'))
        add_item_btn.clicked.connect(self.add_new_item)

        edit_item_btn = QPushButton("تعديل")
        edit_item_btn.setIcon(IconsManager.get_standard_icon('edit'))
        edit_item_btn.clicked.connect(self.edit_selected_item)

        delete_item_btn = QPushButton("حذف")
        delete_item_btn.setIcon(IconsManager.get_standard_icon('delete'))
        delete_item_btn.clicked.connect(self.delete_selected_item)

        stock_movement_btn = QPushButton("حركة مخزون")
        stock_movement_btn.setIcon(IconsManager.get_standard_icon('refresh'))
        stock_movement_btn.clicked.connect(self.add_stock_movement)

        actions_layout.addWidget(add_item_btn)
        actions_layout.addWidget(edit_item_btn)
        actions_layout.addWidget(delete_item_btn)
        actions_layout.addWidget(stock_movement_btn)

        toolbar_layout.addWidget(search_group)
        toolbar_layout.addWidget(actions_group)
        toolbar_layout.addStretch()

        parent_layout.addWidget(toolbar_widget)

    def create_inventory_panel(self, parent_splitter):
        """إنشاء لوحة المخزون"""
        inventory_widget = QWidget()
        layout = QVBoxLayout(inventory_widget)

        # جدول المخزون
        self.inventory_table = QTableWidget()
        self.inventory_table.setColumnCount(8)
        self.inventory_table.setHorizontalHeaderLabels([
            "الاسم", "الفئة", "المخزون الحالي", "الحد الأدنى",
            "تكلفة الوحدة", "سعر البيع", "المورد", "الحالة"
        ])

        # تعديل عرض الأعمدة
        header = self.inventory_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)

        self.inventory_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.inventory_table.setAlternatingRowColors(True)
        self.inventory_table.setSortingEnabled(True)
        self.inventory_table.itemSelectionChanged.connect(self.on_item_selected)

        layout.addWidget(self.inventory_table)
        parent_splitter.addWidget(inventory_widget)

    def create_details_panel(self, parent_splitter):
        """إنشاء لوحة التفاصيل"""
        details_widget = QWidget()
        layout = QVBoxLayout(details_widget)

        # تفاصيل العنصر
        details_group = QGroupBox("تفاصيل العنصر")
        details_layout = QFormLayout(details_group)

        self.item_name_label = QLabel("-")
        self.item_category_label = QLabel("-")
        self.item_description_label = QLabel("-")
        self.item_unit_label = QLabel("-")
        self.item_current_stock_label = QLabel("-")
        self.item_unit_cost_label = QLabel("-")
        self.item_selling_price_label = QLabel("-")
        self.item_supplier_label = QLabel("-")
        self.item_location_label = QLabel("-")

        details_layout.addRow("الاسم:", self.item_name_label)
        details_layout.addRow("الفئة:", self.item_category_label)
        details_layout.addRow("الوصف:", self.item_description_label)
        details_layout.addRow("الوحدة:", self.item_unit_label)
        details_layout.addRow("المخزون الحالي:", self.item_current_stock_label)
        details_layout.addRow("تكلفة الوحدة:", self.item_unit_cost_label)
        details_layout.addRow("سعر البيع:", self.item_selling_price_label)
        details_layout.addRow("المورد:", self.item_supplier_label)
        details_layout.addRow("الموقع:", self.item_location_label)

        layout.addWidget(details_group)
        layout.addStretch()
        parent_splitter.addWidget(details_widget)

    def create_status_bar(self, parent_layout):
        """إنشاء شريط الحالة"""
        status_widget = QWidget()
        status_layout = QHBoxLayout(status_widget)

        self.total_items_label = QLabel("إجمالي العناصر: 0")
        self.total_value_label = QLabel("قيمة المخزون: 0.00 د.ل")
        self.low_stock_label = QLabel("مخزون منخفض: 0")

        status_layout.addWidget(self.total_items_label)
        status_layout.addWidget(self.total_value_label)
        status_layout.addWidget(self.low_stock_label)
        status_layout.addStretch()

        parent_layout.addWidget(status_widget)

    def setup_auto_refresh(self):
        """إعداد التحديث التلقائي"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.start(30000)  # تحديث كل 30 ثانية

    def load_inventory(self):
        """تحميل المخزون"""
        items = self.db_manager.get_all_items()
        self.populate_inventory_table(items)
        self.update_statistics(items)

    def populate_inventory_table(self, items: List[Dict[str, Any]]):
        """ملء جدول المخزون"""
        self.inventory_table.setRowCount(len(items))

        for row, item in enumerate(items):
            self.inventory_table.setItem(row, 0, QTableWidgetItem(item.get('name', '')))
            self.inventory_table.setItem(row, 1, QTableWidgetItem(item.get('category', '')))
            self.inventory_table.setItem(row, 2, QTableWidgetItem(f"{item.get('current_stock', 0):.1f}"))
            self.inventory_table.setItem(row, 3, QTableWidgetItem(f"{item.get('minimum_stock', 0):.1f}"))

            # تنسيق العملة
            from ui.modern_styles import CurrencyFormatter
            unit_cost = CurrencyFormatter.format_currency(item.get('unit_cost', 0))
            selling_price = CurrencyFormatter.format_currency(item.get('selling_price', 0))

            self.inventory_table.setItem(row, 4, QTableWidgetItem(unit_cost))
            self.inventory_table.setItem(row, 5, QTableWidgetItem(selling_price))
            self.inventory_table.setItem(row, 6, QTableWidgetItem(item.get('supplier', '')))

            # تحديد حالة المخزون
            current_stock = item.get('current_stock', 0)
            minimum_stock = item.get('minimum_stock', 0)

            if current_stock <= 0:
                status = "نفد المخزون"
                color = QColor(255, 0, 0)  # أحمر
            elif current_stock <= minimum_stock:
                status = "مخزون منخفض"
                color = QColor(255, 165, 0)  # برتقالي
            else:
                status = "متاح"
                color = QColor(0, 128, 0)  # أخضر

            status_item = QTableWidgetItem(status)
            status_item.setForeground(color)
            self.inventory_table.setItem(row, 7, status_item)

            # حفظ معرف العنصر
            self.inventory_table.item(row, 0).setData(Qt.ItemDataRole.UserRole, item.get('id'))

    def update_statistics(self, items: List[Dict[str, Any]]):
        """تحديث الإحصائيات"""
        total_items = len(items)
        total_value = sum(item.get('current_stock', 0) * item.get('unit_cost', 0) for item in items)
        low_stock_count = len([item for item in items
                              if item.get('current_stock', 0) <= item.get('minimum_stock', 0)])

        from ui.modern_styles import CurrencyFormatter
        self.total_items_label.setText(f"إجمالي العناصر: {total_items}")
        self.total_value_label.setText(f"قيمة المخزون: {CurrencyFormatter.format_currency(total_value)}")
        self.low_stock_label.setText(f"مخزون منخفض: {low_stock_count}")

    def filter_inventory(self):
        """فلترة المخزون"""
        search_text = self.search_edit.text().lower()
        category_filter = self.category_filter.currentText()
        status_filter = self.status_filter.currentText()

        for row in range(self.inventory_table.rowCount()):
            show_row = True

            # فلترة النص
            if search_text:
                name = self.inventory_table.item(row, 0).text().lower()
                category = self.inventory_table.item(row, 1).text().lower()
                supplier = self.inventory_table.item(row, 6).text().lower()

                if not (search_text in name or search_text in category or search_text in supplier):
                    show_row = False

            # فلترة الفئة
            if show_row and category_filter != "جميع الفئات":
                if self.inventory_table.item(row, 1).text() != category_filter:
                    show_row = False

            # فلترة الحالة
            if show_row and status_filter != "جميع الحالات":
                if self.inventory_table.item(row, 7).text() != status_filter:
                    show_row = False

            self.inventory_table.setRowHidden(row, not show_row)

    def apply_filters(self):
        """تطبيق الفلاتر"""
        self.filter_inventory()

    def on_item_selected(self):
        """عند اختيار عنصر"""
        current_row = self.inventory_table.currentRow()
        if current_row >= 0:
            item_id = self.inventory_table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)
            if item_id:
                self.current_item_id = item_id
                self.load_item_details(item_id)
                self.item_selected.emit(item_id)

    def load_item_details(self, item_id: str):
        """تحميل تفاصيل العنصر"""
        item = self.db_manager.get_item_by_id(item_id)
        if item:
            from ui.modern_styles import CurrencyFormatter

            self.item_name_label.setText(item.get('name', '-'))
            self.item_category_label.setText(item.get('category', '-'))
            self.item_description_label.setText(item.get('description', '-'))
            self.item_unit_label.setText(item.get('unit', '-'))
            self.item_current_stock_label.setText(f"{item.get('current_stock', 0):.1f}")
            self.item_unit_cost_label.setText(CurrencyFormatter.format_currency(item.get('unit_cost', 0)))
            self.item_selling_price_label.setText(CurrencyFormatter.format_currency(item.get('selling_price', 0)))
            self.item_supplier_label.setText(item.get('supplier', '-'))
            self.item_location_label.setText(item.get('location', '-'))

    def add_new_item(self):
        """إضافة عنصر جديد"""
        dialog = InventoryItemDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            item_data = dialog.get_item_data()

            # إنشاء كائن العنصر
            item = InventoryItem(
                id="",  # سيتم إنشاؤه تلقائياً
                name=item_data['name'],
                category=item_data['category'],
                subcategory=item_data['subcategory'],
                description=item_data['description'],
                unit=item_data['unit'],
                current_stock=item_data['current_stock'],
                minimum_stock=item_data['minimum_stock'],
                maximum_stock=item_data['maximum_stock'],
                unit_cost=item_data['unit_cost'],
                selling_price=item_data['selling_price'],
                supplier=item_data['supplier'],
                location=item_data['location'],
                barcode=item_data['barcode'],
                notes=item_data['notes'],
                created_date="",  # سيتم إنشاؤه تلقائياً
                last_updated=""   # سيتم إنشاؤه تلقائياً
            )

            if self.db_manager.add_inventory_item(item):
                QMessageBox.information(self, "نجح", "تم إضافة العنصر بنجاح")
                self.refresh_data()
                self.item_updated.emit()
            else:
                QMessageBox.warning(self, "خطأ", "فشل في إضافة العنصر")

    def edit_selected_item(self):
        """تعديل العنصر المختار"""
        if not self.current_item_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عنصر للتعديل")
            return

        # تحميل بيانات العنصر الحالية
        item_data = self.db_manager.get_item_by_id(self.current_item_id)
        if not item_data:
            QMessageBox.warning(self, "خطأ", "لم يتم العثور على بيانات العنصر")
            return

        dialog = InventoryItemDialog(self, item_data)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            updated_data = dialog.get_item_data()

            # إنشاء كائن العنصر المحدث
            item = InventoryItem(
                id=self.current_item_id,
                name=updated_data['name'],
                category=updated_data['category'],
                subcategory=updated_data['subcategory'],
                description=updated_data['description'],
                unit=updated_data['unit'],
                current_stock=updated_data['current_stock'],
                minimum_stock=updated_data['minimum_stock'],
                maximum_stock=updated_data['maximum_stock'],
                unit_cost=updated_data['unit_cost'],
                selling_price=updated_data['selling_price'],
                supplier=updated_data['supplier'],
                location=updated_data['location'],
                barcode=updated_data['barcode'],
                notes=updated_data['notes'],
                created_date=item_data['created_date'],  # الاحتفاظ بالتاريخ الأصلي
                last_updated=datetime.now().isoformat()  # تحديث تاريخ التعديل
            )

            if self.db_manager.update_inventory_item(item):
                QMessageBox.information(self, "نجح", "تم تحديث العنصر بنجاح")
                self.refresh_data()
                self.load_item_details(self.current_item_id)
                self.item_updated.emit()
            else:
                QMessageBox.warning(self, "خطأ", "فشل في تحديث العنصر")

    def delete_selected_item(self):
        """حذف العنصر المختار"""
        if not self.current_item_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عنصر للحذف")
            return

        # الحصول على اسم العنصر للتأكيد
        item_data = self.db_manager.get_item_by_id(self.current_item_id)
        item_name = item_data.get('name', 'غير معروف') if item_data else 'غير معروف'

        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف العنصر '{item_name}'؟\n\n"
            "⚠️ تحذير: سيتم حذف جميع البيانات المرتبطة بهذا العنصر:\n"
            "• حركات المخزون\n"
            "• التنبيهات\n"
            "• سجلات الطلبات\n\n"
            "هذا الإجراء لا يمكن التراجع عنه!",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            if self.db_manager.delete_inventory_item(self.current_item_id):
                QMessageBox.information(self, "تم الحذف", f"تم حذف العنصر '{item_name}' بنجاح")
                self.current_item_id = None
                self.refresh_data()
                self.clear_item_details()
                self.item_updated.emit()
            else:
                QMessageBox.warning(self, "خطأ", "فشل في حذف العنصر")

    def add_stock_movement(self):
        """إضافة حركة مخزون"""
        if not self.current_item_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عنصر لإضافة حركة مخزون")
            return

        item_data = self.db_manager.get_item_by_id(self.current_item_id)
        if not item_data:
            QMessageBox.warning(self, "خطأ", "لم يتم العثور على بيانات العنصر")
            return

        dialog = StockMovementDialog(self, item_data)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            movement_data = dialog.get_movement_data()

            # إنشاء كائن الحركة
            movement = StockMovement(
                id="",  # سيتم إنشاؤه تلقائياً
                item_id=self.current_item_id,
                movement_type=movement_data['movement_type'],
                quantity=movement_data['quantity'],
                unit_cost=movement_data['unit_cost'],
                total_cost=movement_data['quantity'] * movement_data['unit_cost'],
                reference_number=movement_data['reference_number'],
                notes=movement_data['notes'],
                created_date="",  # سيتم إنشاؤه تلقائياً
                created_by=movement_data['created_by']
            )

            if self.db_manager.add_stock_movement(movement):
                QMessageBox.information(self, "نجح", "تم إضافة حركة المخزون بنجاح")
                self.refresh_data()
                self.load_item_details(self.current_item_id)
                self.item_updated.emit()
            else:
                QMessageBox.warning(self, "خطأ", "فشل في إضافة حركة المخزون")

    def clear_item_details(self):
        """مسح تفاصيل العنصر"""
        self.item_name_label.setText("-")
        self.item_category_label.setText("-")
        self.item_description_label.setText("-")
        self.item_unit_label.setText("-")
        self.item_current_stock_label.setText("-")
        self.item_unit_cost_label.setText("-")
        self.item_selling_price_label.setText("-")
        self.item_supplier_label.setText("-")
        self.item_location_label.setText("-")

    def refresh_data(self):
        """تحديث البيانات"""
        self.load_inventory()

    def export_inventory(self):
        """تصدير المخزون"""
        try:
            from PySide6.QtWidgets import QFileDialog

            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "تصدير بيانات المخزون",
                "inventory_export.csv",
                "CSV Files (*.csv);;Excel Files (*.xlsx)"
            )

            if file_path:
                items = self.db_manager.get_all_items()

                if file_path.endswith('.csv'):
                    # تصدير CSV
                    import csv
                    with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                        writer = csv.writer(csvfile)

                        # كتابة العناوين
                        headers = ['الاسم', 'الفئة', 'الوصف', 'الوحدة', 'المخزون الحالي',
                                 'الحد الأدنى', 'تكلفة الوحدة (د.ل)', 'سعر البيع (د.ل)',
                                 'المورد', 'الموقع', 'الحالة']
                        writer.writerow(headers)

                        # كتابة البيانات
                        for item in items:
                            from ui.modern_styles import CurrencyFormatter
                            unit_cost = CurrencyFormatter.format_currency(item.get('unit_cost', 0))
                            selling_price = CurrencyFormatter.format_currency(item.get('selling_price', 0))

                            # تحديد الحالة
                            current_stock = item.get('current_stock', 0)
                            minimum_stock = item.get('minimum_stock', 0)

                            if current_stock <= 0:
                                status = "نفد المخزون"
                            elif current_stock <= minimum_stock:
                                status = "مخزون منخفض"
                            else:
                                status = "متاح"

                            row = [
                                item.get('name', ''),
                                item.get('category', ''),
                                item.get('description', ''),
                                item.get('unit', ''),
                                f"{current_stock:.1f}",
                                f"{minimum_stock:.1f}",
                                unit_cost,
                                selling_price,
                                item.get('supplier', ''),
                                item.get('location', ''),
                                status
                            ]
                            writer.writerow(row)

                QMessageBox.information(self, "تصدير ناجح", f"تم تصدير بيانات {len(items)} عنصر إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تصدير البيانات: {e}")


class InventoryItemDialog(QDialog):
    """حوار إضافة/تعديل عنصر المخزون"""

    def __init__(self, parent=None, item_data=None):
        super().__init__(parent)
        self.item_data = item_data or {}
        self.init_ui()
        self.load_data()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("إضافة عنصر جديد" if not self.item_data else "تعديل عنصر المخزون")
        self.setModal(True)
        self.resize(600, 700)

        layout = QVBoxLayout(self)

        # تبويبات البيانات
        tabs = QTabWidget()

        # تبويب البيانات الأساسية
        basic_tab = QWidget()
        basic_layout = QFormLayout(basic_tab)

        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("اسم العنصر")
        basic_layout.addRow("الاسم *:", self.name_edit)

        self.category_combo = QComboBox()
        self.category_combo.setEditable(True)
        self.category_combo.addItems(["أخشاب", "معادن", "أقمشة", "مواد لاصقة", "أدوات", "مسامير", "دهانات"])
        basic_layout.addRow("الفئة *:", self.category_combo)

        self.subcategory_edit = QLineEdit()
        self.subcategory_edit.setPlaceholderText("الفئة الفرعية")
        basic_layout.addRow("الفئة الفرعية:", self.subcategory_edit)

        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        self.description_edit.setPlaceholderText("وصف العنصر...")
        basic_layout.addRow("الوصف:", self.description_edit)

        self.unit_combo = QComboBox()
        self.unit_combo.setEditable(True)
        self.unit_combo.addItems(["قطعة", "متر", "متر مربع", "متر مكعب", "كيلو", "لتر", "علبة", "كيس"])
        basic_layout.addRow("وحدة القياس *:", self.unit_combo)

        tabs.addTab(basic_tab, "البيانات الأساسية")

        # تبويب المخزون
        stock_tab = QWidget()
        stock_layout = QFormLayout(stock_tab)

        self.current_stock_spin = QDoubleSpinBox()
        self.current_stock_spin.setRange(0.0, 999999.99)
        self.current_stock_spin.setDecimals(2)
        stock_layout.addRow("المخزون الحالي:", self.current_stock_spin)

        self.minimum_stock_spin = QDoubleSpinBox()
        self.minimum_stock_spin.setRange(0.0, 999999.99)
        self.minimum_stock_spin.setDecimals(2)
        stock_layout.addRow("الحد الأدنى:", self.minimum_stock_spin)

        self.maximum_stock_spin = QDoubleSpinBox()
        self.maximum_stock_spin.setRange(0.0, 999999.99)
        self.maximum_stock_spin.setDecimals(2)
        stock_layout.addRow("الحد الأقصى:", self.maximum_stock_spin)

        self.location_edit = QLineEdit()
        self.location_edit.setPlaceholderText("موقع التخزين")
        stock_layout.addRow("الموقع:", self.location_edit)

        tabs.addTab(stock_tab, "المخزون")

        # تبويب الأسعار
        pricing_tab = QWidget()
        pricing_layout = QFormLayout(pricing_tab)

        self.unit_cost_spin = QDoubleSpinBox()
        self.unit_cost_spin.setRange(0.0, 999999.99)
        self.unit_cost_spin.setDecimals(2)
        self.unit_cost_spin.setSuffix(" د.ل")
        pricing_layout.addRow("تكلفة الوحدة:", self.unit_cost_spin)

        self.selling_price_spin = QDoubleSpinBox()
        self.selling_price_spin.setRange(0.0, 999999.99)
        self.selling_price_spin.setDecimals(2)
        self.selling_price_spin.setSuffix(" د.ل")
        pricing_layout.addRow("سعر البيع:", self.selling_price_spin)

        self.supplier_edit = QLineEdit()
        self.supplier_edit.setPlaceholderText("اسم المورد")
        pricing_layout.addRow("المورد:", self.supplier_edit)

        tabs.addTab(pricing_tab, "الأسعار")

        # تبويب معلومات إضافية
        extra_tab = QWidget()
        extra_layout = QFormLayout(extra_tab)

        self.barcode_edit = QLineEdit()
        self.barcode_edit.setPlaceholderText("رمز الباركود")
        extra_layout.addRow("الباركود:", self.barcode_edit)

        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")
        extra_layout.addRow("ملاحظات:", self.notes_edit)

        tabs.addTab(extra_tab, "معلومات إضافية")

        layout.addWidget(tabs)

        # أزرار الحوار
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        buttons.button(QDialogButtonBox.StandardButton.Ok).setText("حفظ")
        buttons.button(QDialogButtonBox.StandardButton.Cancel).setText("إلغاء")

        layout.addWidget(buttons)

    def load_data(self):
        """تحميل البيانات الموجودة"""
        if self.item_data:
            self.name_edit.setText(self.item_data.get('name', ''))

            category = self.item_data.get('category', '')
            if category:
                index = self.category_combo.findText(category)
                if index >= 0:
                    self.category_combo.setCurrentIndex(index)
                else:
                    self.category_combo.setCurrentText(category)

            self.subcategory_edit.setText(self.item_data.get('subcategory', ''))
            self.description_edit.setPlainText(self.item_data.get('description', ''))

            unit = self.item_data.get('unit', '')
            if unit:
                index = self.unit_combo.findText(unit)
                if index >= 0:
                    self.unit_combo.setCurrentIndex(index)
                else:
                    self.unit_combo.setCurrentText(unit)

            self.current_stock_spin.setValue(self.item_data.get('current_stock', 0.0))
            self.minimum_stock_spin.setValue(self.item_data.get('minimum_stock', 0.0))
            self.maximum_stock_spin.setValue(self.item_data.get('maximum_stock', 0.0))
            self.location_edit.setText(self.item_data.get('location', ''))
            self.unit_cost_spin.setValue(self.item_data.get('unit_cost', 0.0))
            self.selling_price_spin.setValue(self.item_data.get('selling_price', 0.0))
            self.supplier_edit.setText(self.item_data.get('supplier', ''))
            self.barcode_edit.setText(self.item_data.get('barcode', ''))
            self.notes_edit.setPlainText(self.item_data.get('notes', ''))

    def get_item_data(self):
        """الحصول على بيانات العنصر"""
        return {
            'name': self.name_edit.text().strip(),
            'category': self.category_combo.currentText().strip(),
            'subcategory': self.subcategory_edit.text().strip(),
            'description': self.description_edit.toPlainText().strip(),
            'unit': self.unit_combo.currentText().strip(),
            'current_stock': self.current_stock_spin.value(),
            'minimum_stock': self.minimum_stock_spin.value(),
            'maximum_stock': self.maximum_stock_spin.value(),
            'unit_cost': self.unit_cost_spin.value(),
            'selling_price': self.selling_price_spin.value(),
            'supplier': self.supplier_edit.text().strip(),
            'location': self.location_edit.text().strip(),
            'barcode': self.barcode_edit.text().strip(),
            'notes': self.notes_edit.toPlainText().strip()
        }

    def accept(self):
        """التحقق من صحة البيانات قبل الحفظ"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم العنصر")
            self.name_edit.setFocus()
            return

        if not self.category_combo.currentText().strip():
            QMessageBox.warning(self, "خطأ", "يرجى اختيار فئة العنصر")
            self.category_combo.setFocus()
            return

        if not self.unit_combo.currentText().strip():
            QMessageBox.warning(self, "خطأ", "يرجى اختيار وحدة القياس")
            self.unit_combo.setFocus()
            return

        super().accept()


class StockMovementDialog(QDialog):
    """حوار إضافة حركة مخزون"""

    def __init__(self, parent=None, item_data=None):
        super().__init__(parent)
        self.item_data = item_data or {}
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        item_name = self.item_data.get('name', 'غير معروف')
        self.setWindowTitle(f"حركة مخزون - {item_name}")
        self.setModal(True)
        self.resize(400, 300)

        layout = QVBoxLayout(self)

        # معلومات العنصر
        info_group = QGroupBox("معلومات العنصر")
        info_layout = QFormLayout(info_group)

        info_layout.addRow("الاسم:", QLabel(item_name))
        info_layout.addRow("المخزون الحالي:", QLabel(f"{self.item_data.get('current_stock', 0):.1f} {self.item_data.get('unit', '')}"))

        layout.addWidget(info_group)

        # بيانات الحركة
        movement_group = QGroupBox("بيانات الحركة")
        movement_layout = QFormLayout(movement_group)

        self.movement_type_combo = QComboBox()
        self.movement_type_combo.addItems(["دخول", "خروج", "تعديل", "تلف", "إرجاع", "استهلاك"])
        movement_layout.addRow("نوع الحركة *:", self.movement_type_combo)

        self.quantity_spin = QDoubleSpinBox()
        self.quantity_spin.setRange(0.01, 999999.99)
        self.quantity_spin.setDecimals(2)
        self.quantity_spin.setValue(1.0)
        movement_layout.addRow("الكمية *:", self.quantity_spin)

        self.unit_cost_spin = QDoubleSpinBox()
        self.unit_cost_spin.setRange(0.0, 999999.99)
        self.unit_cost_spin.setDecimals(2)
        self.unit_cost_spin.setSuffix(" د.ل")
        self.unit_cost_spin.setValue(self.item_data.get('unit_cost', 0.0))
        movement_layout.addRow("تكلفة الوحدة:", self.unit_cost_spin)

        self.reference_edit = QLineEdit()
        self.reference_edit.setPlaceholderText("رقم الفاتورة أو المرجع")
        movement_layout.addRow("رقم المرجع:", self.reference_edit)

        self.created_by_edit = QLineEdit()
        self.created_by_edit.setPlaceholderText("اسم المستخدم")
        self.created_by_edit.setText("مدير النظام")
        movement_layout.addRow("المستخدم:", self.created_by_edit)

        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("ملاحظات الحركة...")
        movement_layout.addRow("ملاحظات:", self.notes_edit)

        layout.addWidget(movement_group)

        # أزرار الحوار
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        buttons.button(QDialogButtonBox.StandardButton.Ok).setText("حفظ")
        buttons.button(QDialogButtonBox.StandardButton.Cancel).setText("إلغاء")

        layout.addWidget(buttons)

    def get_movement_data(self):
        """الحصول على بيانات الحركة"""
        return {
            'movement_type': self.movement_type_combo.currentText(),
            'quantity': self.quantity_spin.value(),
            'unit_cost': self.unit_cost_spin.value(),
            'reference_number': self.reference_edit.text().strip(),
            'notes': self.notes_edit.toPlainText().strip(),
            'created_by': self.created_by_edit.text().strip()
        }

    def accept(self):
        """التحقق من صحة البيانات قبل الحفظ"""
        if self.quantity_spin.value() <= 0:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال كمية صحيحة")
            self.quantity_spin.setFocus()
            return

        if not self.created_by_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المستخدم")
            self.created_by_edit.setFocus()
            return

        super().accept()
