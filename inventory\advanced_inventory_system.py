#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام تتبع المخزون المتقدم - PySide6
Advanced Inventory Tracking System - PySide6
"""

import sqlite3
import json
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QTableWidget, QTableWidgetItem, QHeaderView, QLineEdit,
    QComboBox, QTextEdit, QDateEdit, QSpinBox, QDoubleSpinBox,
    QGroupBox, QFormLayout, QDialog, QDialogButtonBox,
    QMessageBox, QFileDialog, QTabWidget, QSplitter,
    QTreeWidget, QTreeWidgetItem, QProgressBar, QCheckBox,
    Q<PERSON>ridLayout, QFrame
)
from PySide6.QtCore import Qt, QDate, Signal, QTimer, QThread
from PySide6.QtGui import QFont, QColor, QPixmap, QIcon

from ui.modern_styles import ModernStyleManager
from ui.icons_manager import IconsManager


@dataclass
class InventoryItem:
    """عنصر المخزون"""
    id: str
    name: str
    category: str
    subcategory: str
    description: str
    unit: str  # وحدة القياس (متر، قطعة، كيلو، إلخ)
    current_stock: float
    minimum_stock: float  # الحد الأدنى للتنبيه
    maximum_stock: float  # الحد الأقصى
    unit_cost: float  # تكلفة الوحدة (د.ل)
    selling_price: float  # سعر البيع (د.ل)
    supplier: str  # المورد
    location: str  # موقع التخزين
    barcode: str  # الباركود
    notes: str
    created_date: str
    last_updated: str
    status: str = "متاح"  # متاح، نفد، معلق

    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.created_date:
            self.created_date = datetime.now().isoformat()
        if not self.last_updated:
            self.last_updated = datetime.now().isoformat()


@dataclass
class StockMovement:
    """حركة المخزون"""
    id: str
    item_id: str
    movement_type: str  # دخول، خروج، تعديل، تلف
    quantity: float
    unit_cost: float  # تكلفة الوحدة (د.ل)
    total_cost: float  # التكلفة الإجمالية (د.ل)
    reference_number: str  # رقم المرجع (فاتورة، طلب، إلخ)
    notes: str
    created_date: str
    created_by: str

    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.created_date:
            self.created_date = datetime.now().isoformat()


@dataclass
class Supplier:
    """المورد"""
    id: str
    name: str
    company: str
    phone: str
    email: str
    address: str
    payment_terms: int
    notes: str
    created_date: str
    status: str = "نشط"

    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.created_date:
            self.created_date = datetime.now().isoformat()


class InventoryDatabaseManager:
    """مدير قاعدة بيانات المخزون"""

    def __init__(self, db_path: str = "inventory_advanced.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """تهيئة قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # جدول عناصر المخزون
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventory_items (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                category TEXT,
                subcategory TEXT,
                description TEXT,
                unit TEXT,
                current_stock REAL DEFAULT 0,
                minimum_stock REAL DEFAULT 0,
                maximum_stock REAL DEFAULT 0,
                unit_cost REAL DEFAULT 0,
                selling_price REAL DEFAULT 0,
                supplier TEXT,
                location TEXT,
                barcode TEXT,
                notes TEXT,
                created_date TEXT,
                last_updated TEXT,
                status TEXT DEFAULT 'متاح'
            )
        ''')

        # جدول حركات المخزون
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_movements (
                id TEXT PRIMARY KEY,
                item_id TEXT,
                movement_type TEXT,
                quantity REAL,
                unit_cost REAL,
                total_cost REAL,
                reference_number TEXT,
                notes TEXT,
                created_date TEXT,
                created_by TEXT,
                FOREIGN KEY (item_id) REFERENCES inventory_items (id)
            )
        ''')

        # جدول الموردين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS suppliers (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                company TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                payment_terms INTEGER DEFAULT 30,
                notes TEXT,
                created_date TEXT,
                status TEXT DEFAULT 'نشط'
            )
        ''')

        # جدول فئات المخزون
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventory_categories (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                parent_category TEXT,
                description TEXT,
                created_date TEXT
            )
        ''')

        # جدول التنبيهات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inventory_alerts (
                id TEXT PRIMARY KEY,
                item_id TEXT,
                alert_type TEXT,
                message TEXT,
                created_date TEXT,
                is_read INTEGER DEFAULT 0,
                FOREIGN KEY (item_id) REFERENCES inventory_items (id)
            )
        ''')

        # جدول طلبات الشراء
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS purchase_orders (
                id TEXT PRIMARY KEY,
                supplier_id TEXT,
                order_date TEXT,
                expected_delivery TEXT,
                total_amount REAL,  -- المبلغ الإجمالي (د.ل)
                status TEXT,
                notes TEXT,
                created_date TEXT,
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
            )
        ''')

        conn.commit()
        conn.close()

    def add_inventory_item(self, item: InventoryItem) -> bool:
        """إضافة عنصر جديد للمخزون"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO inventory_items
                (id, name, category, subcategory, description, unit, current_stock,
                 minimum_stock, maximum_stock, unit_cost, selling_price, supplier,
                 location, barcode, notes, created_date, last_updated, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                item.id, item.name, item.category, item.subcategory, item.description,
                item.unit, item.current_stock, item.minimum_stock, item.maximum_stock,
                item.unit_cost, item.selling_price, item.supplier, item.location,
                item.barcode, item.notes, item.created_date, item.last_updated, item.status
            ))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"خطأ في إضافة عنصر المخزون: {e}")
            return False

    def get_all_inventory_items(self) -> List[Dict[str, Any]]:
        """الحصول على جميع عناصر المخزون"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM inventory_items ORDER BY name')
            items_data = cursor.fetchall()

            items = []
            columns = [desc[0] for desc in cursor.description]

            for item_data in items_data:
                item_dict = dict(zip(columns, item_data))
                items.append(item_dict)

            conn.close()
            return items

        except Exception as e:
            print(f"خطأ في تحميل عناصر المخزون: {e}")
            return []

    def add_stock_movement(self, movement: StockMovement) -> bool:
        """إضافة حركة مخزون"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # إضافة الحركة
            cursor.execute('''
                INSERT INTO stock_movements
                (id, item_id, movement_type, quantity, unit_cost, total_cost,
                 reference_number, notes, created_date, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                movement.id, movement.item_id, movement.movement_type, movement.quantity,
                movement.unit_cost, movement.total_cost, movement.reference_number,
                movement.notes, movement.created_date, movement.created_by
            ))

            # تحديث المخزون الحالي
            if movement.movement_type in ["دخول", "إرجاع"]:
                quantity_change = movement.quantity
            elif movement.movement_type in ["خروج", "تلف", "استهلاك"]:
                quantity_change = -movement.quantity
            else:  # تعديل
                quantity_change = movement.quantity

            cursor.execute('''
                UPDATE inventory_items
                SET current_stock = current_stock + ?,
                    last_updated = ?
                WHERE id = ?
            ''', (quantity_change, datetime.now().isoformat(), movement.item_id))

            # فحص التنبيهات
            self.check_stock_alerts(cursor, movement.item_id)

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"خطأ في إضافة حركة المخزون: {e}")
            return False

    def check_stock_alerts(self, cursor, item_id: str):
        """فحص تنبيهات المخزون"""
        try:
            # الحصول على بيانات العنصر
            cursor.execute('''
                SELECT current_stock, minimum_stock, maximum_stock, name
                FROM inventory_items WHERE id = ?
            ''', (item_id,))

            result = cursor.fetchone()
            if not result:
                return

            current_stock, minimum_stock, maximum_stock, item_name = result

            # فحص المخزون المنخفض
            if current_stock <= minimum_stock:
                alert_message = f"تنبيه: المخزون منخفض للعنصر '{item_name}'. الكمية الحالية: {current_stock}"
                cursor.execute('''
                    INSERT INTO inventory_alerts (id, item_id, alert_type, message, created_date)
                    VALUES (?, ?, ?, ?, ?)
                ''', (str(uuid.uuid4()), item_id, "مخزون منخفض", alert_message, datetime.now().isoformat()))

            # فحص نفاد المخزون
            if current_stock <= 0:
                alert_message = f"تحذير: نفد المخزون للعنصر '{item_name}'"
                cursor.execute('''
                    INSERT INTO inventory_alerts (id, item_id, alert_type, message, created_date)
                    VALUES (?, ?, ?, ?, ?)
                ''', (str(uuid.uuid4()), item_id, "نفاد مخزون", alert_message, datetime.now().isoformat()))

        except Exception as e:
            print(f"خطأ في فحص التنبيهات: {e}")

    def get_low_stock_items(self) -> List[Dict[str, Any]]:
        """الحصول على العناصر ذات المخزون المنخفض"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM inventory_items
                WHERE current_stock <= minimum_stock
                ORDER BY (current_stock / minimum_stock) ASC
            ''')

            items_data = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]

            items = []
            for item_data in items_data:
                item_dict = dict(zip(columns, item_data))
                items.append(item_dict)

            conn.close()
            return items

        except Exception as e:
            print(f"خطأ في تحميل العناصر منخفضة المخزون: {e}")
            return []

    def get_all_items(self) -> List[Dict[str, Any]]:
        """الحصول على جميع العناصر (اسم مختصر)"""
        return self.get_all_inventory_items()

    def get_item_by_id(self, item_id: str) -> Optional[Dict[str, Any]]:
        """الحصول على عنصر بالمعرف"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM inventory_items WHERE id = ?', (item_id,))
            item_data = cursor.fetchone()

            if item_data:
                columns = [desc[0] for desc in cursor.description]
                item_dict = dict(zip(columns, item_data))
                conn.close()
                return item_dict

            conn.close()
            return None

        except Exception as e:
            print(f"خطأ في تحميل العنصر: {e}")
            return None


class AdvancedInventoryManagerWidget(QWidget):
    """واجهة إدارة المخزون المتقدمة"""

    item_selected = Signal(str)  # إشارة اختيار عنصر
    item_updated = Signal()  # إشارة تحديث العنصر

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_manager = InventoryDatabaseManager()
        self.current_item_id = None
        self.init_ui()
        self.load_inventory()
        self.setup_auto_refresh()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)

        # شريط الأدوات
        self.create_toolbar(layout)

        # المحتوى الرئيسي
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # قائمة المخزون
        self.create_inventory_panel(main_splitter)

        # تفاصيل العنصر
        self.create_details_panel(main_splitter)

        # تعيين النسب
        main_splitter.setSizes([600, 400])

        layout.addWidget(main_splitter)

        # شريط الحالة
        self.create_status_bar(layout)

    def create_toolbar(self, parent_layout):
        """إنشاء شريط الأدوات"""
        toolbar_widget = QWidget()
        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(0, 0, 0, 0)

        # البحث والفلترة
        search_group = QGroupBox("البحث والفلترة")
        search_layout = QHBoxLayout(search_group)

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("ابحث بالاسم، الفئة، أو المورد...")
        self.search_edit.textChanged.connect(self.filter_inventory)

        self.category_filter = QComboBox()
        self.category_filter.addItems(["جميع الفئات", "أخشاب", "معادن", "أقمشة", "مواد لاصقة", "أدوات"])
        self.category_filter.currentTextChanged.connect(self.apply_filters)

        self.status_filter = QComboBox()
        self.status_filter.addItems(["جميع الحالات", "متاح", "مخزون منخفض", "نفد المخزون"])
        self.status_filter.currentTextChanged.connect(self.apply_filters)

        search_layout.addWidget(QLabel("البحث:"))
        search_layout.addWidget(self.search_edit)
        search_layout.addWidget(QLabel("الفئة:"))
        search_layout.addWidget(self.category_filter)
        search_layout.addWidget(QLabel("الحالة:"))
        search_layout.addWidget(self.status_filter)

        # أزرار الإدارة
        actions_group = QGroupBox("الإجراءات")
        actions_layout = QHBoxLayout(actions_group)

        add_item_btn = QPushButton("إضافة عنصر")
        add_item_btn.setIcon(IconsManager.get_standard_icon('add'))
        add_item_btn.clicked.connect(self.add_new_item)

        edit_item_btn = QPushButton("تعديل")
        edit_item_btn.setIcon(IconsManager.get_standard_icon('edit'))
        edit_item_btn.clicked.connect(self.edit_selected_item)

        delete_item_btn = QPushButton("حذف")
        delete_item_btn.setIcon(IconsManager.get_standard_icon('delete'))
        delete_item_btn.clicked.connect(self.delete_selected_item)

        stock_movement_btn = QPushButton("حركة مخزون")
        stock_movement_btn.setIcon(IconsManager.get_standard_icon('refresh'))
        stock_movement_btn.clicked.connect(self.add_stock_movement)

        actions_layout.addWidget(add_item_btn)
        actions_layout.addWidget(edit_item_btn)
        actions_layout.addWidget(delete_item_btn)
        actions_layout.addWidget(stock_movement_btn)

        toolbar_layout.addWidget(search_group)
        toolbar_layout.addWidget(actions_group)
        toolbar_layout.addStretch()

        parent_layout.addWidget(toolbar_widget)

    def create_inventory_panel(self, parent_splitter):
        """إنشاء لوحة المخزون"""
        inventory_widget = QWidget()
        layout = QVBoxLayout(inventory_widget)

        # جدول المخزون
        self.inventory_table = QTableWidget()
        self.inventory_table.setColumnCount(8)
        self.inventory_table.setHorizontalHeaderLabels([
            "الاسم", "الفئة", "المخزون الحالي", "الحد الأدنى",
            "تكلفة الوحدة", "سعر البيع", "المورد", "الحالة"
        ])

        # تعديل عرض الأعمدة
        header = self.inventory_table.horizontalHeader()
        header.setStretchLastSection(True)
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)

        self.inventory_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.inventory_table.setAlternatingRowColors(True)
        self.inventory_table.setSortingEnabled(True)
        self.inventory_table.itemSelectionChanged.connect(self.on_item_selected)

        layout.addWidget(self.inventory_table)
        parent_splitter.addWidget(inventory_widget)

    def create_details_panel(self, parent_splitter):
        """إنشاء لوحة التفاصيل"""
        details_widget = QWidget()
        layout = QVBoxLayout(details_widget)

        # تفاصيل العنصر
        details_group = QGroupBox("تفاصيل العنصر")
        details_layout = QFormLayout(details_group)

        self.item_name_label = QLabel("-")
        self.item_category_label = QLabel("-")
        self.item_description_label = QLabel("-")
        self.item_unit_label = QLabel("-")
        self.item_current_stock_label = QLabel("-")
        self.item_unit_cost_label = QLabel("-")
        self.item_selling_price_label = QLabel("-")
        self.item_supplier_label = QLabel("-")
        self.item_location_label = QLabel("-")

        details_layout.addRow("الاسم:", self.item_name_label)
        details_layout.addRow("الفئة:", self.item_category_label)
        details_layout.addRow("الوصف:", self.item_description_label)
        details_layout.addRow("الوحدة:", self.item_unit_label)
        details_layout.addRow("المخزون الحالي:", self.item_current_stock_label)
        details_layout.addRow("تكلفة الوحدة:", self.item_unit_cost_label)
        details_layout.addRow("سعر البيع:", self.item_selling_price_label)
        details_layout.addRow("المورد:", self.item_supplier_label)
        details_layout.addRow("الموقع:", self.item_location_label)

        layout.addWidget(details_group)
        layout.addStretch()
        parent_splitter.addWidget(details_widget)

    def create_status_bar(self, parent_layout):
        """إنشاء شريط الحالة"""
        status_widget = QWidget()
        status_layout = QHBoxLayout(status_widget)

        self.total_items_label = QLabel("إجمالي العناصر: 0")
        self.total_value_label = QLabel("قيمة المخزون: 0.00 د.ل")
        self.low_stock_label = QLabel("مخزون منخفض: 0")

        status_layout.addWidget(self.total_items_label)
        status_layout.addWidget(self.total_value_label)
        status_layout.addWidget(self.low_stock_label)
        status_layout.addStretch()

        parent_layout.addWidget(status_widget)

    def setup_auto_refresh(self):
        """إعداد التحديث التلقائي"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.start(30000)  # تحديث كل 30 ثانية

    def load_inventory(self):
        """تحميل المخزون"""
        items = self.db_manager.get_all_items()
        self.populate_inventory_table(items)
        self.update_statistics(items)

    def populate_inventory_table(self, items: List[Dict[str, Any]]):
        """ملء جدول المخزون"""
        self.inventory_table.setRowCount(len(items))

        for row, item in enumerate(items):
            self.inventory_table.setItem(row, 0, QTableWidgetItem(item.get('name', '')))
            self.inventory_table.setItem(row, 1, QTableWidgetItem(item.get('category', '')))
            self.inventory_table.setItem(row, 2, QTableWidgetItem(f"{item.get('current_stock', 0):.1f}"))
            self.inventory_table.setItem(row, 3, QTableWidgetItem(f"{item.get('minimum_stock', 0):.1f}"))

            # تنسيق العملة
            from ui.modern_styles import CurrencyFormatter
            unit_cost = CurrencyFormatter.format_currency(item.get('unit_cost', 0))
            selling_price = CurrencyFormatter.format_currency(item.get('selling_price', 0))

            self.inventory_table.setItem(row, 4, QTableWidgetItem(unit_cost))
            self.inventory_table.setItem(row, 5, QTableWidgetItem(selling_price))
            self.inventory_table.setItem(row, 6, QTableWidgetItem(item.get('supplier', '')))

            # تحديد حالة المخزون
            current_stock = item.get('current_stock', 0)
            minimum_stock = item.get('minimum_stock', 0)

            if current_stock <= 0:
                status = "نفد المخزون"
                color = QColor(255, 0, 0)  # أحمر
            elif current_stock <= minimum_stock:
                status = "مخزون منخفض"
                color = QColor(255, 165, 0)  # برتقالي
            else:
                status = "متاح"
                color = QColor(0, 128, 0)  # أخضر

            status_item = QTableWidgetItem(status)
            status_item.setForeground(color)
            self.inventory_table.setItem(row, 7, status_item)

            # حفظ معرف العنصر
            self.inventory_table.item(row, 0).setData(Qt.ItemDataRole.UserRole, item.get('id'))

    def update_statistics(self, items: List[Dict[str, Any]]):
        """تحديث الإحصائيات"""
        total_items = len(items)
        total_value = sum(item.get('current_stock', 0) * item.get('unit_cost', 0) for item in items)
        low_stock_count = len([item for item in items
                              if item.get('current_stock', 0) <= item.get('minimum_stock', 0)])

        from ui.modern_styles import CurrencyFormatter
        self.total_items_label.setText(f"إجمالي العناصر: {total_items}")
        self.total_value_label.setText(f"قيمة المخزون: {CurrencyFormatter.format_currency(total_value)}")
        self.low_stock_label.setText(f"مخزون منخفض: {low_stock_count}")

    def filter_inventory(self):
        """فلترة المخزون"""
        search_text = self.search_edit.text().lower()
        category_filter = self.category_filter.currentText()
        status_filter = self.status_filter.currentText()

        for row in range(self.inventory_table.rowCount()):
            show_row = True

            # فلترة النص
            if search_text:
                name = self.inventory_table.item(row, 0).text().lower()
                category = self.inventory_table.item(row, 1).text().lower()
                supplier = self.inventory_table.item(row, 6).text().lower()

                if not (search_text in name or search_text in category or search_text in supplier):
                    show_row = False

            # فلترة الفئة
            if show_row and category_filter != "جميع الفئات":
                if self.inventory_table.item(row, 1).text() != category_filter:
                    show_row = False

            # فلترة الحالة
            if show_row and status_filter != "جميع الحالات":
                if self.inventory_table.item(row, 7).text() != status_filter:
                    show_row = False

            self.inventory_table.setRowHidden(row, not show_row)

    def apply_filters(self):
        """تطبيق الفلاتر"""
        self.filter_inventory()

    def on_item_selected(self):
        """عند اختيار عنصر"""
        current_row = self.inventory_table.currentRow()
        if current_row >= 0:
            item_id = self.inventory_table.item(current_row, 0).data(Qt.ItemDataRole.UserRole)
            if item_id:
                self.current_item_id = item_id
                self.load_item_details(item_id)
                self.item_selected.emit(item_id)

    def load_item_details(self, item_id: str):
        """تحميل تفاصيل العنصر"""
        item = self.db_manager.get_item_by_id(item_id)
        if item:
            from ui.modern_styles import CurrencyFormatter

            self.item_name_label.setText(item.get('name', '-'))
            self.item_category_label.setText(item.get('category', '-'))
            self.item_description_label.setText(item.get('description', '-'))
            self.item_unit_label.setText(item.get('unit', '-'))
            self.item_current_stock_label.setText(f"{item.get('current_stock', 0):.1f}")
            self.item_unit_cost_label.setText(CurrencyFormatter.format_currency(item.get('unit_cost', 0)))
            self.item_selling_price_label.setText(CurrencyFormatter.format_currency(item.get('selling_price', 0)))
            self.item_supplier_label.setText(item.get('supplier', '-'))
            self.item_location_label.setText(item.get('location', '-'))

    def add_new_item(self):
        """إضافة عنصر جديد"""
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ إضافة عنصر جديد قريباً")

    def edit_selected_item(self):
        """تعديل العنصر المختار"""
        if not self.current_item_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عنصر للتعديل")
            return

        QMessageBox.information(self, "معلومات", "سيتم تنفيذ تعديل العنصر قريباً")

    def delete_selected_item(self):
        """حذف العنصر المختار"""
        if not self.current_item_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عنصر للحذف")
            return

        reply = QMessageBox.question(self, "تأكيد الحذف",
                                   "هل أنت متأكد من حذف هذا العنصر؟",
                                   QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)

        if reply == QMessageBox.StandardButton.Yes:
            QMessageBox.information(self, "معلومات", "سيتم تنفيذ حذف العنصر قريباً")

    def add_stock_movement(self):
        """إضافة حركة مخزون"""
        if not self.current_item_id:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار عنصر لإضافة حركة مخزون")
            return

        QMessageBox.information(self, "معلومات", "سيتم تنفيذ إضافة حركة مخزون قريباً")

    def refresh_data(self):
        """تحديث البيانات"""
        self.load_inventory()

    def export_inventory(self):
        """تصدير المخزون"""
        QMessageBox.information(self, "معلومات", "سيتم تنفيذ تصدير المخزون قريباً")
