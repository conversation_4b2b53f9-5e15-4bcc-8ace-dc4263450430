#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصدار مبسط من تطبيق مصمم الأثاث الاحترافي
Simplified Version of Professional Furniture Designer
"""

import sys
import os
from pathlib import Path
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QMenuBar, QToolBar, QStatusBar, QSplitter, QTabWidget,
    QTreeWidget, QTreeWidgetItem, QTableWidget, QLabel,
    QPushButton, QMessageBox, QFileDialog, QProgressBar, QTextEdit
)
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt6.QtGui import QAction, QIcon, QFont, QPixmap

# استيراد الوحدات الأساسية
from ui.modern_styles import ModernStyleManager
from ui.icons_manager import IconsManager


class SimpleSplashScreen(QWidget):
    """شاشة البداية المبسطة"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة شاشة البداية"""
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint | Qt.WindowType.WindowStaysOnTopHint)
        self.setFixedSize(500, 300)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # الخلفية
        self.setStyleSheet(f"""
            QWidget {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {ModernStyleManager.COLORS['primary']},
                    stop:1 {ModernStyleManager.COLORS['secondary']});
                border-radius: 15px;
                color: {ModernStyleManager.COLORS['text_light']};
            }}
        """)
        
        # العنوان
        title_label = QLabel("🪑 مصمم الأثاث الاحترافي")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: 20px;
                font-weight: bold;
                margin: 20px 0;
                background: transparent;
            }}
        """)
        
        # العنوان الفرعي
        subtitle_label = QLabel("الإصدار المبسط - PyQt6")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setStyleSheet(f"""
            QLabel {{
                font-size: 14px;
                margin-bottom: 20px;
                background: transparent;
                opacity: 0.8;
            }}
        """)
        
        # رسالة التحميل
        loading_label = QLabel("جاري التحميل...")
        loading_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        loading_label.setStyleSheet(f"""
            QLabel {{
                font-size: 12px;
                background: transparent;
            }}
        """)
        
        layout.addWidget(title_label)
        layout.addWidget(subtitle_label)
        layout.addStretch()
        layout.addWidget(loading_label)
        
        # توسيط النافذة
        self.center_on_screen()
        
    def center_on_screen(self):
        """توسيط النافذة على الشاشة"""
        screen = QApplication.primaryScreen().geometry()
        x = (screen.width() - self.width()) // 2
        y = (screen.height() - self.height()) // 2
        self.move(x, y)


class SimpleMainWindow(QMainWindow):
    """النافذة الرئيسية المبسطة"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("مصمم الأثاث الاحترافي - الإصدار المبسط")
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)
        
        # إعداد الأيقونة
        self.setWindowIcon(IconsManager.get_standard_icon('furniture'))
        
        # إنشاء القوائم
        self.create_menus()
        
        # إنشاء شريط الأدوات
        self.create_toolbars()
        
        # إنشاء المحتوى الرئيسي
        self.create_main_content()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
        
        # توسيط النافذة
        self.center_on_screen()
        
    def create_menus(self):
        """إنشاء القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu("ملف")
        
        new_action = QAction(IconsManager.get_action_icon('new_project'), "مشروع جديد", self)
        new_action.setShortcut("Ctrl+N")
        new_action.triggered.connect(self.new_project)
        file_menu.addAction(new_action)
        
        open_action = QAction(IconsManager.get_action_icon('open_project'), "فتح مشروع", self)
        open_action.setShortcut("Ctrl+O")
        open_action.triggered.connect(self.open_project)
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة الأدوات
        tools_menu = menubar.addMenu("أدوات")
        
        calculator_action = QAction(IconsManager.get_standard_icon('calculator'), "حاسبة التكلفة", self)
        calculator_action.triggered.connect(self.show_calculator)
        tools_menu.addAction(calculator_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")
        
        about_action = QAction(IconsManager.get_action_icon('about'), "حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
        
    def create_toolbars(self):
        """إنشاء أشرطة الأدوات"""
        main_toolbar = self.addToolBar("الأدوات الرئيسية")
        main_toolbar.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextUnderIcon)
        
        main_toolbar.addAction(IconsManager.get_action_icon('new_project'), "جديد")
        main_toolbar.addAction(IconsManager.get_action_icon('open_project'), "فتح")
        main_toolbar.addSeparator()
        main_toolbar.addAction(IconsManager.get_standard_icon('calculator'), "حاسبة")
        
    def create_main_content(self):
        """إنشاء المحتوى الرئيسي"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QHBoxLayout(central_widget)
        layout.setContentsMargins(8, 8, 8, 8)
        
        # المقسم الرئيسي
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # الشريط الجانبي
        self.create_sidebar(main_splitter)
        
        # المنطقة الوسطى
        self.create_center_area(main_splitter)
        
        # تعيين النسب
        main_splitter.setSizes([250, 750])
        
        layout.addWidget(main_splitter)
        
    def create_sidebar(self, parent):
        """إنشاء الشريط الجانبي"""
        sidebar = QWidget()
        sidebar.setMaximumWidth(300)
        sidebar.setMinimumWidth(200)
        
        layout = QVBoxLayout(sidebar)
        
        # عنوان الشريط الجانبي
        title_label = QLabel("مستكشف المشروع")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                font-weight: bold;
                padding: 12px;
                background-color: {ModernStyleManager.COLORS['primary']};
                color: {ModernStyleManager.COLORS['text_light']};
                border-radius: 8px;
                margin-bottom: 8px;
            }}
        """)
        
        # شجرة المشروع
        self.project_tree = QTreeWidget()
        self.project_tree.setHeaderLabel("المكونات")
        
        # إضافة عناصر تجريبية
        root_item = QTreeWidgetItem(["مشروع تجريبي"])
        root_item.setIcon(0, IconsManager.get_standard_icon('furniture'))
        
        table_item = QTreeWidgetItem(["طاولة طعام"])
        table_item.setIcon(0, IconsManager.get_furniture_icon('table'))
        
        chair_item = QTreeWidgetItem(["كراسي (4 قطع)"])
        chair_item.setIcon(0, IconsManager.get_furniture_icon('chair'))
        
        root_item.addChild(table_item)
        root_item.addChild(chair_item)
        
        self.project_tree.addTopLevelItem(root_item)
        self.project_tree.expandAll()
        
        layout.addWidget(title_label)
        layout.addWidget(self.project_tree)
        
        parent.addWidget(sidebar)
        
    def create_center_area(self, parent):
        """إنشاء المنطقة الوسطى"""
        center_widget = QWidget()
        layout = QVBoxLayout(center_widget)
        
        # تبويبات المحتوى
        self.content_tabs = QTabWidget()
        
        # تبويب الترحيب
        welcome_tab = self.create_welcome_tab()
        self.content_tabs.addTab(welcome_tab, "الترحيب")
        
        # تبويب المشروع
        project_tab = self.create_project_tab()
        self.content_tabs.addTab(project_tab, "المشروع")
        
        # تبويب الحاسبة
        calculator_tab = self.create_calculator_tab()
        self.content_tabs.addTab(calculator_tab, "حاسبة التكلفة")
        
        layout.addWidget(self.content_tabs)
        parent.addWidget(center_widget)
        
    def create_welcome_tab(self):
        """إنشاء تبويب الترحيب"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        welcome_label = QLabel("🪑 مرحباً بك في مصمم الأثاث الاحترافي")
        welcome_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        welcome_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['title']}px;
                font-weight: bold;
                color: {ModernStyleManager.COLORS['primary']};
                margin: 30px;
            }}
        """)
        
        info_text = QTextEdit()
        info_text.setReadOnly(True)
        info_text.setHtml("""
        <div style="text-align: center; font-size: 14px; line-height: 1.6;">
            <h3>🌟 الإصدار المبسط</h3>
            <p>هذا هو الإصدار المبسط من تطبيق مصمم الأثاث الاحترافي.</p>
            
            <h4>✅ الميزات المتوفرة:</h4>
            <ul style="text-align: right;">
                <li>واجهة مستخدم حديثة ومهنية</li>
                <li>إدارة المشاريع الأساسية</li>
                <li>حاسبة التكلفة</li>
                <li>نظام الأنماط المتقدم</li>
            </ul>
            
            <h4>🔧 للحصول على الإصدار الكامل:</h4>
            <p>قم بتثبيت المكتبات الإضافية:</p>
            <code>pip install pyserial reportlab arabic-reshaper python-bidi seaborn</code>
            
            <p>ثم شغل التطبيق الكامل:</p>
            <code>python main_application.py</code>
        </div>
        """)
        
        layout.addWidget(welcome_label)
        layout.addWidget(info_text)
        
        return tab
        
    def create_project_tab(self):
        """إنشاء تبويب المشروع"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # جدول المكونات
        components_table = QTableWidget()
        components_table.setColumnCount(4)
        components_table.setHorizontalHeaderLabels(["المكون", "الطول", "العرض", "السماكة"])
        
        # إضافة بيانات تجريبية
        components_table.setRowCount(3)
        components_table.setItem(0, 0, QTableWidgetItem("سطح الطاولة"))
        components_table.setItem(0, 1, QTableWidgetItem("150 سم"))
        components_table.setItem(0, 2, QTableWidgetItem("80 سم"))
        components_table.setItem(0, 3, QTableWidgetItem("3 سم"))
        
        components_table.setItem(1, 0, QTableWidgetItem("أرجل الطاولة"))
        components_table.setItem(1, 1, QTableWidgetItem("75 سم"))
        components_table.setItem(1, 2, QTableWidgetItem("5 سم"))
        components_table.setItem(1, 3, QTableWidgetItem("5 سم"))
        
        components_table.setItem(2, 0, QTableWidgetItem("مقعد الكرسي"))
        components_table.setItem(2, 1, QTableWidgetItem("45 سم"))
        components_table.setItem(2, 2, QTableWidgetItem("40 سم"))
        components_table.setItem(2, 3, QTableWidgetItem("2 سم"))
        
        layout.addWidget(QLabel("مكونات المشروع:"))
        layout.addWidget(components_table)
        
        return tab
        
    def create_calculator_tab(self):
        """إنشاء تبويب الحاسبة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        calc_label = QLabel("🧮 حاسبة التكلفة")
        calc_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                font-weight: bold;
                color: {ModernStyleManager.COLORS['primary']};
                margin: 10px 0;
            }}
        """)
        
        calc_info = QLabel("سيتم تطوير حاسبة التكلفة في الإصدارات القادمة")
        calc_info.setAlignment(Qt.AlignmentFlag.AlignCenter)
        calc_info.setStyleSheet("color: #7F8C8D; font-size: 14px; margin: 50px;")
        
        layout.addWidget(calc_label)
        layout.addWidget(calc_info)
        layout.addStretch()
        
        return tab
        
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = self.statusBar()
        status_bar.showMessage("جاهز - الإصدار المبسط من مصمم الأثاث الاحترافي")
        
        # معلومات إضافية
        version_label = QLabel("الإصدار: 3.0 مبسط")
        status_bar.addPermanentWidget(version_label)
        
    def center_on_screen(self):
        """توسيط النافذة على الشاشة"""
        screen = QApplication.primaryScreen().geometry()
        x = (screen.width() - self.width()) // 2
        y = (screen.height() - self.height()) // 2
        self.move(x, y)
        
    def new_project(self):
        """مشروع جديد"""
        QMessageBox.information(self, "مشروع جديد", "سيتم إنشاء مشروع جديد")
        
    def open_project(self):
        """فتح مشروع"""
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(self, "فتح مشروع", "", "ملفات المشروع (*.json)")
        if file_path:
            QMessageBox.information(self, "فتح مشروع", f"سيتم فتح المشروع: {file_path}")
            
    def show_calculator(self):
        """عرض الحاسبة"""
        self.content_tabs.setCurrentIndex(2)  # التبديل إلى تبويب الحاسبة
        
    def show_about(self):
        """عرض معلومات حول البرنامج"""
        QMessageBox.about(self, "حول البرنامج",
                         "🪑 مصمم الأثاث الاحترافي - الإصدار المبسط\n\n"
                         "الإصدار 3.0 - PyQt6 Edition\n"
                         "تطبيق لتصميم الأثاث وإدارة مصانع الأثاث\n\n"
                         "هذا هو الإصدار المبسط الذي يعمل مع الحد الأدنى من المكتبات.\n"
                         "للحصول على جميع الميزات، يرجى تثبيت المكتبات الإضافية.")


class SimpleFurnitureDesignerApp(QApplication):
    """تطبيق مصمم الأثاث المبسط"""
    
    def __init__(self, argv):
        super().__init__(argv)
        self.setup_application()
        self.show_splash_screen()
        
    def setup_application(self):
        """إعداد التطبيق"""
        self.setApplicationName("Professional Furniture Designer - Simple")
        self.setApplicationVersion("3.0-Simple")
        self.setOrganizationName("Professional Furniture Design")
        
        # إعداد الخط والأنماط
        ModernStyleManager.setup_application_font(self)
        self.setPalette(ModernStyleManager.create_color_palette())
        self.setStyleSheet(ModernStyleManager.get_complete_style())
        
        # التأكد من وجود المجلدات المطلوبة
        IconsManager.ensure_icons_directory()
        
    def show_splash_screen(self):
        """عرض شاشة البداية"""
        self.splash = SimpleSplashScreen()
        self.splash.show()
        
        # تأخير بسيط ثم عرض النافذة الرئيسية
        QTimer.singleShot(2000, self.show_main_window)
        
    def show_main_window(self):
        """عرض النافذة الرئيسية"""
        self.splash.close()
        
        self.main_window = SimpleMainWindow()
        self.main_window.show()


def main():
    """الدالة الرئيسية"""
    app = SimpleFurnitureDesignerApp(sys.argv)
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
