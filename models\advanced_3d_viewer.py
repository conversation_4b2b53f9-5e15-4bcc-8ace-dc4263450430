#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
معاين ثلاثي الأبعاد متقدم لنماذج الأثاث مع إضاءة وظلال واقعية
Advanced 3D Viewer for Furniture Models with Realistic Lighting and Shadows
"""

import os
import numpy as np
import trimesh
from typing import List, Dict, Any, Optional, Tuple
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QSlider, QComboBox, QCheckBox, QGroupBox, QFileDialog,
    QMessageBox, QProgressBar, QSplitter, QTextEdit, QSpinBox,
    QDoubleSpinBox, QTabWidget, QFrame, QGridLayout
)
from PySide6.QtCore import Qt, QTimer, Signal, QThread, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import <PERSON><PERSON>ont, Q<PERSON>ixmap, <PERSON><PERSON><PERSON><PERSON>, QColor, QLinearGradient
from PySide6.QtOpenGLWidgets import QOpenGLWidget
from PySide6.QtOpenGL import QOpenGLVersionProfile

try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    from mpl_toolkits.mplot3d import Axes3D
    from mpl_toolkits.mplot3d.art3d import Poly3DCollection
    import matplotlib.colors as mcolors
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

from ui.modern_styles import ModernStyleManager
from ui.icons_manager import IconsManager


class LightingSettings:
    """إعدادات الإضاءة المتقدمة"""

    def __init__(self):
        self.ambient_intensity = 0.3  # شدة الإضاءة المحيطة
        self.diffuse_intensity = 0.7  # شدة الإضاءة المنتشرة
        self.specular_intensity = 0.5  # شدة الإضاءة اللامعة
        self.light_position = [1, 1, 1]  # موقع مصدر الضوء
        self.light_color = [1.0, 1.0, 1.0]  # لون الضوء
        self.shadow_enabled = True  # تفعيل الظلال
        self.shadow_intensity = 0.3  # شدة الظلال


class MaterialSettings:
    """إعدادات المواد والخامات"""

    def __init__(self):
        self.material_type = "خشب"  # نوع المادة
        self.base_color = [0.8, 0.6, 0.4]  # اللون الأساسي
        self.metallic = 0.0  # معامل المعدنية
        self.roughness = 0.8  # معامل الخشونة
        self.transparency = 1.0  # معامل الشفافية
        self.texture_enabled = False  # تفعيل النسيج
        self.texture_scale = 1.0  # مقياس النسيج


class ViewSettings:
    """إعدادات العرض المتقدمة"""

    def __init__(self):
        self.camera_distance = 5.0  # مسافة الكاميرا
        self.camera_elevation = 30.0  # زاوية الارتفاع
        self.camera_azimuth = 45.0  # زاوية الدوران
        self.field_of_view = 60.0  # مجال الرؤية
        self.background_color = [0.95, 0.95, 0.95]  # لون الخلفية
        self.grid_enabled = True  # تفعيل الشبكة
        self.axes_enabled = True  # تفعيل المحاور
        self.wireframe_enabled = False  # تفعيل الإطار السلكي


class Model3DInfo:
    """معلومات النموذج ثلاثي الأبعاد"""

    def __init__(self, file_path: str):
        self.file_path = file_path
        self.file_name = os.path.basename(file_path)
        self.file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
        self.format = os.path.splitext(file_path)[1].lower()
        self.mesh = None
        self.components = []
        self.bounds = None
        self.volume = 0.0
        self.surface_area = 0.0
        self.vertices_count = 0
        self.faces_count = 0
        self.is_loaded = False
        self.load_error = None


class ModelLoader(QThread):
    """خيط تحميل النماذج ثلاثية الأبعاد"""

    progress_updated = Signal(int, str)
    model_loaded = Signal(object)  # Model3DInfo
    loading_failed = Signal(str)

    def __init__(self, file_path: str):
        super().__init__()
        self.file_path = file_path

    def run(self):
        """تشغيل عملية التحميل"""
        try:
            self.progress_updated.emit(10, "بدء تحميل النموذج...")

            # إنشاء كائن معلومات النموذج
            model_info = Model3DInfo(self.file_path)

            self.progress_updated.emit(30, "قراءة الملف...")

            # تحميل النموذج باستخدام trimesh
            mesh = trimesh.load(self.file_path)
            model_info.mesh = mesh

            self.progress_updated.emit(50, "تحليل النموذج...")

            # تحليل النموذج
            if isinstance(mesh, trimesh.Scene):
                # نموذج متعدد المكونات
                model_info.components = list(mesh.geometry.keys())
                model_info.bounds = mesh.bounds if hasattr(mesh, 'bounds') else None
                model_info.volume = sum(geom.volume for geom in mesh.geometry.values()
                                      if hasattr(geom, 'volume'))
                model_info.surface_area = sum(geom.area for geom in mesh.geometry.values()
                                            if hasattr(geom, 'area'))
                model_info.vertices_count = sum(len(geom.vertices) for geom in mesh.geometry.values()
                                              if hasattr(geom, 'vertices'))
                model_info.faces_count = sum(len(geom.faces) for geom in mesh.geometry.values()
                                           if hasattr(geom, 'faces'))
            else:
                # نموذج مكون واحد
                model_info.components = ["المكون الرئيسي"]
                model_info.bounds = mesh.bounds
                model_info.volume = float(mesh.volume) if hasattr(mesh, 'volume') else 0.0
                model_info.surface_area = float(mesh.area) if hasattr(mesh, 'area') else 0.0
                model_info.vertices_count = len(mesh.vertices) if hasattr(mesh, 'vertices') else 0
                model_info.faces_count = len(mesh.faces) if hasattr(mesh, 'faces') else 0

            self.progress_updated.emit(80, "إنهاء التحليل...")

            model_info.is_loaded = True

            self.progress_updated.emit(100, "تم التحميل بنجاح!")
            self.model_loaded.emit(model_info)

        except Exception as e:
            error_msg = f"خطأ في تحميل النموذج: {str(e)}"
            self.loading_failed.emit(error_msg)


class Advanced3DViewer(QWidget):
    """معاين ثلاثي الأبعاد متقدم مع إضاءة وظلال واقعية"""

    model_selected = Signal(object)  # Model3DInfo
    view_changed = Signal()  # إشارة تغيير العرض

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_model = None
        self.supported_formats = ['.obj', '.stl', '.dae', '.ply', '.off', '.3mf']

        # إعدادات متقدمة
        self.lighting_settings = LightingSettings()
        self.material_settings = MaterialSettings()
        self.view_settings = ViewSettings()

        # متغيرات التحكم في العرض
        self.rotation_speed = 1.0
        self.zoom_sensitivity = 0.1
        self.auto_rotate = False
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self.animate_rotation)

        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)

        # شريط الأدوات العلوي
        self.create_toolbar(layout)

        # المحتوى الرئيسي
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # منطقة العرض
        self.create_viewer_area(main_splitter)

        # لوحة التحكم
        self.create_control_panel(main_splitter)

        # تعيين النسب
        main_splitter.setSizes([700, 300])

        layout.addWidget(main_splitter)

    def create_toolbar(self, parent_layout):
        """إنشاء شريط الأدوات"""
        toolbar_widget = QWidget()
        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(0, 0, 0, 0)

        # زر تحميل نموذج
        load_btn = QPushButton("تحميل نموذج")
        load_btn.setIcon(IconsManager.get_standard_icon('open'))
        load_btn.clicked.connect(self.load_model)

        # زر حفظ الصورة
        save_image_btn = QPushButton("حفظ صورة")
        save_image_btn.setIcon(IconsManager.get_standard_icon('save'))
        save_image_btn.clicked.connect(self.save_screenshot)

        # زر إعادة تعيين العرض
        reset_view_btn = QPushButton("إعادة تعيين العرض")
        reset_view_btn.setIcon(IconsManager.get_standard_icon('refresh'))
        reset_view_btn.clicked.connect(self.reset_view)

        # معلومات النموذج الحالي
        self.model_info_label = QLabel("لا يوجد نموذج محمل")
        self.model_info_label.setStyleSheet(f"""
            QLabel {{
                background-color: {ModernStyleManager.COLORS['card']};
                padding: 8px;
                border-radius: 4px;
                font-weight: bold;
            }}
        """)

        toolbar_layout.addWidget(load_btn)
        toolbar_layout.addWidget(save_image_btn)
        toolbar_layout.addWidget(reset_view_btn)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.model_info_label)

        parent_layout.addWidget(toolbar_widget)

    def create_viewer_area(self, parent_splitter):
        """إنشاء منطقة العرض"""
        viewer_widget = QWidget()
        viewer_layout = QVBoxLayout(viewer_widget)
        viewer_layout.setContentsMargins(4, 4, 4, 4)

        # عنوان منطقة العرض
        title_label = QLabel("معاين ثلاثي الأبعاد")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                font-weight: bold;
                padding: 8px;
                background-color: {ModernStyleManager.COLORS['primary']};
                color: {ModernStyleManager.COLORS['text_light']};
                border-radius: 6px;
            }}
        """)

        # منطقة العرض الفعلية
        if MATPLOTLIB_AVAILABLE:
            self.create_matplotlib_viewer(viewer_layout)
        else:
            self.create_fallback_viewer(viewer_layout)

        # شريط التقدم للتحميل
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid {ModernStyleManager.COLORS['border']};
                border-radius: 5px;
                text-align: center;
                background-color: {ModernStyleManager.COLORS['surface']};
            }}
            QProgressBar::chunk {{
                background-color: {ModernStyleManager.COLORS['secondary']};
                border-radius: 3px;
            }}
        """)

        viewer_layout.addWidget(title_label)
        viewer_layout.addWidget(self.progress_bar)

        parent_splitter.addWidget(viewer_widget)

    def create_matplotlib_viewer(self, parent_layout):
        """إنشاء معاين matplotlib"""
        # إنشاء الشكل
        self.figure = Figure(figsize=(8, 6), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        self.canvas.setStyleSheet(f"""
            background-color: {ModernStyleManager.COLORS['surface']};
            border: 1px solid {ModernStyleManager.COLORS['border']};
            border-radius: 8px;
        """)

        # إنشاء المحور ثلاثي الأبعاد
        self.ax = self.figure.add_subplot(111, projection='3d')
        self.ax.set_title("معاين النماذج ثلاثية الأبعاد", fontsize=14, fontweight='bold')

        # إعداد المحور
        self.setup_3d_axis()

        parent_layout.addWidget(self.canvas)

    def create_fallback_viewer(self, parent_layout):
        """إنشاء معاين بديل"""
        fallback_label = QLabel("معاين ثلاثي الأبعاد غير متاح\nيرجى تثبيت matplotlib")
        fallback_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        fallback_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                color: {ModernStyleManager.COLORS['text_muted']};
                background-color: {ModernStyleManager.COLORS['card']};
                border: 2px dashed {ModernStyleManager.COLORS['border']};
                border-radius: 8px;
                padding: 50px;
            }}
        """)

        parent_layout.addWidget(fallback_label)

    def create_control_panel(self, parent_splitter):
        """إنشاء لوحة التحكم المتقدمة"""
        control_widget = QWidget()
        control_widget.setMaximumWidth(400)
        control_layout = QVBoxLayout(control_widget)
        control_layout.setContentsMargins(4, 4, 4, 4)

        # عنوان لوحة التحكم
        title_label = QLabel("لوحة التحكم المتقدمة")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                font-weight: bold;
                padding: 8px;
                background-color: {ModernStyleManager.COLORS['secondary']};
                color: {ModernStyleManager.COLORS['text_light']};
                border-radius: 6px;
                margin-bottom: 8px;
            }}
        """)

        # إنشاء تبويبات للتحكم
        control_tabs = QTabWidget()
        control_tabs.setStyleSheet(f"""
            QTabWidget::pane {{
                border: 1px solid {ModernStyleManager.COLORS['border']};
                border-radius: 6px;
                background-color: {ModernStyleManager.COLORS['surface']};
            }}
            QTabBar::tab {{
                background-color: {ModernStyleManager.COLORS['card']};
                padding: 8px 12px;
                margin: 2px;
                border-radius: 4px;
            }}
            QTabBar::tab:selected {{
                background-color: {ModernStyleManager.COLORS['primary']};
                color: {ModernStyleManager.COLORS['text_light']};
            }}
        """)

        # تبويب معلومات النموذج
        model_info_tab = QWidget()
        self.create_model_info_tab(model_info_tab)
        control_tabs.addTab(model_info_tab, "معلومات النموذج")

        # تبويب إعدادات العرض
        view_settings_tab = QWidget()
        self.create_view_settings_tab(view_settings_tab)
        control_tabs.addTab(view_settings_tab, "إعدادات العرض")

        # تبويب الإضاءة والمواد
        lighting_tab = QWidget()
        self.create_lighting_tab(lighting_tab)
        control_tabs.addTab(lighting_tab, "الإضاءة والمواد")

        # تبويب التحليل
        analysis_tab = QWidget()
        self.create_analysis_tab(analysis_tab)
        control_tabs.addTab(analysis_tab, "التحليل")

        control_layout.addWidget(title_label)
        control_layout.addWidget(control_tabs)

        parent_splitter.addWidget(control_widget)

    def create_model_info_tab(self, tab_widget):
        """إنشاء تبويب معلومات النموذج"""
        layout = QVBoxLayout(tab_widget)
        layout.setContentsMargins(8, 8, 8, 8)

        # معلومات الملف
        file_group = QGroupBox("معلومات الملف")
        file_layout = QGridLayout(file_group)

        self.file_name_label = QLabel("الملف: غير محدد")
        self.file_size_label = QLabel("الحجم: غير محدد")
        self.format_label = QLabel("الصيغة: غير محددة")

        file_layout.addWidget(QLabel("اسم الملف:"), 0, 0)
        file_layout.addWidget(self.file_name_label, 0, 1)
        file_layout.addWidget(QLabel("حجم الملف:"), 1, 0)
        file_layout.addWidget(self.file_size_label, 1, 1)
        file_layout.addWidget(QLabel("صيغة الملف:"), 2, 0)
        file_layout.addWidget(self.format_label, 2, 1)

        # معلومات النموذج
        model_group = QGroupBox("معلومات النموذج")
        model_layout = QGridLayout(model_group)

        self.vertices_label = QLabel("الرؤوس: 0")
        self.faces_label = QLabel("الوجوه: 0")
        self.volume_label = QLabel("الحجم: 0")
        self.area_label = QLabel("المساحة: 0")

        model_layout.addWidget(QLabel("عدد الرؤوس:"), 0, 0)
        model_layout.addWidget(self.vertices_label, 0, 1)
        model_layout.addWidget(QLabel("عدد الوجوه:"), 1, 0)
        model_layout.addWidget(self.faces_label, 1, 1)
        model_layout.addWidget(QLabel("الحجم:"), 2, 0)
        model_layout.addWidget(self.volume_label, 2, 1)
        model_layout.addWidget(QLabel("المساحة:"), 3, 0)
        model_layout.addWidget(self.area_label, 3, 1)

        layout.addWidget(file_group)
        layout.addWidget(model_group)
        layout.addStretch()

    def create_view_settings_tab(self, tab_widget):
        """إنشاء تبويب إعدادات العرض"""
        layout = QVBoxLayout(tab_widget)
        layout.setContentsMargins(8, 8, 8, 8)

        # إعدادات الكاميرا
        camera_group = QGroupBox("إعدادات الكاميرا")
        camera_layout = QGridLayout(camera_group)

        # مسافة الكاميرا
        camera_layout.addWidget(QLabel("المسافة:"), 0, 0)
        self.distance_slider = QSlider(Qt.Orientation.Horizontal)
        self.distance_slider.setRange(1, 20)
        self.distance_slider.setValue(int(self.view_settings.camera_distance))
        self.distance_slider.valueChanged.connect(self.update_camera_distance)
        camera_layout.addWidget(self.distance_slider, 0, 1)

        # زاوية الارتفاع
        camera_layout.addWidget(QLabel("الارتفاع:"), 1, 0)
        self.elevation_slider = QSlider(Qt.Orientation.Horizontal)
        self.elevation_slider.setRange(-90, 90)
        self.elevation_slider.setValue(int(self.view_settings.camera_elevation))
        self.elevation_slider.valueChanged.connect(self.update_camera_elevation)
        camera_layout.addWidget(self.elevation_slider, 1, 1)

        # زاوية الدوران
        camera_layout.addWidget(QLabel("الدوران:"), 2, 0)
        self.azimuth_slider = QSlider(Qt.Orientation.Horizontal)
        self.azimuth_slider.setRange(0, 360)
        self.azimuth_slider.setValue(int(self.view_settings.camera_azimuth))
        self.azimuth_slider.valueChanged.connect(self.update_camera_azimuth)
        camera_layout.addWidget(self.azimuth_slider, 2, 1)

        # خيارات العرض
        display_group = QGroupBox("خيارات العرض")
        display_layout = QVBoxLayout(display_group)

        self.wireframe_check = QCheckBox("عرض الإطار السلكي")
        self.wireframe_check.setChecked(self.view_settings.wireframe_enabled)
        self.wireframe_check.toggled.connect(self.toggle_wireframe)

        self.axes_check = QCheckBox("عرض المحاور")
        self.axes_check.setChecked(self.view_settings.axes_enabled)
        self.axes_check.toggled.connect(self.toggle_axes)

        self.grid_check = QCheckBox("عرض الشبكة")
        self.grid_check.setChecked(self.view_settings.grid_enabled)
        self.grid_check.toggled.connect(self.toggle_grid)

        self.auto_rotate_check = QCheckBox("دوران تلقائي")
        self.auto_rotate_check.setChecked(self.auto_rotate)
        self.auto_rotate_check.toggled.connect(self.toggle_auto_rotate)

        display_layout.addWidget(self.wireframe_check)
        display_layout.addWidget(self.axes_check)
        display_layout.addWidget(self.grid_check)
        display_layout.addWidget(self.auto_rotate_check)

        layout.addWidget(camera_group)
        layout.addWidget(display_group)
        layout.addStretch()

    def create_lighting_tab(self, tab_widget):
        """إنشاء تبويب الإضاءة والمواد"""
        layout = QVBoxLayout(tab_widget)
        layout.setContentsMargins(8, 8, 8, 8)

        # إعدادات الإضاءة
        lighting_group = QGroupBox("إعدادات الإضاءة")
        lighting_layout = QGridLayout(lighting_group)

        # شدة الإضاءة المحيطة
        lighting_layout.addWidget(QLabel("الإضاءة المحيطة:"), 0, 0)
        self.ambient_slider = QSlider(Qt.Orientation.Horizontal)
        self.ambient_slider.setRange(0, 100)
        self.ambient_slider.setValue(int(self.lighting_settings.ambient_intensity * 100))
        self.ambient_slider.valueChanged.connect(self.update_ambient_lighting)
        lighting_layout.addWidget(self.ambient_slider, 0, 1)

        # شدة الإضاءة المنتشرة
        lighting_layout.addWidget(QLabel("الإضاءة المنتشرة:"), 1, 0)
        self.diffuse_slider = QSlider(Qt.Orientation.Horizontal)
        self.diffuse_slider.setRange(0, 100)
        self.diffuse_slider.setValue(int(self.lighting_settings.diffuse_intensity * 100))
        self.diffuse_slider.valueChanged.connect(self.update_diffuse_lighting)
        lighting_layout.addWidget(self.diffuse_slider, 1, 1)

        # شدة الإضاءة اللامعة
        lighting_layout.addWidget(QLabel("الإضاءة اللامعة:"), 2, 0)
        self.specular_slider = QSlider(Qt.Orientation.Horizontal)
        self.specular_slider.setRange(0, 100)
        self.specular_slider.setValue(int(self.lighting_settings.specular_intensity * 100))
        self.specular_slider.valueChanged.connect(self.update_specular_lighting)
        lighting_layout.addWidget(self.specular_slider, 2, 1)

        # تفعيل الظلال
        self.shadows_check = QCheckBox("تفعيل الظلال")
        self.shadows_check.setChecked(self.lighting_settings.shadow_enabled)
        self.shadows_check.toggled.connect(self.toggle_shadows)
        lighting_layout.addWidget(self.shadows_check, 3, 0, 1, 2)

        # إعدادات المواد
        material_group = QGroupBox("إعدادات المواد")
        material_layout = QGridLayout(material_group)

        # نوع المادة
        material_layout.addWidget(QLabel("نوع المادة:"), 0, 0)
        self.material_combo = QComboBox()
        self.material_combo.addItems(["خشب", "معدن", "بلاستيك", "زجاج", "قماش", "جلد"])
        self.material_combo.setCurrentText(self.material_settings.material_type)
        self.material_combo.currentTextChanged.connect(self.update_material_type)
        material_layout.addWidget(self.material_combo, 0, 1)

        # معامل الخشونة
        material_layout.addWidget(QLabel("الخشونة:"), 1, 0)
        self.roughness_slider = QSlider(Qt.Orientation.Horizontal)
        self.roughness_slider.setRange(0, 100)
        self.roughness_slider.setValue(int(self.material_settings.roughness * 100))
        self.roughness_slider.valueChanged.connect(self.update_roughness)
        material_layout.addWidget(self.roughness_slider, 1, 1)

        # معامل المعدنية
        material_layout.addWidget(QLabel("المعدنية:"), 2, 0)
        self.metallic_slider = QSlider(Qt.Orientation.Horizontal)
        self.metallic_slider.setRange(0, 100)
        self.metallic_slider.setValue(int(self.material_settings.metallic * 100))
        self.metallic_slider.valueChanged.connect(self.update_metallic)
        material_layout.addWidget(self.metallic_slider, 2, 1)

        # معامل الشفافية
        material_layout.addWidget(QLabel("الشفافية:"), 3, 0)
        self.transparency_slider = QSlider(Qt.Orientation.Horizontal)
        self.transparency_slider.setRange(0, 100)
        self.transparency_slider.setValue(int(self.material_settings.transparency * 100))
        self.transparency_slider.valueChanged.connect(self.update_transparency)
        material_layout.addWidget(self.transparency_slider, 3, 1)

        layout.addWidget(lighting_group)
        layout.addWidget(material_group)
        layout.addStretch()

    def create_analysis_tab(self, tab_widget):
        """إنشاء تبويب التحليل"""
        layout = QVBoxLayout(tab_widget)
        layout.setContentsMargins(8, 8, 8, 8)

        # أزرار التحليل
        analysis_group = QGroupBox("أدوات التحليل")
        analysis_layout = QVBoxLayout(analysis_group)

        analyze_btn = QPushButton("تحليل الأبعاد")
        analyze_btn.setIcon(IconsManager.get_standard_icon('ruler'))
        analyze_btn.clicked.connect(self.analyze_dimensions)

        measure_btn = QPushButton("أداة القياس")
        measure_btn.setIcon(IconsManager.get_standard_icon('measure'))
        measure_btn.clicked.connect(self.activate_measure_tool)

        export_data_btn = QPushButton("تصدير البيانات")
        export_data_btn.setIcon(IconsManager.get_standard_icon('export'))
        export_data_btn.clicked.connect(self.export_model_data)

        screenshot_btn = QPushButton("لقطة شاشة")
        screenshot_btn.setIcon(IconsManager.get_standard_icon('camera'))
        screenshot_btn.clicked.connect(self.save_screenshot)

        analysis_layout.addWidget(analyze_btn)
        analysis_layout.addWidget(measure_btn)
        analysis_layout.addWidget(export_data_btn)
        analysis_layout.addWidget(screenshot_btn)

        # معلومات التحليل
        info_group = QGroupBox("نتائج التحليل")
        info_layout = QVBoxLayout(info_group)

        self.analysis_text = QTextEdit()
        self.analysis_text.setMaximumHeight(150)
        self.analysis_text.setPlainText("لا توجد نتائج تحليل متاحة")
        info_layout.addWidget(self.analysis_text)

        layout.addWidget(analysis_group)
        layout.addWidget(info_group)
        layout.addStretch()

    def create_model_info_group(self, parent_layout):
        """إنشاء مجموعة معلومات النموذج"""
        info_group = QGroupBox("معلومات النموذج")
        info_layout = QVBoxLayout(info_group)

        # معلومات الملف
        self.file_name_label = QLabel("الملف: غير محدد")
        self.file_size_label = QLabel("الحجم: غير محدد")
        self.format_label = QLabel("الصيغة: غير محددة")

        # معلومات النموذج
        self.vertices_label = QLabel("الرؤوس: 0")
        self.faces_label = QLabel("الوجوه: 0")
        self.volume_label = QLabel("الحجم: 0")
        self.area_label = QLabel("المساحة: 0")

        # إضافة التسميات
        for label in [self.file_name_label, self.file_size_label, self.format_label,
                     self.vertices_label, self.faces_label, self.volume_label, self.area_label]:
            label.setStyleSheet("QLabel { padding: 4px; }")
            info_layout.addWidget(label)

        parent_layout.addWidget(info_group)

    def create_view_settings_group(self, parent_layout):
        """إنشاء مجموعة إعدادات العرض"""
        view_group = QGroupBox("إعدادات العرض")
        view_layout = QVBoxLayout(view_group)

        # خيارات العرض
        self.wireframe_check = QCheckBox("عرض الإطار السلكي")
        self.axes_check = QCheckBox("عرض المحاور")
        self.axes_check.setChecked(True)

        # ألوان العرض
        color_label = QLabel("لون النموذج:")
        self.color_combo = QComboBox()
        self.color_combo.addItems(["أزرق", "أحمر", "أخضر", "رمادي", "ذهبي"])

        view_layout.addWidget(self.wireframe_check)
        view_layout.addWidget(self.axes_check)
        view_layout.addWidget(color_label)
        view_layout.addWidget(self.color_combo)

        parent_layout.addWidget(view_group)

    def create_analysis_group(self, parent_layout):
        """إنشاء مجموعة التحليل"""
        analysis_group = QGroupBox("تحليل النموذج")
        analysis_layout = QVBoxLayout(analysis_group)

        # أزرار التحليل
        analyze_btn = QPushButton("تحليل الأبعاد")
        analyze_btn.setIcon(IconsManager.get_standard_icon('ruler'))
        analyze_btn.clicked.connect(self.analyze_dimensions)

        export_data_btn = QPushButton("تصدير البيانات")
        export_data_btn.setIcon(IconsManager.get_standard_icon('export'))
        export_data_btn.clicked.connect(self.export_model_data)

        analysis_layout.addWidget(analyze_btn)
        analysis_layout.addWidget(export_data_btn)

        parent_layout.addWidget(analysis_group)

    def setup_3d_axis(self):
        """إعداد المحور ثلاثي الأبعاد"""
        if not MATPLOTLIB_AVAILABLE:
            return

        self.ax.clear()
        self.ax.set_xlabel('X (مم)')
        self.ax.set_ylabel('Y (مم)')
        self.ax.set_zlabel('Z (مم)')

        # إعداد الشبكة
        self.ax.grid(True, alpha=0.3)

        # إعداد الخلفية
        self.ax.xaxis.pane.fill = False
        self.ax.yaxis.pane.fill = False
        self.ax.zaxis.pane.fill = False

        self.canvas.draw()

    # دوال التحكم في الكاميرا والعرض
    def update_camera_distance(self, value):
        """تحديث مسافة الكاميرا"""
        self.view_settings.camera_distance = value
        self.update_view()

    def update_camera_elevation(self, value):
        """تحديث زاوية ارتفاع الكاميرا"""
        self.view_settings.camera_elevation = value
        self.update_view()

    def update_camera_azimuth(self, value):
        """تحديث زاوية دوران الكاميرا"""
        self.view_settings.camera_azimuth = value
        self.update_view()

    def toggle_wireframe(self, enabled):
        """تبديل عرض الإطار السلكي"""
        self.view_settings.wireframe_enabled = enabled
        self.update_view()

    def toggle_axes(self, enabled):
        """تبديل عرض المحاور"""
        self.view_settings.axes_enabled = enabled
        self.update_view()

    def toggle_grid(self, enabled):
        """تبديل عرض الشبكة"""
        self.view_settings.grid_enabled = enabled
        self.update_view()

    def toggle_auto_rotate(self, enabled):
        """تبديل الدوران التلقائي"""
        self.auto_rotate = enabled
        if enabled:
            self.animation_timer.start(50)  # 50ms = 20 FPS
        else:
            self.animation_timer.stop()

    def animate_rotation(self):
        """تحريك الدوران التلقائي"""
        if self.auto_rotate:
            self.view_settings.camera_azimuth += self.rotation_speed
            if self.view_settings.camera_azimuth >= 360:
                self.view_settings.camera_azimuth = 0
            self.azimuth_slider.setValue(int(self.view_settings.camera_azimuth))
            self.update_view()

    # دوال التحكم في الإضاءة والمواد
    def update_ambient_lighting(self, value):
        """تحديث شدة الإضاءة المحيطة"""
        self.lighting_settings.ambient_intensity = value / 100.0
        self.update_view()

    def update_diffuse_lighting(self, value):
        """تحديث شدة الإضاءة المنتشرة"""
        self.lighting_settings.diffuse_intensity = value / 100.0
        self.update_view()

    def update_specular_lighting(self, value):
        """تحديث شدة الإضاءة اللامعة"""
        self.lighting_settings.specular_intensity = value / 100.0
        self.update_view()

    def toggle_shadows(self, enabled):
        """تبديل تفعيل الظلال"""
        self.lighting_settings.shadow_enabled = enabled
        self.update_view()

    def update_material_type(self, material_type):
        """تحديث نوع المادة"""
        self.material_settings.material_type = material_type
        # تحديث الخصائص حسب نوع المادة
        material_presets = {
            "خشب": {"base_color": [0.8, 0.6, 0.4], "metallic": 0.0, "roughness": 0.8},
            "معدن": {"base_color": [0.7, 0.7, 0.7], "metallic": 0.9, "roughness": 0.1},
            "بلاستيك": {"base_color": [0.2, 0.4, 0.8], "metallic": 0.0, "roughness": 0.5},
            "زجاج": {"base_color": [0.9, 0.9, 0.9], "metallic": 0.0, "roughness": 0.0},
            "قماش": {"base_color": [0.6, 0.3, 0.2], "metallic": 0.0, "roughness": 0.9},
            "جلد": {"base_color": [0.4, 0.2, 0.1], "metallic": 0.0, "roughness": 0.7}
        }

        if material_type in material_presets:
            preset = material_presets[material_type]
            self.material_settings.base_color = preset["base_color"]
            self.material_settings.metallic = preset["metallic"]
            self.material_settings.roughness = preset["roughness"]

            # تحديث المنزلقات
            self.metallic_slider.setValue(int(preset["metallic"] * 100))
            self.roughness_slider.setValue(int(preset["roughness"] * 100))

        self.update_view()

    def update_roughness(self, value):
        """تحديث معامل الخشونة"""
        self.material_settings.roughness = value / 100.0
        self.update_view()

    def update_metallic(self, value):
        """تحديث معامل المعدنية"""
        self.material_settings.metallic = value / 100.0
        self.update_view()

    def update_transparency(self, value):
        """تحديث معامل الشفافية"""
        self.material_settings.transparency = value / 100.0
        self.update_view()

    def update_view(self):
        """تحديث العرض مع الإعدادات الجديدة"""
        if self.current_model and MATPLOTLIB_AVAILABLE:
            self.display_model()
            self.view_changed.emit()

    def activate_measure_tool(self):
        """تفعيل أداة القياس"""
        QMessageBox.information(self, "أداة القياس",
                              "أداة القياس قيد التطوير.\n"
                              "ستتيح لك قياس المسافات والزوايا في النموذج.")

    def load_model(self):
        """تحميل نموذج ثلاثي الأبعاد"""
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(
            self,
            "اختر نموذج ثلاثي الأبعاد",
            "",
            "نماذج ثلاثية الأبعاد (*.obj *.stl *.dae *.ply *.off *.3mf);;جميع الملفات (*)"
        )

        if file_path:
            self.load_model_from_path(file_path)

    def load_model_from_path(self, file_path: str):
        """تحميل نموذج من مسار محدد"""
        # إظهار شريط التقدم
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # إنشاء خيط التحميل
        self.loader_thread = ModelLoader(file_path)
        self.loader_thread.progress_updated.connect(self.update_loading_progress)
        self.loader_thread.model_loaded.connect(self.on_model_loaded)
        self.loader_thread.loading_failed.connect(self.on_loading_failed)
        self.loader_thread.start()

    def update_loading_progress(self, value: int, message: str):
        """تحديث تقدم التحميل"""
        self.progress_bar.setValue(value)
        self.model_info_label.setText(message)

    def on_model_loaded(self, model_info: Model3DInfo):
        """عند تحميل النموذج بنجاح"""
        self.current_model = model_info
        self.progress_bar.setVisible(False)

        # تحديث معلومات النموذج
        self.update_model_info()

        # عرض النموذج
        self.display_model()

        # إرسال إشارة
        self.model_selected.emit(model_info)

    def on_loading_failed(self, error_message: str):
        """عند فشل التحميل"""
        self.progress_bar.setVisible(False)
        self.model_info_label.setText("فشل التحميل")

        QMessageBox.critical(self, "خطأ في التحميل", error_message)

    def update_model_info(self):
        """تحديث معلومات النموذج"""
        if not self.current_model:
            return

        model = self.current_model

        # معلومات الملف
        self.file_name_label.setText(f"الملف: {model.file_name}")
        self.file_size_label.setText(f"الحجم: {model.file_size / 1024:.1f} KB")
        self.format_label.setText(f"الصيغة: {model.format.upper()}")

        # معلومات النموذج
        self.vertices_label.setText(f"الرؤوس: {model.vertices_count:,}")
        self.faces_label.setText(f"الوجوه: {model.faces_count:,}")
        self.volume_label.setText(f"الحجم: {model.volume:.2f} مم³")
        self.area_label.setText(f"المساحة: {model.surface_area:.2f} مم²")

        # تحديث شريط المعلومات
        self.model_info_label.setText(f"تم تحميل: {model.file_name}")

    def display_model(self):
        """عرض النموذج مع الإضاءة والظلال المتقدمة"""
        if not MATPLOTLIB_AVAILABLE or not self.current_model:
            return

        try:
            mesh = self.current_model.mesh

            self.ax.clear()

            # تطبيق إعدادات الكاميرا
            self.ax.view_init(
                elev=self.view_settings.camera_elevation,
                azim=self.view_settings.camera_azimuth
            )
            self.ax.dist = self.view_settings.camera_distance

            # تطبيق لون الخلفية
            bg_color = self.view_settings.background_color
            self.figure.patch.set_facecolor(bg_color)
            self.ax.xaxis.pane.fill = False
            self.ax.yaxis.pane.fill = False
            self.ax.zaxis.pane.fill = False

            if isinstance(mesh, trimesh.Scene):
                # عرض مشهد متعدد المكونات
                for name, geometry in mesh.geometry.items():
                    if hasattr(geometry, 'vertices') and hasattr(geometry, 'faces'):
                        self.plot_mesh_advanced(geometry)
            else:
                # عرض مكون واحد
                self.plot_mesh_advanced(mesh)

            # إعداد المحور مع الإعدادات المتقدمة
            self.setup_advanced_axis()

            # تحديث العرض
            self.canvas.draw()

        except Exception as e:
            QMessageBox.warning(self, "خطأ في العرض", f"لا يمكن عرض النموذج: {str(e)}")

    def plot_mesh_advanced(self, mesh):
        """رسم شبكة ثلاثية الأبعاد مع إضاءة متقدمة"""
        if not hasattr(mesh, 'vertices') or not hasattr(mesh, 'faces'):
            return

        vertices = mesh.vertices
        faces = mesh.faces

        # حساب الأسطح للإضاءة
        face_vertices = vertices[faces]

        # حساب الأشعة العادية للوجوه
        v0 = face_vertices[:, 0]
        v1 = face_vertices[:, 1]
        v2 = face_vertices[:, 2]

        # حساب الأشعة العادية
        normals = np.cross(v1 - v0, v2 - v0)
        normals = normals / np.linalg.norm(normals, axis=1, keepdims=True)

        # موقع مصدر الضوء
        light_pos = np.array(self.lighting_settings.light_position)

        # حساب الإضاءة لكل وجه
        face_centers = np.mean(face_vertices, axis=1)
        light_dirs = light_pos - face_centers
        light_dirs = light_dirs / np.linalg.norm(light_dirs, axis=1, keepdims=True)

        # حساب شدة الإضاءة المنتشرة
        diffuse_intensity = np.maximum(0, np.sum(normals * light_dirs, axis=1))
        diffuse_intensity *= self.lighting_settings.diffuse_intensity

        # إضافة الإضاءة المحيطة
        ambient = self.lighting_settings.ambient_intensity
        total_intensity = ambient + diffuse_intensity
        total_intensity = np.clip(total_intensity, 0, 1)

        # تطبيق لون المادة
        base_color = np.array(self.material_settings.base_color)

        # حساب الألوان النهائية
        colors = []
        for intensity in total_intensity:
            color = base_color * intensity

            # تطبيق تأثير المعدنية
            if self.material_settings.metallic > 0:
                metallic_factor = self.material_settings.metallic
                color = color * (1 - metallic_factor) + np.array([0.8, 0.8, 0.8]) * metallic_factor

            # تطبيق الشفافية
            alpha = self.material_settings.transparency
            colors.append([*color, alpha])

        # إنشاء مجموعة الأسطح ثلاثية الأبعاد
        if self.view_settings.wireframe_enabled:
            # عرض الإطار السلكي
            poly3d = Poly3DCollection(face_vertices,
                                    facecolors='none',
                                    edgecolors='black',
                                    linewidths=0.5,
                                    alpha=0.8)
        else:
            # عرض الأسطح الملونة
            poly3d = Poly3DCollection(face_vertices,
                                    facecolors=colors,
                                    edgecolors='black' if self.material_settings.roughness > 0.5 else 'none',
                                    linewidths=0.1,
                                    alpha=self.material_settings.transparency)

        self.ax.add_collection3d(poly3d)

        # إضافة الظلال إذا كانت مفعلة
        if self.lighting_settings.shadow_enabled:
            self.add_shadows(vertices, faces)

    def add_shadows(self, vertices, faces):
        """إضافة الظلال للنموذج"""
        try:
            # إسقاط الظلال على المستوى السفلي
            shadow_z = np.min(vertices[:, 2]) - 0.1

            # حساب الظلال المسقطة
            light_pos = np.array(self.lighting_settings.light_position)
            shadow_vertices = []

            for vertex in vertices:
                # حساب اتجاه الإسقاط
                direction = vertex - light_pos
                # إيجاد نقطة التقاطع مع المستوى
                t = (shadow_z - light_pos[2]) / direction[2] if direction[2] != 0 else 0
                shadow_point = light_pos + t * direction
                shadow_vertices.append([shadow_point[0], shadow_point[1], shadow_z])

            shadow_vertices = np.array(shadow_vertices)
            shadow_faces = vertices[faces]

            # رسم الظلال
            shadow_color = [0.3, 0.3, 0.3, self.lighting_settings.shadow_intensity]
            shadow_poly = Poly3DCollection(shadow_faces,
                                         facecolors=shadow_color,
                                         edgecolors='none',
                                         alpha=self.lighting_settings.shadow_intensity)

            self.ax.add_collection3d(shadow_poly)

        except Exception as e:
            print(f"خطأ في إضافة الظلال: {e}")

    def setup_advanced_axis(self):
        """إعداد المحور مع الإعدادات المتقدمة"""
        if not MATPLOTLIB_AVAILABLE:
            return

        # تطبيق إعدادات المحاور
        if self.view_settings.axes_enabled:
            self.ax.set_xlabel('X (مم)', fontsize=10)
            self.ax.set_ylabel('Y (مم)', fontsize=10)
            self.ax.set_zlabel('Z (مم)', fontsize=10)
        else:
            self.ax.set_xlabel('')
            self.ax.set_ylabel('')
            self.ax.set_zlabel('')

        # تطبيق إعدادات الشبكة
        self.ax.grid(self.view_settings.grid_enabled, alpha=0.3)

        # إعداد نسبة العرض المتساوية
        if self.current_model and self.current_model.bounds is not None:
            bounds = self.current_model.bounds
            max_range = np.max(bounds[1] - bounds[0]) / 2.0
            center = np.mean(bounds, axis=0)

            self.ax.set_xlim(center[0] - max_range, center[0] + max_range)
            self.ax.set_ylim(center[1] - max_range, center[1] + max_range)
            self.ax.set_zlim(center[2] - max_range, center[2] + max_range)

        # إعداد العنوان
        title = f"معاين ثلاثي الأبعاد - {self.material_settings.material_type}"
        self.ax.set_title(title, fontsize=12, fontweight='bold', pad=20)

    def plot_mesh(self, mesh):
        """رسم شبكة ثلاثية الأبعاد"""
        if not hasattr(mesh, 'vertices') or not hasattr(mesh, 'faces'):
            return

        vertices = mesh.vertices
        faces = mesh.faces

        # رسم الوجوه
        for face in faces:
            triangle = vertices[face]
            self.ax.plot_trisurf(
                triangle[:, 0], triangle[:, 1], triangle[:, 2],
                alpha=0.7, shade=True
            )

    def reset_view(self):
        """إعادة تعيين العرض"""
        if MATPLOTLIB_AVAILABLE:
            self.setup_3d_axis()
            if self.current_model:
                self.display_model()

    def save_screenshot(self):
        """حفظ لقطة شاشة"""
        if not MATPLOTLIB_AVAILABLE or not self.current_model:
            QMessageBox.warning(self, "تحذير", "لا يوجد نموذج لحفظه")
            return

        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getSaveFileName(
            self,
            "حفظ صورة النموذج",
            f"{self.current_model.file_name}_screenshot.png",
            "صور PNG (*.png);;صور JPEG (*.jpg);;جميع الملفات (*)"
        )

        if file_path:
            self.figure.savefig(file_path, dpi=300, bbox_inches='tight')
            QMessageBox.information(self, "نجح الحفظ", f"تم حفظ الصورة في:\n{file_path}")

    def analyze_dimensions(self):
        """تحليل أبعاد النموذج"""
        if not self.current_model:
            QMessageBox.warning(self, "تحذير", "لا يوجد نموذج للتحليل")
            return

        # عرض نافذة تحليل الأبعاد
        QMessageBox.information(self, "تحليل الأبعاد", "سيتم تنفيذ تحليل الأبعاد قريباً")

    def export_model_data(self):
        """تصدير بيانات النموذج"""
        if not self.current_model:
            QMessageBox.warning(self, "تحذير", "لا يوجد نموذج للتصدير")
            return

        # عرض نافذة تصدير البيانات
        QMessageBox.information(self, "تصدير البيانات", "سيتم تنفيذ تصدير البيانات قريباً")
