#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام عروض الأسعار المكتمل لتطبيق تصميم الأثاث
Complete Quotation Management System for Furniture Design Application
"""

def get_safe_icon(icon_name):
    """الحصول على أيقونة آمنة"""
    try:
        from ui.icons_manager import IconsManager
        return IconsManager.get_standard_icon(icon_name)
    except Exception:
        # إرجاع أيقونة فارغة في حالة الخطأ
        from PySide6.QtGui import QIcon
        return QIcon()


import os
import json
import sqlite3
from datetime import datetime, date, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QLineEdit, QTextEdit, QComboBox, QDateEdit, QSpinBox,
    Q<PERSON>able<PERSON><PERSON>t, QTableWidgetItem, QHeaderView, QMessageBox,
    QDialog, QFormLayout, QDialogButtonBox, QGroupBox,
    QCheckBox, QDoubleSpinBox, QTabWidget, QSplitter,
    QTreeWidget, QTreeWidgetItem, QProgressBar, QFrame
)
from PySide6.QtCore import Qt, QDate, QThread, Signal, QTimer
from PySide6.QtGui import QFont, QColor, QPixmap, QPainter
from PySide6.QtPrintSupport import QPrinter, QPrintDialog

from ui.modern_styles import ModernStyleManager
from ui.icons_manager import IconsManager


@dataclass
class QuotationItem:
    """عنصر في عرض السعر"""
    id: int
    quotation_id: int
    item_name: str
    description: str
    quantity: float
    unit: str
    unit_price: float
    discount_percent: float
    total_price: float
    notes: str


@dataclass
class Quotation:
    """عرض السعر"""
    id: int
    quotation_number: str
    client_id: int
    client_name: str
    title: str
    description: str
    quotation_date: str
    valid_until: str
    status: str  # draft, sent, accepted, rejected, expired
    subtotal: float
    discount_amount: float
    tax_amount: float
    total_amount: float
    terms_conditions: str
    notes: str
    created_by: str
    created_date: str
    last_modified: str

    def __post_init__(self):
        if not self.created_date:
            self.created_date = datetime.now().isoformat()
        if not self.last_modified:
            self.last_modified = datetime.now().isoformat()


class QuotationDialog(QDialog):
    """نافذة إنشاء/تعديل عرض السعر"""

    def __init__(self, quotation: Optional[Quotation] = None, parent=None):
        super().__init__(parent)
        self.quotation = quotation
        self.is_edit_mode = quotation is not None
        self.items = []
        self.init_ui()
        if self.quotation:
            self.load_quotation_data()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("تعديل عرض السعر" if self.is_edit_mode else "إنشاء عرض سعر جديد")
        self.setModal(True)
        self.resize(800, 700)

        layout = QVBoxLayout(self)

        # تبويبات البيانات
        tabs = QTabWidget()

        # تبويب البيانات الأساسية
        basic_tab = self.create_basic_info_tab()
        tabs.addTab(basic_tab, "البيانات الأساسية")

        # تبويب العناصر
        items_tab = self.create_items_tab()
        tabs.addTab(items_tab, "العناصر")

        # تبويب الشروط والأحكام
        terms_tab = self.create_terms_tab()
        tabs.addTab(terms_tab, "الشروط والأحكام")

        layout.addWidget(tabs)

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        self.save_btn = QPushButton("حفظ")
        self.save_btn.setIcon(get_safe_icon("save"))
        self.save_btn.clicked.connect(self.accept)

        self.preview_btn = QPushButton("معاينة")
        self.preview_btn.setIcon(get_safe_icon("preview"))
        self.preview_btn.clicked.connect(self.preview_quotation)

        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.setIcon(get_safe_icon("close"))
        self.cancel_btn.clicked.connect(self.reject)

        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.preview_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_btn)

        layout.addLayout(buttons_layout)

    def create_basic_info_tab(self):
        """إنشاء تبويب البيانات الأساسية"""
        tab = QWidget()
        layout = QFormLayout(tab)
        layout.setSpacing(ModernStyleManager.SPACING['md'])

        # رقم عرض السعر
        self.quotation_number_edit = QLineEdit()
        self.quotation_number_edit.setPlaceholderText("سيتم إنشاؤه تلقائياً")
        self.quotation_number_edit.setReadOnly(True)
        layout.addRow("رقم عرض السعر:", self.quotation_number_edit)

        # العميل
        self.client_combo = QComboBox()
        self.client_combo.setEditable(True)
        self.client_combo.setPlaceholderText("اختر العميل")
        layout.addRow("العميل *:", self.client_combo)

        # العنوان
        self.title_edit = QLineEdit()
        self.title_edit.setPlaceholderText("عنوان عرض السعر")
        layout.addRow("العنوان *:", self.title_edit)

        # الوصف
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        self.description_edit.setPlaceholderText("وصف مختصر لعرض السعر")
        layout.addRow("الوصف:", self.description_edit)

        # تاريخ عرض السعر
        self.quotation_date_edit = QDateEdit()
        self.quotation_date_edit.setDate(QDate.currentDate())
        self.quotation_date_edit.setCalendarPopup(True)
        layout.addRow("تاريخ عرض السعر:", self.quotation_date_edit)

        # صالح حتى
        self.valid_until_edit = QDateEdit()
        self.valid_until_edit.setDate(QDate.currentDate().addDays(30))
        self.valid_until_edit.setCalendarPopup(True)
        layout.addRow("صالح حتى:", self.valid_until_edit)

        # الحالة
        self.status_combo = QComboBox()
        self.status_combo.addItems(["مسودة", "مرسل", "مقبول", "مرفوض", "منتهي الصلاحية"])
        layout.addRow("الحالة:", self.status_combo)

        return tab

    def create_items_tab(self):
        """إنشاء تبويب العناصر"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # شريط أدوات العناصر
        items_toolbar = QHBoxLayout()

        add_item_btn = QPushButton("إضافة عنصر")
        add_item_btn.setIcon(get_safe_icon("add"))
        add_item_btn.clicked.connect(self.add_item)

        edit_item_btn = QPushButton("تعديل")
        edit_item_btn.setIcon(get_safe_icon("edit"))
        edit_item_btn.clicked.connect(self.edit_item)

        delete_item_btn = QPushButton("حذف")
        delete_item_btn.setIcon(get_safe_icon("delete"))
        delete_item_btn.clicked.connect(self.delete_item)

        items_toolbar.addWidget(add_item_btn)
        items_toolbar.addWidget(edit_item_btn)
        items_toolbar.addWidget(delete_item_btn)
        items_toolbar.addStretch()

        layout.addLayout(items_toolbar)

        # جدول العناصر
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(7)
        self.items_table.setHorizontalHeaderLabels([
            "العنصر", "الوصف", "الكمية", "الوحدة", "السعر", "الخصم %", "الإجمالي"
        ])

        # تخصيص الجدول
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)  # العنصر
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # الوصف
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # الكمية
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # الوحدة
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # السعر
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # الخصم
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # الإجمالي

        layout.addWidget(self.items_table)

        # ملخص الأسعار
        summary_frame = QFrame()
        summary_frame.setFrameStyle(QFrame.Shape.Box)
        summary_layout = QFormLayout(summary_frame)

        self.subtotal_label = QLabel("0.00 ريال")
        self.discount_spin = QDoubleSpinBox()
        self.discount_spin.setRange(0, 999999)
        self.discount_spin.setSuffix(" ريال")
        self.discount_spin.valueChanged.connect(self.calculate_totals)

        self.tax_spin = QDoubleSpinBox()
        self.tax_spin.setRange(0, 100)
        self.tax_spin.setSuffix(" %")
        self.tax_spin.setValue(15)  # ضريبة القيمة المضافة
        self.tax_spin.valueChanged.connect(self.calculate_totals)

        self.total_label = QLabel("0.00 ريال")
        self.total_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                font-weight: bold;
                color: {ModernStyleManager.COLORS['primary']};
            }}
        """)

        summary_layout.addRow("المجموع الفرعي:", self.subtotal_label)
        summary_layout.addRow("الخصم:", self.discount_spin)
        summary_layout.addRow("الضريبة:", self.tax_spin)
        summary_layout.addRow("الإجمالي:", self.total_label)

        layout.addWidget(summary_frame)

        return tab

    def create_terms_tab(self):
        """إنشاء تبويب الشروط والأحكام"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # الشروط والأحكام
        terms_label = QLabel("الشروط والأحكام:")
        terms_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                font-weight: bold;
                margin-bottom: {ModernStyleManager.SPACING['sm']}px;
            }}
        """)
        layout.addWidget(terms_label)

        self.terms_edit = QTextEdit()
        self.terms_edit.setPlaceholderText("أدخل الشروط والأحكام...")

        # شروط افتراضية
        default_terms = """1. الأسعار المذكورة شاملة ضريبة القيمة المضافة
2. مدة التسليم: 15-30 يوم عمل من تاريخ التأكيد
3. الدفع: 50% مقدم و 50% عند التسليم
4. التركيب مجاني داخل المدينة
5. ضمان سنة واحدة ضد عيوب الصناعة
6. العرض صالح لمدة 30 يوم من تاريخ الإصدار"""

        self.terms_edit.setPlainText(default_terms)
        layout.addWidget(self.terms_edit)

        # الملاحظات
        notes_label = QLabel("ملاحظات إضافية:")
        notes_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                font-weight: bold;
                margin: {ModernStyleManager.SPACING['md']}px 0 {ModernStyleManager.SPACING['sm']}px 0;
            }}
        """)
        layout.addWidget(notes_label)

        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        self.notes_edit.setPlaceholderText("ملاحظات إضافية...")
        layout.addWidget(self.notes_edit)

        return tab

    def add_item(self):
        """إضافة عنصر جديد"""
        # هنا يمكن إضافة نافذة لإدخال تفاصيل العنصر
        # للبساطة، سنضيف عنصر تجريبي
        row = self.items_table.rowCount()
        self.items_table.insertRow(row)

        self.items_table.setItem(row, 0, QTableWidgetItem("عنصر جديد"))
        self.items_table.setItem(row, 1, QTableWidgetItem("وصف العنصر"))
        self.items_table.setItem(row, 2, QTableWidgetItem("1"))
        self.items_table.setItem(row, 3, QTableWidgetItem("قطعة"))
        self.items_table.setItem(row, 4, QTableWidgetItem("1000"))
        self.items_table.setItem(row, 5, QTableWidgetItem("0"))
        self.items_table.setItem(row, 6, QTableWidgetItem("1000"))

        self.calculate_totals()

    def edit_item(self):
        """تعديل العنصر المحدد"""
        current_row = self.items_table.currentRow()
        if current_row >= 0:
            # هنا يمكن فتح نافذة تعديل العنصر
            pass

    def delete_item(self):
        """حذف العنصر المحدد"""
        current_row = self.items_table.currentRow()
        if current_row >= 0:
            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                "هل أنت متأكد من حذف هذا العنصر؟",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.items_table.removeRow(current_row)
                self.calculate_totals()

    def calculate_totals(self):
        """حساب الإجماليات"""
        subtotal = 0.0

        for row in range(self.items_table.rowCount()):
            total_item = self.items_table.item(row, 6)
            if total_item:
                try:
                    subtotal += float(total_item.text())
                except ValueError:
                    pass

        self.subtotal_label.setText(f"{subtotal:,.2f} ريال")

        discount = self.discount_spin.value()
        tax_rate = self.tax_spin.value() / 100

        after_discount = subtotal - discount
        tax_amount = after_discount * tax_rate
        total = after_discount + tax_amount

        self.total_label.setText(f"{total:,.2f} ريال")

    def preview_quotation(self):
        """معاينة عرض السعر"""
        QMessageBox.information(self, "معاينة", "سيتم تطوير معاينة عرض السعر قريباً")

    def load_quotation_data(self):
        """تحميل بيانات عرض السعر للتعديل"""
        if not self.quotation:
            return

        # تحميل البيانات الأساسية
        self.quotation_number_edit.setText(self.quotation.quotation_number)
        self.title_edit.setText(self.quotation.title)
        self.description_edit.setPlainText(self.quotation.description)
        self.terms_edit.setPlainText(self.quotation.terms_conditions)
        self.notes_edit.setPlainText(self.quotation.notes)

        # تحميل التواريخ
        try:
            quotation_date = QDate.fromString(self.quotation.quotation_date, "yyyy-MM-dd")
            self.quotation_date_edit.setDate(quotation_date)

            valid_until = QDate.fromString(self.quotation.valid_until, "yyyy-MM-dd")
            self.valid_until_edit.setDate(valid_until)
        except:
            pass

        # تحميل الحالة
        statuses = {"draft": 0, "sent": 1, "accepted": 2, "rejected": 3, "expired": 4}
        self.status_combo.setCurrentIndex(statuses.get(self.quotation.status, 0))

    def get_quotation_data(self) -> Quotation:
        """الحصول على بيانات عرض السعر من النموذج"""
        statuses = ["draft", "sent", "accepted", "rejected", "expired"]

        return Quotation(
            id=self.quotation.id if self.quotation else 0,
            quotation_number=self.quotation_number_edit.text() or self.generate_quotation_number(),
            client_id=0,  # سيتم تحديده من قاعدة البيانات
            client_name=self.client_combo.currentText(),
            title=self.title_edit.text().strip(),
            description=self.description_edit.toPlainText().strip(),
            quotation_date=self.quotation_date_edit.date().toString("yyyy-MM-dd"),
            valid_until=self.valid_until_edit.date().toString("yyyy-MM-dd"),
            status=statuses[self.status_combo.currentIndex()],
            subtotal=float(self.subtotal_label.text().replace(" ريال", "").replace(",", "")),
            discount_amount=self.discount_spin.value(),
            tax_amount=0.0,  # سيتم حسابه
            total_amount=float(self.total_label.text().replace(" ريال", "").replace(",", "")),
            terms_conditions=self.terms_edit.toPlainText().strip(),
            notes=self.notes_edit.toPlainText().strip(),
            created_by="المستخدم الحالي",
            created_date=self.quotation.created_date if self.quotation else datetime.now().isoformat(),
            last_modified=datetime.now().isoformat()
        )

    def generate_quotation_number(self) -> str:
        """إنشاء رقم عرض سعر تلقائي"""
        today = datetime.now()
        return f"Q-{today.year}{today.month:02d}{today.day:02d}-{today.hour:02d}{today.minute:02d}"

    def validate_data(self) -> bool:
        """التحقق من صحة البيانات"""
        if not self.title_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال عنوان عرض السعر")
            self.title_edit.setFocus()
            return False

        if not self.client_combo.currentText().strip():
            QMessageBox.warning(self, "خطأ", "يرجى اختيار العميل")
            self.client_combo.setFocus()
            return False

        if self.items_table.rowCount() == 0:
            QMessageBox.warning(self, "خطأ", "يرجى إضافة عنصر واحد على الأقل")
            return False

        return True

    def accept(self):
        """قبول النموذج"""
        if self.validate_data():
            super().accept()


class QuotationDatabase:
    """قاعدة بيانات عروض الأسعار"""

    def __init__(self, db_path: str = "quotations.db"):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """تهيئة قاعدة البيانات"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # جدول عروض الأسعار
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS quotations (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    quotation_number TEXT UNIQUE NOT NULL,
                    client_id INTEGER,
                    client_name TEXT,
                    title TEXT,
                    description TEXT,
                    quotation_date TEXT,
                    valid_until TEXT,
                    status TEXT,
                    subtotal REAL,
                    discount_amount REAL,
                    tax_amount REAL,
                    total_amount REAL,
                    terms_conditions TEXT,
                    notes TEXT,
                    created_by TEXT,
                    created_date TEXT,
                    last_modified TEXT
                )
            """)

            # جدول عناصر عروض الأسعار
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS quotation_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    quotation_id INTEGER,
                    item_name TEXT,
                    description TEXT,
                    quantity REAL,
                    unit TEXT,
                    unit_price REAL,
                    discount_percent REAL,
                    total_price REAL,
                    notes TEXT,
                    FOREIGN KEY (quotation_id) REFERENCES quotations (id)
                )
            """)

            conn.commit()

    def add_quotation(self, quotation: Quotation) -> int:
        """إضافة عرض سعر جديد"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO quotations (
                    quotation_number, client_id, client_name, title, description,
                    quotation_date, valid_until, status, subtotal, discount_amount,
                    tax_amount, total_amount, terms_conditions, notes,
                    created_by, created_date, last_modified
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                quotation.quotation_number, quotation.client_id, quotation.client_name,
                quotation.title, quotation.description, quotation.quotation_date,
                quotation.valid_until, quotation.status, quotation.subtotal,
                quotation.discount_amount, quotation.tax_amount, quotation.total_amount,
                quotation.terms_conditions, quotation.notes, quotation.created_by,
                quotation.created_date, quotation.last_modified
            ))
            return cursor.lastrowid

    def update_quotation(self, quotation: Quotation):
        """تحديث عرض السعر"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE quotations SET
                    quotation_number=?, client_id=?, client_name=?, title=?, description=?,
                    quotation_date=?, valid_until=?, status=?, subtotal=?, discount_amount=?,
                    tax_amount=?, total_amount=?, terms_conditions=?, notes=?, last_modified=?
                WHERE id=?
            """, (
                quotation.quotation_number, quotation.client_id, quotation.client_name,
                quotation.title, quotation.description, quotation.quotation_date,
                quotation.valid_until, quotation.status, quotation.subtotal,
                quotation.discount_amount, quotation.tax_amount, quotation.total_amount,
                quotation.terms_conditions, quotation.notes, quotation.last_modified,
                quotation.id
            ))

    def delete_quotation(self, quotation_id: int):
        """حذف عرض السعر"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM quotations WHERE id=?", (quotation_id,))
            cursor.execute("DELETE FROM quotation_items WHERE quotation_id=?", (quotation_id,))

    def get_all_quotations(self) -> List[Quotation]:
        """الحصول على جميع عروض الأسعار"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM quotations ORDER BY created_date DESC")
            rows = cursor.fetchall()

            quotations = []
            for row in rows:
                quotation = Quotation(
                    id=row[0], quotation_number=row[1], client_id=row[2],
                    client_name=row[3], title=row[4], description=row[5],
                    quotation_date=row[6], valid_until=row[7], status=row[8],
                    subtotal=row[9], discount_amount=row[10], tax_amount=row[11],
                    total_amount=row[12], terms_conditions=row[13], notes=row[14],
                    created_by=row[15], created_date=row[16], last_modified=row[17]
                )
                quotations.append(quotation)

            return quotations


class QuotationManagerWidget(QWidget):
    """واجهة إدارة عروض الأسعار الرئيسية"""

    def __init__(self):
        super().__init__()
        self.db = QuotationDatabase()
        self.quotations = []
        self.selected_quotation = None
        self.init_ui()
        self.load_quotations()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setSpacing(ModernStyleManager.SPACING['md'])
        layout.setContentsMargins(
            ModernStyleManager.SPACING['lg'],
            ModernStyleManager.SPACING['lg'],
            ModernStyleManager.SPACING['lg'],
            ModernStyleManager.SPACING['lg']
        )

        # شريط الأدوات العلوي
        toolbar_layout = QHBoxLayout()

        # عنوان الصفحة
        title_label = QLabel("💰 إدارة عروض الأسعار")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['title']}px;
                font-weight: bold;
                color: {ModernStyleManager.COLORS['primary']};
                margin-bottom: {ModernStyleManager.SPACING['md']}px;
            }}
        """)

        # شريط البحث
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔍 البحث في عروض الأسعار...")
        self.search_edit.textChanged.connect(self.search_quotations)
        self.search_edit.setProperty("inputSize", "medium")

        # فلتر الحالة
        self.status_filter = QComboBox()
        self.status_filter.addItems(["جميع الحالات", "مسودة", "مرسل", "مقبول", "مرفوض", "منتهي الصلاحية"])
        self.status_filter.currentTextChanged.connect(self.filter_by_status)

        # أزرار التحكم
        self.new_quotation_btn = QPushButton("عرض سعر جديد")
        self.new_quotation_btn.setIcon(get_safe_icon("add"))
        self.new_quotation_btn.clicked.connect(self.new_quotation)
        self.new_quotation_btn.setProperty("buttonSize", "medium")

        self.edit_quotation_btn = QPushButton("تعديل")
        self.edit_quotation_btn.setIcon(get_safe_icon("edit"))
        self.edit_quotation_btn.clicked.connect(self.edit_quotation)
        self.edit_quotation_btn.setEnabled(False)
        self.edit_quotation_btn.setProperty("buttonSize", "medium")

        self.duplicate_btn = QPushButton("نسخ")
        self.duplicate_btn.setIcon(get_safe_icon("copy"))
        self.duplicate_btn.clicked.connect(self.duplicate_quotation)
        self.duplicate_btn.setEnabled(False)
        self.duplicate_btn.setProperty("buttonSize", "medium")

        self.print_btn = QPushButton("طباعة")
        self.print_btn.setIcon(get_safe_icon("print"))
        self.print_btn.clicked.connect(self.print_quotation)
        self.print_btn.setEnabled(False)
        self.print_btn.setProperty("buttonSize", "medium")

        self.delete_btn = QPushButton("حذف")
        self.delete_btn.setIcon(get_safe_icon("delete"))
        self.delete_btn.clicked.connect(self.delete_quotation)
        self.delete_btn.setEnabled(False)
        self.delete_btn.setProperty("buttonSize", "medium")

        toolbar_layout.addWidget(title_label)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.search_edit)
        toolbar_layout.addWidget(self.status_filter)
        toolbar_layout.addSpacing(ModernStyleManager.SPACING['md'])
        toolbar_layout.addWidget(self.new_quotation_btn)
        toolbar_layout.addWidget(self.edit_quotation_btn)
        toolbar_layout.addWidget(self.duplicate_btn)
        toolbar_layout.addWidget(self.print_btn)
        toolbar_layout.addWidget(self.delete_btn)

        layout.addLayout(toolbar_layout)

        # جدول عروض الأسعار
        self.create_quotations_table()
        layout.addWidget(self.quotations_table)

        # شريط الحالة
        status_layout = QHBoxLayout()

        self.status_label = QLabel("جاهز")
        self.quotations_count_label = QLabel("عروض الأسعار: 0")
        self.total_value_label = QLabel("القيمة الإجمالية: 0 ريال")

        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.quotations_count_label)
        status_layout.addWidget(self.total_value_label)

        layout.addLayout(status_layout)

    def create_quotations_table(self):
        """إنشاء جدول عروض الأسعار"""
        self.quotations_table = QTableWidget()
        self.quotations_table.setColumnCount(8)
        self.quotations_table.setHorizontalHeaderLabels([
            "رقم العرض", "العميل", "العنوان", "التاريخ",
            "صالح حتى", "الحالة", "المبلغ", "آخر تعديل"
        ])

        # تخصيص الجدول
        header = self.quotations_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # رقم العرض
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # العميل
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)  # العنوان
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # التاريخ
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # صالح حتى
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # الحالة
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # المبلغ
        header.setSectionResizeMode(7, QHeaderView.ResizeMode.ResizeToContents)  # آخر تعديل

        # إعدادات الجدول
        self.quotations_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.quotations_table.setAlternatingRowColors(True)
        self.quotations_table.setSortingEnabled(True)

        # ربط الأحداث
        self.quotations_table.selectionModel().selectionChanged.connect(self.on_quotation_selected)
        self.quotations_table.doubleClicked.connect(self.edit_quotation)

    def load_quotations(self):
        """تحميل عروض الأسعار"""
        try:
            self.status_label.setText("جاري تحميل عروض الأسعار...")
            self.quotations = self.db.get_all_quotations()
            self.populate_quotations_table()
            self.quotations_count_label.setText(f"عروض الأسعار: {len(self.quotations)}")

            # حساب القيمة الإجمالية
            total_value = sum(q.total_amount for q in self.quotations)
            self.total_value_label.setText(f"القيمة الإجمالية: {total_value:,.2f} ريال")

            self.status_label.setText("تم تحميل عروض الأسعار بنجاح")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل عروض الأسعار: {str(e)}")
            self.status_label.setText("فشل في تحميل عروض الأسعار")

    def populate_quotations_table(self):
        """ملء جدول عروض الأسعار"""
        self.quotations_table.setRowCount(len(self.quotations))

        for row, quotation in enumerate(self.quotations):
            # رقم العرض
            number_item = QTableWidgetItem(quotation.quotation_number)
            self.quotations_table.setItem(row, 0, number_item)

            # العميل
            client_item = QTableWidgetItem(quotation.client_name)
            self.quotations_table.setItem(row, 1, client_item)

            # العنوان
            title_item = QTableWidgetItem(quotation.title)
            self.quotations_table.setItem(row, 2, title_item)

            # التاريخ
            date_item = QTableWidgetItem(quotation.quotation_date)
            self.quotations_table.setItem(row, 3, date_item)

            # صالح حتى
            valid_item = QTableWidgetItem(quotation.valid_until)
            self.quotations_table.setItem(row, 4, valid_item)

            # الحالة
            status_names = {
                "draft": "مسودة", "sent": "مرسل", "accepted": "مقبول",
                "rejected": "مرفوض", "expired": "منتهي الصلاحية"
            }
            status_item = QTableWidgetItem(status_names.get(quotation.status, quotation.status))

            # تلوين الحالة
            if quotation.status == "accepted":
                status_item.setBackground(QColor("#27AE60"))
                status_item.setForeground(QColor("white"))
            elif quotation.status == "rejected":
                status_item.setBackground(QColor("#E74C3C"))
                status_item.setForeground(QColor("white"))
            elif quotation.status == "expired":
                status_item.setBackground(QColor("#95A5A6"))
                status_item.setForeground(QColor("white"))
            elif quotation.status == "sent":
                status_item.setBackground(QColor("#3498DB"))
                status_item.setForeground(QColor("white"))
            else:  # draft
                status_item.setBackground(QColor("#F39C12"))
                status_item.setForeground(QColor("white"))

            self.quotations_table.setItem(row, 5, status_item)

            # المبلغ
            amount_item = QTableWidgetItem(f"{quotation.total_amount:,.2f}")
            self.quotations_table.setItem(row, 6, amount_item)

            # آخر تعديل
            try:
                last_modified = datetime.fromisoformat(quotation.last_modified).strftime("%Y-%m-%d")
            except:
                last_modified = quotation.last_modified
            modified_item = QTableWidgetItem(last_modified)
            self.quotations_table.setItem(row, 7, modified_item)

    def on_quotation_selected(self):
        """عند تحديد عرض سعر"""
        selected_rows = self.quotations_table.selectionModel().selectedRows()

        if selected_rows:
            row = selected_rows[0].row()
            if 0 <= row < len(self.quotations):
                self.selected_quotation = self.quotations[row]

                # تفعيل أزرار التحكم
                self.edit_quotation_btn.setEnabled(True)
                self.duplicate_btn.setEnabled(True)
                self.print_btn.setEnabled(True)
                self.delete_btn.setEnabled(True)
        else:
            self.selected_quotation = None

            # تعطيل أزرار التحكم
            self.edit_quotation_btn.setEnabled(False)
            self.duplicate_btn.setEnabled(False)
            self.print_btn.setEnabled(False)
            self.delete_btn.setEnabled(False)

    def new_quotation(self):
        """إنشاء عرض سعر جديد"""
        dialog = QuotationDialog(parent=self)

        if dialog.exec() == QDialog.DialogCode.Accepted:
            try:
                quotation_data = dialog.get_quotation_data()
                quotation_id = self.db.add_quotation(quotation_data)

                QMessageBox.information(self, "نجح", f"تم إنشاء عرض السعر بنجاح\nرقم العرض: {quotation_data.quotation_number}")
                self.load_quotations()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في إنشاء عرض السعر: {str(e)}")

    def edit_quotation(self):
        """تعديل عرض السعر المحدد"""
        if not self.selected_quotation:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عرض سعر للتعديل")
            return

        dialog = QuotationDialog(quotation=self.selected_quotation, parent=self)

        if dialog.exec() == QDialog.DialogCode.Accepted:
            try:
                quotation_data = dialog.get_quotation_data()
                self.db.update_quotation(quotation_data)

                QMessageBox.information(self, "نجح", "تم تحديث عرض السعر بنجاح")
                self.load_quotations()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في تحديث عرض السعر: {str(e)}")

    def duplicate_quotation(self):
        """نسخ عرض السعر المحدد"""
        if not self.selected_quotation:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عرض سعر للنسخ")
            return

        # إنشاء نسخة من عرض السعر
        new_quotation = Quotation(
            id=0,
            quotation_number="",  # سيتم إنشاؤه تلقائياً
            client_id=self.selected_quotation.client_id,
            client_name=self.selected_quotation.client_name,
            title=f"نسخة من {self.selected_quotation.title}",
            description=self.selected_quotation.description,
            quotation_date=datetime.now().strftime("%Y-%m-%d"),
            valid_until=(datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d"),
            status="draft",
            subtotal=self.selected_quotation.subtotal,
            discount_amount=self.selected_quotation.discount_amount,
            tax_amount=self.selected_quotation.tax_amount,
            total_amount=self.selected_quotation.total_amount,
            terms_conditions=self.selected_quotation.terms_conditions,
            notes=self.selected_quotation.notes,
            created_by="المستخدم الحالي",
            created_date=datetime.now().isoformat(),
            last_modified=datetime.now().isoformat()
        )

        dialog = QuotationDialog(quotation=new_quotation, parent=self)

        if dialog.exec() == QDialog.DialogCode.Accepted:
            try:
                quotation_data = dialog.get_quotation_data()
                quotation_id = self.db.add_quotation(quotation_data)

                QMessageBox.information(self, "نجح", f"تم نسخ عرض السعر بنجاح\nرقم العرض الجديد: {quotation_data.quotation_number}")
                self.load_quotations()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في نسخ عرض السعر: {str(e)}")

    def print_quotation(self):
        """طباعة عرض السعر المحدد"""
        if not self.selected_quotation:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عرض سعر للطباعة")
            return

        QMessageBox.information(self, "طباعة", "سيتم تطوير نظام الطباعة قريباً")

    def delete_quotation(self):
        """حذف عرض السعر المحدد"""
        if not self.selected_quotation:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد عرض سعر للحذف")
            return

        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            f"هل أنت متأكد من حذف عرض السعر '{self.selected_quotation.quotation_number}'؟\n"
            "سيتم حذف جميع البيانات المرتبطة بهذا العرض.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                self.db.delete_quotation(self.selected_quotation.id)
                QMessageBox.information(self, "نجح", "تم حذف عرض السعر بنجاح")
                self.load_quotations()

            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في حذف عرض السعر: {str(e)}")

    def search_quotations(self):
        """البحث في عروض الأسعار"""
        search_term = self.search_edit.text().strip()

        if not search_term:
            self.load_quotations()
            return

        # فلترة عروض الأسعار
        filtered_quotations = []
        for quotation in self.quotations:
            if (search_term.lower() in quotation.quotation_number.lower() or
                search_term.lower() in quotation.client_name.lower() or
                search_term.lower() in quotation.title.lower()):
                filtered_quotations.append(quotation)

        # تحديث الجدول
        self.quotations = filtered_quotations
        self.populate_quotations_table()
        self.quotations_count_label.setText(f"نتائج البحث: {len(self.quotations)}")

    def filter_by_status(self):
        """فلترة حسب الحالة"""
        status_filter = self.status_filter.currentText()

        if status_filter == "جميع الحالات":
            self.load_quotations()
            return

        # تحويل النص العربي إلى الحالة الإنجليزية
        status_map = {
            "مسودة": "draft",
            "مرسل": "sent",
            "مقبول": "accepted",
            "مرفوض": "rejected",
            "منتهي الصلاحية": "expired"
        }

        target_status = status_map.get(status_filter)
        if not target_status:
            return

        # فلترة عروض الأسعار
        filtered_quotations = [q for q in self.quotations if q.status == target_status]

        # تحديث الجدول
        self.quotations = filtered_quotations
        self.populate_quotations_table()
        self.quotations_count_label.setText(f"عروض الأسعار ({status_filter}): {len(self.quotations)}")
