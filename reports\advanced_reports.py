#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التقارير المتقدم لتطبيق تصميم الأثاث
Advanced Reports System for Furniture Design Application
"""

import os
import json
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QDateEdit, QSpinBox, QTextEdit, QGroupBox,
    QTableWidget, QTableWidgetItem, QProgressBar, QMessageBox,
    QTabWidget, QCheckBox, QLineEdit, QFileDialog, QSplitter
)
from PyQt6.QtCore import Qt, QDate, QThread, pyqtSignal
from PyQt6.QtGui import QFont, QColor, QPixmap, QPainter
from PyQt6.QtPrintSupport import QPrinter, QPrintDialog

try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import seaborn as sns
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

try:
    from reportlab.lib.pagesizes import A4, letter
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    from reportlab.pdfgen import canvas
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

from ui.modern_styles import ModernStyleManager
from ui.icons_manager import IconsManager


class ReportType(Enum):
    """أنواع التقارير"""
    FINANCIAL = "financial"
    INVENTORY = "inventory"
    CLIENTS = "clients"
    PROJECTS = "projects"
    PRODUCTION = "production"
    SALES = "sales"
    CUSTOM = "custom"


class ReportFormat(Enum):
    """صيغ التقارير"""
    PDF = "pdf"
    EXCEL = "excel"
    CSV = "csv"
    HTML = "html"
    PRINT = "print"


@dataclass
class ReportConfig:
    """إعدادات التقرير"""
    report_type: ReportType
    title: str
    date_from: str
    date_to: str
    filters: Dict[str, Any]
    format: ReportFormat
    include_charts: bool = True
    include_summary: bool = True
    group_by: Optional[str] = None
    sort_by: Optional[str] = None
    created_date: str = ""
    
    def __post_init__(self):
        if not self.created_date:
            self.created_date = datetime.now().isoformat()


class ReportGenerator(QThread):
    """مولد التقارير"""
    
    progress_updated = pyqtSignal(int, str)
    report_generated = pyqtSignal(str, dict)  # file_path, report_data
    generation_failed = pyqtSignal(str)
    
    def __init__(self, config: ReportConfig, data_sources: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.data_sources = data_sources
        
    def run(self):
        """تشغيل توليد التقرير"""
        try:
            self.progress_updated.emit(10, "بدء توليد التقرير...")
            
            # جمع البيانات
            self.progress_updated.emit(30, "جمع البيانات...")
            report_data = self.collect_data()
            
            # معالجة البيانات
            self.progress_updated.emit(50, "معالجة البيانات...")
            processed_data = self.process_data(report_data)
            
            # إنشاء التقرير
            self.progress_updated.emit(70, "إنشاء التقرير...")
            file_path = self.generate_report(processed_data)
            
            self.progress_updated.emit(100, "تم إنشاء التقرير بنجاح!")
            self.report_generated.emit(file_path, processed_data)
            
        except Exception as e:
            self.generation_failed.emit(str(e))
            
    def collect_data(self) -> Dict[str, Any]:
        """جمع البيانات للتقرير"""
        data = {}
        
        if self.config.report_type == ReportType.FINANCIAL:
            data = self.collect_financial_data()
        elif self.config.report_type == ReportType.INVENTORY:
            data = self.collect_inventory_data()
        elif self.config.report_type == ReportType.CLIENTS:
            data = self.collect_clients_data()
        elif self.config.report_type == ReportType.PROJECTS:
            data = self.collect_projects_data()
        elif self.config.report_type == ReportType.PRODUCTION:
            data = self.collect_production_data()
        elif self.config.report_type == ReportType.SALES:
            data = self.collect_sales_data()
        
        return data
        
    def collect_financial_data(self) -> Dict[str, Any]:
        """جمع البيانات المالية"""
        # محاكاة بيانات مالية
        return {
            'revenue': [
                {'date': '2024-01-01', 'amount': 15000, 'source': 'مبيعات'},
                {'date': '2024-01-02', 'amount': 8500, 'source': 'خدمات'},
                {'date': '2024-01-03', 'amount': 12000, 'source': 'مبيعات'},
            ],
            'expenses': [
                {'date': '2024-01-01', 'amount': 5000, 'category': 'مواد خام'},
                {'date': '2024-01-02', 'amount': 2000, 'category': 'رواتب'},
                {'date': '2024-01-03', 'amount': 1500, 'category': 'تشغيل'},
            ],
            'profit_margin': 0.35,
            'total_revenue': 35500,
            'total_expenses': 8500,
            'net_profit': 27000
        }
        
    def collect_inventory_data(self) -> Dict[str, Any]:
        """جمع بيانات المخزون"""
        return {
            'items': [
                {'name': 'خشب بلوط', 'quantity': 150, 'unit': 'متر مربع', 'value': 45000},
                {'name': 'مسامير', 'quantity': 5000, 'unit': 'قطعة', 'value': 2500},
                {'name': 'غراء خشب', 'quantity': 25, 'unit': 'لتر', 'value': 1250},
            ],
            'low_stock_items': ['غراء خشب'],
            'total_value': 48750,
            'categories': ['خشب', 'معدات', 'مواد كيميائية']
        }
        
    def collect_clients_data(self) -> Dict[str, Any]:
        """جمع بيانات العملاء"""
        return {
            'clients': [
                {'name': 'شركة الأثاث الحديث', 'orders': 15, 'revenue': 125000},
                {'name': 'معرض الفخامة', 'orders': 8, 'revenue': 85000},
                {'name': 'مصنع الأثاث الذكي', 'orders': 12, 'revenue': 95000},
            ],
            'total_clients': 45,
            'active_clients': 38,
            'new_clients_this_month': 5
        }
        
    def collect_projects_data(self) -> Dict[str, Any]:
        """جمع بيانات المشاريع"""
        return {
            'projects': [
                {'name': 'غرفة نوم كلاسيكية', 'status': 'مكتمل', 'value': 25000},
                {'name': 'مكتب تنفيذي', 'status': 'قيد التنفيذ', 'value': 15000},
                {'name': 'صالون حديث', 'status': 'تحت التصميم', 'value': 35000},
            ],
            'completed_projects': 25,
            'ongoing_projects': 8,
            'total_value': 750000
        }
        
    def collect_production_data(self) -> Dict[str, Any]:
        """جمع بيانات الإنتاج"""
        return {
            'production_lines': [
                {'name': 'خط الأثاث المكتبي', 'efficiency': 0.85, 'output': 120},
                {'name': 'خط غرف النوم', 'efficiency': 0.92, 'output': 85},
                {'name': 'خط الصالونات', 'efficiency': 0.78, 'output': 65},
            ],
            'total_output': 270,
            'average_efficiency': 0.85,
            'downtime_hours': 15
        }
        
    def collect_sales_data(self) -> Dict[str, Any]:
        """جمع بيانات المبيعات"""
        return {
            'sales': [
                {'product': 'طاولة طعام', 'quantity': 15, 'revenue': 75000},
                {'product': 'كرسي مكتب', 'quantity': 45, 'revenue': 67500},
                {'product': 'خزانة ملابس', 'quantity': 8, 'revenue': 96000},
            ],
            'total_sales': 238500,
            'best_selling_product': 'كرسي مكتب',
            'sales_growth': 0.15
        }
        
    def process_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """معالجة البيانات"""
        processed = raw_data.copy()
        
        # إضافة إحصائيات
        if self.config.report_type == ReportType.FINANCIAL:
            processed['profit_percentage'] = (processed['net_profit'] / processed['total_revenue']) * 100
            
        # تطبيق الفلاتر
        if self.config.filters:
            processed = self.apply_filters(processed)
            
        return processed
        
    def apply_filters(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """تطبيق الفلاتر"""
        # تطبيق فلاتر التاريخ والمعايير الأخرى
        return data
        
    def generate_report(self, data: Dict[str, Any]) -> str:
        """إنشاء التقرير"""
        if self.config.format == ReportFormat.PDF:
            return self.generate_pdf_report(data)
        elif self.config.format == ReportFormat.EXCEL:
            return self.generate_excel_report(data)
        elif self.config.format == ReportFormat.CSV:
            return self.generate_csv_report(data)
        elif self.config.format == ReportFormat.HTML:
            return self.generate_html_report(data)
        else:
            raise Exception(f"صيغة غير مدعومة: {self.config.format}")
            
    def generate_pdf_report(self, data: Dict[str, Any]) -> str:
        """إنشاء تقرير PDF"""
        if not REPORTLAB_AVAILABLE:
            raise Exception("مكتبة ReportLab غير متوفرة")
            
        filename = f"report_{self.config.report_type.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        filepath = os.path.join("exports", filename)
        
        # التأكد من وجود مجلد التصدير
        os.makedirs("exports", exist_ok=True)
        
        doc = SimpleDocTemplate(filepath, pagesize=A4)
        story = []
        styles = getSampleStyleSheet()
        
        # العنوان
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1  # توسيط
        )
        story.append(Paragraph(self.config.title, title_style))
        story.append(Spacer(1, 12))
        
        # معلومات التقرير
        info_data = [
            ['نوع التقرير:', self.config.report_type.value],
            ['تاريخ الإنشاء:', datetime.now().strftime('%Y-%m-%d %H:%M:%S')],
            ['الفترة:', f"{self.config.date_from} إلى {self.config.date_to}"]
        ]
        
        info_table = Table(info_data, colWidths=[2*inch, 3*inch])
        info_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
        ]))
        
        story.append(info_table)
        story.append(Spacer(1, 20))
        
        # بيانات التقرير
        if self.config.report_type == ReportType.FINANCIAL:
            self.add_financial_content_to_pdf(story, data, styles)
        elif self.config.report_type == ReportType.INVENTORY:
            self.add_inventory_content_to_pdf(story, data, styles)
        # إضافة أنواع أخرى من التقارير...
        
        doc.build(story)
        return filepath
        
    def add_financial_content_to_pdf(self, story, data, styles):
        """إضافة محتوى التقرير المالي للـ PDF"""
        # ملخص مالي
        story.append(Paragraph("الملخص المالي", styles['Heading2']))
        
        summary_data = [
            ['إجمالي الإيرادات', f"{data['total_revenue']:,} ريال"],
            ['إجمالي المصروفات', f"{data['total_expenses']:,} ريال"],
            ['صافي الربح', f"{data['net_profit']:,} ريال"],
            ['هامش الربح', f"{data.get('profit_percentage', 0):.1f}%"]
        ]
        
        summary_table = Table(summary_data, colWidths=[3*inch, 2*inch])
        summary_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.lightblue),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
        ]))
        
        story.append(summary_table)
        story.append(Spacer(1, 20))
        
    def add_inventory_content_to_pdf(self, story, data, styles):
        """إضافة محتوى تقرير المخزون للـ PDF"""
        story.append(Paragraph("تقرير المخزون", styles['Heading2']))
        
        # جدول المخزون
        inventory_data = [['الصنف', 'الكمية', 'الوحدة', 'القيمة']]
        for item in data['items']:
            inventory_data.append([
                item['name'],
                str(item['quantity']),
                item['unit'],
                f"{item['value']:,} ريال"
            ])
            
        inventory_table = Table(inventory_data, colWidths=[2*inch, 1*inch, 1*inch, 1.5*inch])
        inventory_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(inventory_table)
        
    def generate_excel_report(self, data: Dict[str, Any]) -> str:
        """إنشاء تقرير Excel"""
        filename = f"report_{self.config.report_type.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        filepath = os.path.join("exports", filename)
        
        # التأكد من وجود مجلد التصدير
        os.makedirs("exports", exist_ok=True)
        
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            if self.config.report_type == ReportType.FINANCIAL:
                # ورقة الإيرادات
                revenue_df = pd.DataFrame(data['revenue'])
                revenue_df.to_excel(writer, sheet_name='الإيرادات', index=False)
                
                # ورقة المصروفات
                expenses_df = pd.DataFrame(data['expenses'])
                expenses_df.to_excel(writer, sheet_name='المصروفات', index=False)
                
            elif self.config.report_type == ReportType.INVENTORY:
                # ورقة المخزون
                inventory_df = pd.DataFrame(data['items'])
                inventory_df.to_excel(writer, sheet_name='المخزون', index=False)
                
        return filepath
        
    def generate_csv_report(self, data: Dict[str, Any]) -> str:
        """إنشاء تقرير CSV"""
        filename = f"report_{self.config.report_type.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        filepath = os.path.join("exports", filename)
        
        # التأكد من وجود مجلد التصدير
        os.makedirs("exports", exist_ok=True)
        
        # تحويل البيانات إلى DataFrame وحفظها
        if self.config.report_type == ReportType.FINANCIAL:
            df = pd.DataFrame(data['revenue'])
        elif self.config.report_type == ReportType.INVENTORY:
            df = pd.DataFrame(data['items'])
        else:
            df = pd.DataFrame(data)
            
        df.to_csv(filepath, index=False, encoding='utf-8-sig')
        return filepath
        
    def generate_html_report(self, data: Dict[str, Any]) -> str:
        """إنشاء تقرير HTML"""
        filename = f"report_{self.config.report_type.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        filepath = os.path.join("exports", filename)
        
        # التأكد من وجود مجلد التصدير
        os.makedirs("exports", exist_ok=True)
        
        html_content = f"""
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>{self.config.title}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                h1 {{ color: #2C3E50; text-align: center; }}
                table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 12px; text-align: right; }}
                th {{ background-color: #3498DB; color: white; }}
                .summary {{ background-color: #ECF0F1; padding: 15px; border-radius: 5px; }}
            </style>
        </head>
        <body>
            <h1>{self.config.title}</h1>
            <div class="summary">
                <h2>معلومات التقرير</h2>
                <p><strong>نوع التقرير:</strong> {self.config.report_type.value}</p>
                <p><strong>تاريخ الإنشاء:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                <p><strong>الفترة:</strong> {self.config.date_from} إلى {self.config.date_to}</p>
            </div>
        """
        
        # إضافة محتوى حسب نوع التقرير
        if self.config.report_type == ReportType.FINANCIAL:
            html_content += self.generate_financial_html_content(data)
        elif self.config.report_type == ReportType.INVENTORY:
            html_content += self.generate_inventory_html_content(data)
            
        html_content += """
        </body>
        </html>
        """
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(html_content)
            
        return filepath
        
    def generate_financial_html_content(self, data: Dict[str, Any]) -> str:
        """إنشاء محتوى HTML للتقرير المالي"""
        content = """
        <h2>الملخص المالي</h2>
        <table>
            <tr><th>البند</th><th>المبلغ</th></tr>
        """
        
        content += f"""
            <tr><td>إجمالي الإيرادات</td><td>{data['total_revenue']:,} ريال</td></tr>
            <tr><td>إجمالي المصروفات</td><td>{data['total_expenses']:,} ريال</td></tr>
            <tr><td>صافي الربح</td><td>{data['net_profit']:,} ريال</td></tr>
        </table>
        
        <h2>تفاصيل الإيرادات</h2>
        <table>
            <tr><th>التاريخ</th><th>المبلغ</th><th>المصدر</th></tr>
        """
        
        for revenue in data['revenue']:
            content += f"""
            <tr>
                <td>{revenue['date']}</td>
                <td>{revenue['amount']:,} ريال</td>
                <td>{revenue['source']}</td>
            </tr>
            """
            
        content += "</table>"
        return content
        
    def generate_inventory_html_content(self, data: Dict[str, Any]) -> str:
        """إنشاء محتوى HTML لتقرير المخزون"""
        content = """
        <h2>تقرير المخزون</h2>
        <table>
            <tr><th>الصنف</th><th>الكمية</th><th>الوحدة</th><th>القيمة</th></tr>
        """
        
        for item in data['items']:
            content += f"""
            <tr>
                <td>{item['name']}</td>
                <td>{item['quantity']}</td>
                <td>{item['unit']}</td>
                <td>{item['value']:,} ريال</td>
            </tr>
            """
            
        content += f"""
        </table>
        <div class="summary">
            <h3>ملخص المخزون</h3>
            <p><strong>إجمالي قيمة المخزون:</strong> {data['total_value']:,} ريال</p>
            <p><strong>عدد الأصناف منخفضة المخزون:</strong> {len(data['low_stock_items'])}</p>
        </div>
        """
        
        return content
