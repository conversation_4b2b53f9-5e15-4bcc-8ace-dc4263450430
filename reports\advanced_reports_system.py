#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام التقارير المتقدم مع دعم PDF والعربية - PySide6
Advanced Reports System with PDF and Arabic Support - PySide6
"""

import os
import json
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.backends.backend_pdf import PdfPages
import seaborn as sns

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QTableWidget, QTableWidgetItem, QHeaderView, QLineEdit,
    QComboBox, QTextEdit, QDateEdit, QSpinBox, QDoubleSpinBox,
    QGroupBox, QFormLayout, Q<PERSON><PERSON><PERSON>, QDialogButtonBox,
    QMessageBox, QFileDialog, QTab<PERSON>idget, QSplitter,
    QTreeWidget, QTreeWidgetItem, QProgressBar, QCheckBox,
    QGridLayout, QFrame, QCalendarWidget
)
from PySide6.QtCore import Qt, QDate, Signal, QTimer, QThread
from PySide6.QtGui import QFont, QColor, QPixmap, QIcon

try:
    from reportlab.lib.pagesizes import A4, letter
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch, cm
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
    from reportlab.lib import colors
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    import arabic_reshaper
    from bidi.algorithm import get_display
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

from ui.modern_styles import ModernStyleManager
from ui.icons_manager import IconsManager


@dataclass
class ReportTemplate:
    """قالب التقرير"""
    id: str
    name: str
    description: str
    report_type: str  # مالي، مخزون، عملاء، مشاريع
    fields: List[str]
    filters: Dict[str, Any]
    chart_types: List[str]
    created_date: str

    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.created_date:
            self.created_date = datetime.now().isoformat()


class ArabicPDFGenerator:
    """مولد PDF مع دعم العربية"""

    def __init__(self):
        self.setup_arabic_fonts()

    def setup_arabic_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            # تسجيل خط عربي (يجب توفر ملف الخط)
            font_path = "assets/fonts/NotoSansArabic-Regular.ttf"
            if os.path.exists(font_path):
                pdfmetrics.registerFont(TTFont('Arabic', font_path))
                self.arabic_font = 'Arabic'
            else:
                self.arabic_font = 'Helvetica'  # خط احتياطي
        except:
            self.arabic_font = 'Helvetica'

    def reshape_arabic_text(self, text: str) -> str:
        """تشكيل النص العربي للعرض الصحيح"""
        try:
            reshaped_text = arabic_reshaper.reshape(text)
            bidi_text = get_display(reshaped_text)
            return bidi_text
        except:
            return text

    def create_arabic_paragraph_style(self, name: str, font_size: int = 12,
                                    alignment: int = 2) -> ParagraphStyle:
        """إنشاء نمط فقرة عربية"""
        return ParagraphStyle(
            name=name,
            fontName=self.arabic_font,
            fontSize=font_size,
            alignment=alignment,  # 2 = محاذاة يمين
            rightIndent=0,
            leftIndent=0,
            spaceAfter=6,
            spaceBefore=6
        )


class FinancialReportGenerator:
    """مولد التقارير المالية"""

    def __init__(self, db_managers: Dict[str, Any]):
        self.db_managers = db_managers
        self.pdf_generator = ArabicPDFGenerator()

    def generate_revenue_report(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """تقرير الإيرادات"""
        try:
            # جمع البيانات من قواعد البيانات المختلفة
            clients_data = self.db_managers['clients'].get_all_clients()

            # حساب الإيرادات
            total_revenue = sum(client.get('total_revenue', 0) for client in clients_data)
            total_orders = sum(client.get('total_orders', 0) for client in clients_data)

            # تحليل الإيرادات حسب الفترة
            revenue_by_month = self._calculate_monthly_revenue(clients_data, start_date, end_date)

            # أفضل العملاء
            top_clients = sorted(clients_data,
                               key=lambda x: x.get('total_revenue', 0),
                               reverse=True)[:10]

            report_data = {
                'title': 'تقرير الإيرادات',
                'period': f"من {start_date} إلى {end_date}",
                'total_revenue': total_revenue,  # د.ل
                'total_orders': total_orders,
                'average_order_value': total_revenue / total_orders if total_orders > 0 else 0,  # د.ل
                'revenue_by_month': revenue_by_month,
                'top_clients': top_clients,
                'generated_date': datetime.now().isoformat()
            }

            return report_data

        except Exception as e:
            print(f"خطأ في إنشاء تقرير الإيرادات: {e}")
            return {}

    def _calculate_monthly_revenue(self, clients_data: List[Dict],
                                 start_date: str, end_date: str) -> Dict[str, float]:
        """حساب الإيرادات الشهرية"""
        # هذه دالة مبسطة - في التطبيق الحقيقي ستحتاج لبيانات تفصيلية أكثر
        monthly_revenue = {}

        start = datetime.fromisoformat(start_date)
        end = datetime.fromisoformat(end_date)

        current = start
        while current <= end:
            month_key = current.strftime("%Y-%m")
            # توزيع الإيرادات بشكل تقريبي على الأشهر
            monthly_revenue[month_key] = sum(
                client.get('total_revenue', 0) / 12
                for client in clients_data
            )

            # الانتقال للشهر التالي
            if current.month == 12:
                current = current.replace(year=current.year + 1, month=1)
            else:
                current = current.replace(month=current.month + 1)

        return monthly_revenue

    def generate_profit_loss_report(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """تقرير الأرباح والخسائر"""
        try:
            # جمع بيانات الإيرادات
            revenue_data = self.generate_revenue_report(start_date, end_date)

            # جمع بيانات التكاليف (من المخزون والمشاريع)
            inventory_data = self.db_managers['inventory'].get_all_inventory_items()

            # حساب التكاليف
            total_costs = sum(
                item.get('current_stock', 0) * item.get('unit_cost', 0)
                for item in inventory_data
            )

            # حساب الأرباح
            total_revenue = revenue_data.get('total_revenue', 0)
            gross_profit = total_revenue - total_costs
            profit_margin = (gross_profit / total_revenue * 100) if total_revenue > 0 else 0

            report_data = {
                'title': 'تقرير الأرباح والخسائر',
                'period': f"من {start_date} إلى {end_date}",
                'total_revenue': total_revenue,  # د.ل
                'total_costs': total_costs,  # د.ل
                'gross_profit': gross_profit,  # د.ل
                'profit_margin': profit_margin,  # %
                'generated_date': datetime.now().isoformat()
            }

            return report_data

        except Exception as e:
            print(f"خطأ في إنشاء تقرير الأرباح والخسائر: {e}")
            return {}


class InventoryReportGenerator:
    """مولد تقارير المخزون"""

    def __init__(self, db_managers: Dict[str, Any]):
        self.db_managers = db_managers
        self.pdf_generator = ArabicPDFGenerator()

    def generate_stock_status_report(self) -> Dict[str, Any]:
        """تقرير حالة المخزون"""
        try:
            inventory_data = self.db_managers['inventory'].get_all_inventory_items()

            # تصنيف العناصر
            in_stock = [item for item in inventory_data if item.get('current_stock', 0) > item.get('minimum_stock', 0)]
            low_stock = [item for item in inventory_data if 0 < item.get('current_stock', 0) <= item.get('minimum_stock', 0)]
            out_of_stock = [item for item in inventory_data if item.get('current_stock', 0) <= 0]

            # حساب القيم
            total_value = sum(
                item.get('current_stock', 0) * item.get('unit_cost', 0)
                for item in inventory_data
            )

            # تحليل حسب الفئات
            categories = {}
            for item in inventory_data:
                category = item.get('category', 'غير محدد')
                if category not in categories:
                    categories[category] = {
                        'count': 0,
                        'value': 0,
                        'items': []
                    }
                categories[category]['count'] += 1
                categories[category]['value'] += item.get('current_stock', 0) * item.get('unit_cost', 0)
                categories[category]['items'].append(item)

            report_data = {
                'title': 'تقرير حالة المخزون',
                'total_items': len(inventory_data),
                'total_value': total_value,
                'in_stock_count': len(in_stock),
                'low_stock_count': len(low_stock),
                'out_of_stock_count': len(out_of_stock),
                'in_stock_items': in_stock,
                'low_stock_items': low_stock,
                'out_of_stock_items': out_of_stock,
                'categories': categories,
                'generated_date': datetime.now().isoformat()
            }

            return report_data

        except Exception as e:
            print(f"خطأ في إنشاء تقرير المخزون: {e}")
            return {}

    def generate_movement_report(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """تقرير حركة المخزون"""
        try:
            # هذا يتطلب تطبيق نظام تتبع الحركات في قاعدة البيانات
            # للتبسيط، سنعرض تقرير أساسي

            inventory_data = self.db_managers['inventory'].get_all_inventory_items()

            # تحليل الحركات (مبسط)
            movements_summary = {
                'total_items_moved': len(inventory_data),
                'items_added': len([item for item in inventory_data if item.get('current_stock', 0) > 0]),
                'items_removed': len([item for item in inventory_data if item.get('current_stock', 0) <= 0]),
            }

            report_data = {
                'title': 'تقرير حركة المخزون',
                'period': f"من {start_date} إلى {end_date}",
                'movements_summary': movements_summary,
                'generated_date': datetime.now().isoformat()
            }

            return report_data

        except Exception as e:
            print(f"خطأ في إنشاء تقرير حركة المخزون: {e}")
            return {}


class ClientReportGenerator:
    """مولد تقارير العملاء"""

    def __init__(self, db_managers: Dict[str, Any]):
        self.db_managers = db_managers
        self.pdf_generator = ArabicPDFGenerator()

    def generate_client_analysis_report(self) -> Dict[str, Any]:
        """تقرير تحليل العملاء"""
        try:
            clients_data = self.db_managers['clients'].get_all_clients()

            # تحليل العملاء
            total_clients = len(clients_data)
            active_clients = len([c for c in clients_data if c.get('status') == 'نشط'])

            # تحليل حسب الأولوية
            priority_analysis = {}
            for client in clients_data:
                priority = client.get('priority', 'عادي')
                if priority not in priority_analysis:
                    priority_analysis[priority] = 0
                priority_analysis[priority] += 1

            # تحليل حسب المدينة
            city_analysis = {}
            for client in clients_data:
                city = client.get('city', 'غير محدد')
                if city not in city_analysis:
                    city_analysis[city] = 0
                city_analysis[city] += 1

            # أفضل العملاء من حيث الإيرادات
            top_revenue_clients = sorted(
                clients_data,
                key=lambda x: x.get('total_revenue', 0),
                reverse=True
            )[:10]

            # أكثر العملاء طلبات
            top_orders_clients = sorted(
                clients_data,
                key=lambda x: x.get('total_orders', 0),
                reverse=True
            )[:10]

            report_data = {
                'title': 'تقرير تحليل العملاء',
                'total_clients': total_clients,
                'active_clients': active_clients,
                'inactive_clients': total_clients - active_clients,
                'priority_analysis': priority_analysis,
                'city_analysis': city_analysis,
                'top_revenue_clients': top_revenue_clients,
                'top_orders_clients': top_orders_clients,
                'generated_date': datetime.now().isoformat()
            }

            return report_data

        except Exception as e:
            print(f"خطأ في إنشاء تقرير تحليل العملاء: {e}")
            return {}


class AdvancedReportsWidget(QWidget):
    """واجهة التقارير المتقدمة"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.db_managers = {}  # سيتم تمرير مديري قواعد البيانات
        self.report_generators = {}
        self.init_ui()
        self.setup_report_generators()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)

        # شريط الأدوات
        self.create_toolbar(layout)

        # المحتوى الرئيسي
        main_splitter = QSplitter(Qt.Orientation.Horizontal)

        # قائمة التقارير
        self.create_reports_panel(main_splitter)

        # منطقة المعاينة والإعدادات
        self.create_preview_panel(main_splitter)

        # تعيين النسب
        main_splitter.setSizes([300, 700])

        layout.addWidget(main_splitter)

    def create_reports_panel(self, parent_splitter):
        """إنشاء لوحة التقارير"""
        reports_widget = QWidget()
        layout = QVBoxLayout(reports_widget)

        # قائمة التقارير المتاحة
        reports_group = QGroupBox("التقارير المتاحة")
        reports_layout = QVBoxLayout(reports_group)

        # أزرار التقارير
        financial_btn = QPushButton("📊 تقرير مالي")
        financial_btn.clicked.connect(self.generate_financial_report)

        clients_btn = QPushButton("👥 تقرير العملاء")
        clients_btn.clicked.connect(self.generate_clients_report)

        inventory_btn = QPushButton("📦 تقرير المخزون")
        inventory_btn.clicked.connect(self.generate_inventory_report)

        profit_loss_btn = QPushButton("💰 تقرير الأرباح والخسائر")
        profit_loss_btn.clicked.connect(self.generate_profit_loss_report)

        reports_layout.addWidget(financial_btn)
        reports_layout.addWidget(clients_btn)
        reports_layout.addWidget(inventory_btn)
        reports_layout.addWidget(profit_loss_btn)

        layout.addWidget(reports_group)
        layout.addStretch()
        parent_splitter.addWidget(reports_widget)

    def create_toolbar(self, parent_layout):
        """إنشاء شريط الأدوات"""
        toolbar_widget = QWidget()
        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(0, 0, 0, 0)

        # أزرار التقارير
        financial_btn = QPushButton("تقرير مالي")
        financial_btn.setIcon(IconsManager.get_standard_icon('money'))
        financial_btn.clicked.connect(self.generate_financial_report)

        clients_btn = QPushButton("تقرير العملاء")
        clients_btn.setIcon(IconsManager.get_standard_icon('users'))
        clients_btn.clicked.connect(self.generate_clients_report)

        inventory_btn = QPushButton("تقرير المخزون")
        inventory_btn.setIcon(IconsManager.get_standard_icon('database'))
        inventory_btn.clicked.connect(self.generate_inventory_report)

        export_btn = QPushButton("تصدير PDF")
        export_btn.setIcon(IconsManager.get_standard_icon('export'))
        export_btn.clicked.connect(self.export_current_report)

        toolbar_layout.addWidget(financial_btn)
        toolbar_layout.addWidget(clients_btn)
        toolbar_layout.addWidget(inventory_btn)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(export_btn)

        parent_layout.addWidget(toolbar_widget)
    def generate_financial_report(self):
        """إنشاء تقرير مالي"""
        try:
            # فتح حوار اختيار الفترة
            dialog = ReportPeriodDialog(self, "التقرير المالي")
            if dialog.exec() == QDialog.DialogCode.Accepted:
                start_date, end_date = dialog.get_period()

                # إنشاء التقرير
                if 'financial' in self.report_generators:
                    report_data = self.report_generators['financial'].generate_revenue_report(
                        start_date, end_date
                    )

                    if report_data:
                        self.display_financial_report(report_data)
                    else:
                        QMessageBox.warning(self, "خطأ", "فشل في إنشاء التقرير المالي")
                else:
                    QMessageBox.warning(self, "خطأ", "مولد التقارير المالية غير متاح")

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في إنشاء التقرير المالي: {e}")

    def display_financial_report(self, report_data: Dict[str, Any]):
        """عرض التقرير المالي"""
        try:
            from ui.modern_styles import CurrencyFormatter

            # تنسيق التقرير
            report_text = f"""
📊 {report_data.get('title', 'التقرير المالي')}
{'='*50}

📅 الفترة: {report_data.get('period', 'غير محدد')}
📅 تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M')}

💰 الملخص المالي:
{'='*30}
• إجمالي الإيرادات: {CurrencyFormatter.format_currency(report_data.get('total_revenue', 0))}
• إجمالي الطلبات: {report_data.get('total_orders', 0):,}
• متوسط قيمة الطلب: {CurrencyFormatter.format_currency(report_data.get('average_order_value', 0))}

👥 أفضل العملاء (حسب الإيرادات):
{'='*40}
"""

            # إضافة أفضل العملاء
            top_clients = report_data.get('top_clients', [])[:5]
            for i, client in enumerate(top_clients, 1):
                client_name = client.get('name', 'غير معروف')
                client_revenue = CurrencyFormatter.format_currency(client.get('total_revenue', 0))
                report_text += f"{i}. {client_name}: {client_revenue}\n"

            # إضافة الإيرادات الشهرية
            monthly_revenue = report_data.get('revenue_by_month', {})
            if monthly_revenue:
                report_text += f"\n📈 الإيرادات الشهرية:\n{'='*25}\n"
                for month, revenue in monthly_revenue.items():
                    formatted_revenue = CurrencyFormatter.format_currency(revenue)
                    report_text += f"• {month}: {formatted_revenue}\n"

            # عرض التقرير في منطقة المعاينة
            self.preview_area.setPlainText(report_text)
            self.current_report_data = report_data

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في عرض التقرير: {e}")

    def generate_clients_report(self):
        """إنشاء تقرير العملاء"""
        try:
            # إنشاء التقرير
            if 'clients' in self.report_generators:
                report_data = self.report_generators['clients'].generate_client_analysis_report()

                if report_data:
                    self.display_clients_report(report_data)
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في إنشاء تقرير العملاء")
            else:
                QMessageBox.warning(self, "خطأ", "مولد تقارير العملاء غير متاح")

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في إنشاء تقرير العملاء: {e}")

    def display_clients_report(self, report_data: Dict[str, Any]):
        """عرض تقرير العملاء"""
        try:
            from ui.modern_styles import CurrencyFormatter

            # تنسيق التقرير
            report_text = f"""
👥 {report_data.get('title', 'تقرير تحليل العملاء')}
{'='*50}

📅 تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M')}

📊 إحصائيات العملاء:
{'='*25}
• إجمالي العملاء: {report_data.get('total_clients', 0):,}
• العملاء النشطون: {report_data.get('active_clients', 0):,}
• العملاء غير النشطين: {report_data.get('inactive_clients', 0):,}

🏆 أفضل العملاء (حسب الإيرادات):
{'='*40}
"""

            # إضافة أفضل العملاء
            top_clients = report_data.get('top_revenue_clients', [])[:5]
            for i, client in enumerate(top_clients, 1):
                client_name = client.get('name', 'غير معروف')
                client_revenue = CurrencyFormatter.format_currency(client.get('total_revenue', 0))
                client_orders = client.get('total_orders', 0)
                report_text += f"{i}. {client_name}: {client_revenue} ({client_orders} طلب)\n"

            # تحليل حسب الأولوية
            priority_analysis = report_data.get('priority_analysis', {})
            if priority_analysis:
                report_text += f"\n⭐ تحليل حسب الأولوية:\n{'='*25}\n"
                for priority, count in priority_analysis.items():
                    report_text += f"• {priority}: {count:,} عميل\n"

            # تحليل حسب المدينة
            city_analysis = report_data.get('city_analysis', {})
            if city_analysis:
                report_text += f"\n🏙️ تحليل حسب المدينة:\n{'='*25}\n"
                sorted_cities = sorted(city_analysis.items(), key=lambda x: x[1], reverse=True)
                for city, count in sorted_cities[:10]:  # أفضل 10 مدن
                    report_text += f"• {city}: {count:,} عميل\n"

            # عرض التقرير في منطقة المعاينة
            self.preview_area.setPlainText(report_text)
            self.current_report_data = report_data

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في عرض تقرير العملاء: {e}")

    def generate_inventory_report(self):
        """إنشاء تقرير المخزون"""
        try:
            # إنشاء التقرير
            if 'inventory' in self.report_generators:
                report_data = self.report_generators['inventory'].generate_stock_status_report()

                if report_data:
                    self.display_inventory_report(report_data)
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في إنشاء تقرير المخزون")
            else:
                QMessageBox.warning(self, "خطأ", "مولد تقارير المخزون غير متاح")

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في إنشاء تقرير المخزون: {e}")

    def display_inventory_report(self, report_data: Dict[str, Any]):
        """عرض تقرير المخزون"""
        try:
            from ui.modern_styles import CurrencyFormatter

            # تنسيق التقرير
            report_text = f"""
📦 {report_data.get('title', 'تقرير حالة المخزون')}
{'='*50}

📅 تاريخ الإنشاء: {datetime.now().strftime('%Y-%m-%d %H:%M')}

📊 إحصائيات المخزون:
{'='*25}
• إجمالي العناصر: {report_data.get('total_items', 0):,}
• إجمالي قيمة المخزون: {CurrencyFormatter.format_currency(report_data.get('total_value', 0))}
• العناصر المتاحة: {report_data.get('in_stock_count', 0):,}
• العناصر منخفضة المخزون: {report_data.get('low_stock_count', 0):,}
• العناصر نافدة المخزون: {report_data.get('out_of_stock_count', 0):,}

⚠️ العناصر منخفضة المخزون:
{'='*35}
"""

            # إضافة العناصر منخفضة المخزون
            low_stock_items = report_data.get('low_stock_items', [])[:10]
            if low_stock_items:
                for item in low_stock_items:
                    item_name = item.get('name', 'غير معروف')
                    current_stock = item.get('current_stock', 0)
                    minimum_stock = item.get('minimum_stock', 0)
                    unit = item.get('unit', '')
                    report_text += f"• {item_name}: {current_stock:.1f} {unit} (الحد الأدنى: {minimum_stock:.1f})\n"
            else:
                report_text += "لا توجد عناصر منخفضة المخزون\n"

            # إضافة العناصر نافدة المخزون
            out_of_stock_items = report_data.get('out_of_stock_items', [])[:10]
            if out_of_stock_items:
                report_text += f"\n❌ العناصر نافدة المخزون:\n{'='*30}\n"
                for item in out_of_stock_items:
                    item_name = item.get('name', 'غير معروف')
                    unit = item.get('unit', '')
                    report_text += f"• {item_name} ({unit})\n"

            # تحليل حسب الفئات
            categories = report_data.get('categories', {})
            if categories:
                report_text += f"\n📂 تحليل حسب الفئات:\n{'='*25}\n"
                sorted_categories = sorted(categories.items(), key=lambda x: x[1]['value'], reverse=True)
                for category, data in sorted_categories:
                    count = data.get('count', 0)
                    value = CurrencyFormatter.format_currency(data.get('value', 0))
                    report_text += f"• {category}: {count:,} عنصر - {value}\n"

            # عرض التقرير في منطقة المعاينة
            self.preview_area.setPlainText(report_text)
            self.current_report_data = report_data

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في عرض تقرير المخزون: {e}")

    def export_current_report(self):
        """تصدير التقرير الحالي"""
        try:
            if not hasattr(self, 'current_report_data') or not self.current_report_data:
                QMessageBox.warning(self, "تحذير", "لا يوجد تقرير لتصديره. يرجى إنشاء تقرير أولاً.")
                return

            # اختيار نوع التصدير
            export_dialog = ReportExportDialog(self)
            if export_dialog.exec() == QDialog.DialogCode.Accepted:
                export_format, file_path = export_dialog.get_export_settings()

                if export_format == "PDF":
                    self.export_to_pdf(file_path)
                elif export_format == "HTML":
                    self.export_to_html(file_path)
                elif export_format == "CSV":
                    self.export_to_csv(file_path)
                else:
                    QMessageBox.warning(self, "خطأ", "نوع التصدير غير مدعوم")

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تصدير التقرير: {e}")

    def export_to_pdf(self, file_path: str):
        """تصدير التقرير إلى PDF"""
        try:
            if not REPORTLAB_AVAILABLE:
                QMessageBox.warning(self, "خطأ", "مكتبة ReportLab غير متاحة لتصدير PDF")
                return

            # إنشاء مستند PDF
            doc = SimpleDocTemplate(file_path, pagesize=A4)
            story = []

            # إعداد الأنماط
            styles = getSampleStyleSheet()
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1  # وسط
            )

            # إضافة العنوان
            title = self.current_report_data.get('title', 'تقرير')
            story.append(Paragraph(title, title_style))
            story.append(Spacer(1, 12))

            # إضافة محتوى التقرير
            report_content = self.preview_area.toPlainText()
            lines = report_content.split('\n')

            for line in lines:
                if line.strip():
                    story.append(Paragraph(line, styles['Normal']))
                    story.append(Spacer(1, 6))

            # بناء المستند
            doc.build(story)

            QMessageBox.information(self, "تصدير ناجح", f"تم تصدير التقرير إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تصدير PDF: {e}")

    def export_to_html(self, file_path: str):
        """تصدير التقرير إلى HTML"""
        try:
            report_content = self.preview_area.toPlainText()
            title = self.current_report_data.get('title', 'تقرير')

            html_content = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 40px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }}
        .container {{
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }}
        pre {{
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }}
        .footer {{
            text-align: center;
            margin-top: 30px;
            color: #7f8c8d;
            font-size: 12px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>{title}</h1>
        <pre>{report_content}</pre>
        <div class="footer">
            تم إنشاء هذا التقرير بواسطة نظام إدارة الأثاث المتقدم
        </div>
    </div>
</body>
</html>
"""

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            QMessageBox.information(self, "تصدير ناجح", f"تم تصدير التقرير إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تصدير HTML: {e}")

    def export_to_csv(self, file_path: str):
        """تصدير التقرير إلى CSV"""
        try:
            import csv

            # استخراج البيانات من التقرير الحالي
            data_rows = []

            if 'top_clients' in self.current_report_data:
                # تقرير العملاء
                data_rows.append(['اسم العميل', 'إجمالي الإيرادات (د.ل)', 'عدد الطلبات'])
                for client in self.current_report_data['top_clients']:
                    data_rows.append([
                        client.get('name', ''),
                        client.get('total_revenue', 0),
                        client.get('total_orders', 0)
                    ])
            elif 'low_stock_items' in self.current_report_data:
                # تقرير المخزون
                data_rows.append(['اسم العنصر', 'المخزون الحالي', 'الحد الأدنى', 'الوحدة'])
                for item in self.current_report_data['low_stock_items']:
                    data_rows.append([
                        item.get('name', ''),
                        item.get('current_stock', 0),
                        item.get('minimum_stock', 0),
                        item.get('unit', '')
                    ])
            else:
                # تقرير عام
                data_rows.append(['البيان', 'القيمة'])
                for key, value in self.current_report_data.items():
                    if isinstance(value, (str, int, float)):
                        data_rows.append([key, value])

            # كتابة ملف CSV
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerows(data_rows)

            QMessageBox.information(self, "تصدير ناجح", f"تم تصدير التقرير إلى:\n{file_path}")

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في تصدير CSV: {e}")

    def generate_profit_loss_report(self):
        """إنشاء تقرير الأرباح والخسائر"""
        try:
            from ui.modern_styles import CurrencyFormatter

            # بيانات تجريبية للأرباح والخسائر
            total_revenue = 150000.0  # د.ل
            total_costs = 100000.0    # د.ل
            gross_profit = total_revenue - total_costs
            profit_margin = (gross_profit / total_revenue) * 100 if total_revenue > 0 else 0

            report_data = {
                'title': 'تقرير الأرباح والخسائر',
                'period': 'الشهر الحالي',
                'total_revenue': CurrencyFormatter.format_currency(total_revenue),
                'total_costs': CurrencyFormatter.format_currency(total_costs),
                'gross_profit': CurrencyFormatter.format_currency(gross_profit),
                'profit_margin': f"{profit_margin:.1f}%"
            }

            # عرض التقرير
            report_text = f"""
📊 {report_data['title']}
الفترة: {report_data['period']}

💰 إجمالي الإيرادات: {report_data['total_revenue']}
💸 إجمالي التكاليف: {report_data['total_costs']}
📈 إجمالي الربح: {report_data['gross_profit']}
📊 هامش الربح: {report_data['profit_margin']}
            """

            QMessageBox.information(self, "تقرير الأرباح والخسائر", report_text)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"فشل في إنشاء تقرير الأرباح والخسائر: {e}")

    def create_preview_panel(self, parent_splitter):
        """إنشاء لوحة المعاينة"""
        preview_widget = QWidget()
        layout = QVBoxLayout(preview_widget)

        # عنوان المعاينة
        title_label = QLabel("معاينة التقرير")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; margin: 10px;")

        # منطقة المعاينة
        self.preview_area = QTextEdit()
        self.preview_area.setReadOnly(True)
        self.preview_area.setPlainText("اختر تقريراً لعرض المعاينة هنا...")

        # أزرار المعاينة
        buttons_widget = QWidget()
        buttons_layout = QHBoxLayout(buttons_widget)

        refresh_btn = QPushButton("تحديث")
        refresh_btn.clicked.connect(self.refresh_preview)

        print_btn = QPushButton("طباعة")
        print_btn.clicked.connect(self.print_report)

        buttons_layout.addWidget(refresh_btn)
        buttons_layout.addWidget(print_btn)
        buttons_layout.addStretch()

        layout.addWidget(title_label)
        layout.addWidget(self.preview_area)
        layout.addWidget(buttons_widget)

        parent_splitter.addWidget(preview_widget)

    def refresh_preview(self):
        """تحديث المعاينة"""
        self.preview_area.setPlainText("تم تحديث المعاينة...")

    def print_report(self):
        """طباعة التقرير"""
        QMessageBox.information(self, "طباعة", "سيتم تنفيذ الطباعة قريباً")


    def setup_report_generators(self):
        """إعداد مولدات التقارير"""
        self.report_generators = {
            'financial': FinancialReportGenerator(self.db_managers),
            'inventory': InventoryReportGenerator(self.db_managers),
            'clients': ClientReportGenerator(self.db_managers)
        }

    def set_database_managers(self, db_managers: Dict[str, Any]):
        """تعيين مديري قواعد البيانات"""
        self.db_managers = db_managers
        self.setup_report_generators()


class ReportPeriodDialog(QDialog):
    """حوار اختيار فترة التقرير"""

    def __init__(self, parent=None, report_title="تقرير"):
        super().__init__(parent)
        self.report_title = report_title
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle(f"اختيار فترة {self.report_title}")
        self.setModal(True)
        self.resize(400, 300)

        layout = QVBoxLayout(self)

        # عنوان
        title_label = QLabel(f"اختر الفترة الزمنية لـ {self.report_title}")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; margin: 10px;")
        layout.addWidget(title_label)

        # مجموعة التواريخ
        dates_group = QGroupBox("الفترة الزمنية")
        dates_layout = QFormLayout(dates_group)

        # تاريخ البداية
        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addMonths(-1))
        self.start_date.setCalendarPopup(True)
        dates_layout.addRow("من تاريخ:", self.start_date)

        # تاريخ النهاية
        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        dates_layout.addRow("إلى تاريخ:", self.end_date)

        layout.addWidget(dates_group)

        # فترات سريعة
        quick_periods_group = QGroupBox("فترات سريعة")
        quick_layout = QVBoxLayout(quick_periods_group)

        this_month_btn = QPushButton("هذا الشهر")
        this_month_btn.clicked.connect(self.set_this_month)

        last_month_btn = QPushButton("الشهر الماضي")
        last_month_btn.clicked.connect(self.set_last_month)

        this_year_btn = QPushButton("هذا العام")
        this_year_btn.clicked.connect(self.set_this_year)

        last_year_btn = QPushButton("العام الماضي")
        last_year_btn.clicked.connect(self.set_last_year)

        quick_layout.addWidget(this_month_btn)
        quick_layout.addWidget(last_month_btn)
        quick_layout.addWidget(this_year_btn)
        quick_layout.addWidget(last_year_btn)

        layout.addWidget(quick_periods_group)

        # أزرار الحوار
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        buttons.button(QDialogButtonBox.StandardButton.Ok).setText("موافق")
        buttons.button(QDialogButtonBox.StandardButton.Cancel).setText("إلغاء")

        layout.addWidget(buttons)

    def set_this_month(self):
        """تعيين الشهر الحالي"""
        today = QDate.currentDate()
        first_day = QDate(today.year(), today.month(), 1)
        self.start_date.setDate(first_day)
        self.end_date.setDate(today)

    def set_last_month(self):
        """تعيين الشهر الماضي"""
        today = QDate.currentDate()
        last_month = today.addMonths(-1)
        first_day = QDate(last_month.year(), last_month.month(), 1)
        last_day = QDate(today.year(), today.month(), 1).addDays(-1)
        self.start_date.setDate(first_day)
        self.end_date.setDate(last_day)

    def set_this_year(self):
        """تعيين العام الحالي"""
        today = QDate.currentDate()
        first_day = QDate(today.year(), 1, 1)
        self.start_date.setDate(first_day)
        self.end_date.setDate(today)

    def set_last_year(self):
        """تعيين العام الماضي"""
        today = QDate.currentDate()
        last_year = today.year() - 1
        first_day = QDate(last_year, 1, 1)
        last_day = QDate(last_year, 12, 31)
        self.start_date.setDate(first_day)
        self.end_date.setDate(last_day)

    def get_period(self) -> Tuple[str, str]:
        """الحصول على الفترة المختارة"""
        start = self.start_date.date().toString("yyyy-MM-dd")
        end = self.end_date.date().toString("yyyy-MM-dd")
        return start, end

    def accept(self):
        """التحقق من صحة التواريخ قبل الموافقة"""
        if self.start_date.date() > self.end_date.date():
            QMessageBox.warning(self, "خطأ", "تاريخ البداية يجب أن يكون قبل تاريخ النهاية")
            return

        super().accept()


class ReportExportDialog(QDialog):
    """حوار تصدير التقرير"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("تصدير التقرير")
        self.setModal(True)
        self.resize(400, 250)

        layout = QVBoxLayout(self)

        # عنوان
        title_label = QLabel("اختر نوع التصدير والموقع")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; margin: 10px;")
        layout.addWidget(title_label)

        # نوع التصدير
        format_group = QGroupBox("نوع الملف")
        format_layout = QVBoxLayout(format_group)

        self.pdf_radio = QCheckBox("PDF - ملف PDF")
        self.pdf_radio.setChecked(True)

        self.html_radio = QCheckBox("HTML - صفحة ويب")
        self.csv_radio = QCheckBox("CSV - ملف بيانات")

        format_layout.addWidget(self.pdf_radio)
        format_layout.addWidget(self.html_radio)
        format_layout.addWidget(self.csv_radio)

        layout.addWidget(format_group)

        # مسار الملف
        file_group = QGroupBox("موقع الحفظ")
        file_layout = QHBoxLayout(file_group)

        self.file_path_edit = QLineEdit()
        self.file_path_edit.setPlaceholderText("اختر موقع حفظ الملف...")

        browse_btn = QPushButton("تصفح")
        browse_btn.clicked.connect(self.browse_file)

        file_layout.addWidget(self.file_path_edit)
        file_layout.addWidget(browse_btn)

        layout.addWidget(file_group)

        # أزرار الحوار
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        buttons.button(QDialogButtonBox.StandardButton.Ok).setText("تصدير")
        buttons.button(QDialogButtonBox.StandardButton.Cancel).setText("إلغاء")

        layout.addWidget(buttons)

    def browse_file(self):
        """تصفح موقع الملف"""
        format_type = self.get_selected_format()

        if format_type == "PDF":
            file_filter = "PDF Files (*.pdf)"
            default_name = "report.pdf"
        elif format_type == "HTML":
            file_filter = "HTML Files (*.html)"
            default_name = "report.html"
        elif format_type == "CSV":
            file_filter = "CSV Files (*.csv)"
            default_name = "report.csv"
        else:
            file_filter = "All Files (*.*)"
            default_name = "report"

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "حفظ التقرير",
            default_name,
            file_filter
        )

        if file_path:
            self.file_path_edit.setText(file_path)

    def get_selected_format(self) -> str:
        """الحصول على نوع التصدير المختار"""
        if self.pdf_radio.isChecked():
            return "PDF"
        elif self.html_radio.isChecked():
            return "HTML"
        elif self.csv_radio.isChecked():
            return "CSV"
        else:
            return "PDF"  # افتراضي

    def get_export_settings(self) -> Tuple[str, str]:
        """الحصول على إعدادات التصدير"""
        format_type = self.get_selected_format()
        file_path = self.file_path_edit.text().strip()
        return format_type, file_path

    def accept(self):
        """التحقق من صحة الإعدادات قبل الموافقة"""
        if not self.file_path_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى اختيار موقع حفظ الملف")
            return

        if not any([self.pdf_radio.isChecked(), self.html_radio.isChecked(), self.csv_radio.isChecked()]):
            QMessageBox.warning(self, "خطأ", "يرجى اختيار نوع التصدير")
            return

        super().accept()
