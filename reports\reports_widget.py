#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
واجهة التقارير المتقدمة لتطبيق تصميم الأثاث
Advanced Reports Widget for Furniture Design Application
"""

import os
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import asdict
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QDateEdit, QTextEdit, QGroupBox,
    QTableWidget, QTableWidgetItem, QProgressBar, QMessageBox,
    QTabWidget, QCheckBox, QFileDialog, QSplitter
)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QFont, QColor

try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

from ui.modern_styles import ModernStyleManager
from ui.icons_manager import IconsManager
from .advanced_reports import (
    ReportType, ReportFormat, ReportConfig, ReportGenerator
)


class AdvancedReportsWidget(QWidget):
    """واجهة التقارير المتقدمة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_config = None
        self.data_sources = {}
        self.init_ui()
        
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(8)
        
        # العنوان
        title_label = QLabel("نظام التقارير المتقدم")
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['title']}px;
                font-weight: bold;
                color: {ModernStyleManager.COLORS['primary']};
                padding: 16px;
                background-color: {ModernStyleManager.COLORS['surface']};
                border-radius: 8px;
                margin-bottom: 8px;
            }}
        """)
        
        # المقسم الرئيسي
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # لوحة الإعدادات
        self.create_settings_panel(main_splitter)
        
        # منطقة المعاينة
        self.create_preview_area(main_splitter)
        
        # تعيين النسب
        main_splitter.setSizes([400, 600])
        
        layout.addWidget(title_label)
        layout.addWidget(main_splitter)
        
    def create_settings_panel(self, parent_splitter):
        """إنشاء لوحة الإعدادات"""
        settings_widget = QWidget()
        settings_widget.setMaximumWidth(450)
        layout = QVBoxLayout(settings_widget)
        
        # مجموعة نوع التقرير
        report_type_group = QGroupBox("نوع التقرير")
        report_type_layout = QVBoxLayout(report_type_group)
        
        self.report_type_combo = QComboBox()
        self.report_type_combo.addItems([
            "تقرير مالي", "تقرير المخزون", "تقرير العملاء",
            "تقرير المشاريع", "تقرير الإنتاج", "تقرير المبيعات"
        ])
        self.report_type_combo.currentTextChanged.connect(self.on_report_type_changed)
        
        report_type_layout.addWidget(self.report_type_combo)
        
        # مجموعة الفترة الزمنية
        date_group = QGroupBox("الفترة الزمنية")
        date_layout = QVBoxLayout(date_group)
        
        date_from_layout = QHBoxLayout()
        date_from_layout.addWidget(QLabel("من:"))
        self.date_from = QDateEdit()
        self.date_from.setDate(QDate.currentDate().addDays(-30))
        self.date_from.setCalendarPopup(True)
        date_from_layout.addWidget(self.date_from)
        
        date_to_layout = QHBoxLayout()
        date_to_layout.addWidget(QLabel("إلى:"))
        self.date_to = QDateEdit()
        self.date_to.setDate(QDate.currentDate())
        self.date_to.setCalendarPopup(True)
        date_to_layout.addWidget(self.date_to)
        
        date_layout.addLayout(date_from_layout)
        date_layout.addLayout(date_to_layout)
        
        # مجموعة الفلاتر
        filters_group = QGroupBox("الفلاتر")
        filters_layout = QVBoxLayout(filters_group)
        
        self.include_charts_check = QCheckBox("تضمين الرسوم البيانية")
        self.include_charts_check.setChecked(True)
        
        self.include_summary_check = QCheckBox("تضمين الملخص")
        self.include_summary_check.setChecked(True)
        
        filters_layout.addWidget(self.include_charts_check)
        filters_layout.addWidget(self.include_summary_check)
        
        # مجموعة صيغة التصدير
        format_group = QGroupBox("صيغة التصدير")
        format_layout = QVBoxLayout(format_group)
        
        self.format_combo = QComboBox()
        self.format_combo.addItems(["PDF", "Excel", "CSV", "HTML"])
        
        format_layout.addWidget(self.format_combo)
        
        # أزرار التحكم
        buttons_layout = QVBoxLayout()
        
        preview_btn = QPushButton("معاينة التقرير")
        preview_btn.setIcon(IconsManager.get_standard_icon('search'))
        preview_btn.clicked.connect(self.preview_report)
        
        generate_btn = QPushButton("إنشاء التقرير")
        generate_btn.setIcon(IconsManager.get_standard_icon('report'))
        generate_btn.clicked.connect(self.generate_report)
        
        save_config_btn = QPushButton("حفظ الإعدادات")
        save_config_btn.setIcon(IconsManager.get_standard_icon('save'))
        save_config_btn.clicked.connect(self.save_config)
        
        load_config_btn = QPushButton("تحميل إعدادات")
        load_config_btn.setIcon(IconsManager.get_standard_icon('open'))
        load_config_btn.clicked.connect(self.load_config)
        
        buttons_layout.addWidget(preview_btn)
        buttons_layout.addWidget(generate_btn)
        buttons_layout.addWidget(save_config_btn)
        buttons_layout.addWidget(load_config_btn)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        
        # إضافة المجموعات للتخطيط
        layout.addWidget(report_type_group)
        layout.addWidget(date_group)
        layout.addWidget(filters_group)
        layout.addWidget(format_group)
        layout.addLayout(buttons_layout)
        layout.addWidget(self.progress_bar)
        layout.addStretch()
        
        parent_splitter.addWidget(settings_widget)
        
    def create_preview_area(self, parent_splitter):
        """إنشاء منطقة المعاينة"""
        preview_widget = QWidget()
        layout = QVBoxLayout(preview_widget)
        
        # عنوان المعاينة
        preview_title = QLabel("معاينة التقرير")
        preview_title.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
                font-weight: bold;
                padding: 8px;
                background-color: {ModernStyleManager.COLORS['secondary']};
                color: {ModernStyleManager.COLORS['text_light']};
                border-radius: 6px;
            }}
        """)
        
        # تبويبات المعاينة
        self.preview_tabs = QTabWidget()
        
        # تبويب البيانات
        self.data_tab = QWidget()
        self.create_data_preview_tab()
        self.preview_tabs.addTab(self.data_tab, "البيانات")
        
        # تبويب الرسوم البيانية
        if MATPLOTLIB_AVAILABLE:
            self.charts_tab = QWidget()
            self.create_charts_preview_tab()
            self.preview_tabs.addTab(self.charts_tab, "الرسوم البيانية")
        
        # تبويب الملخص
        self.summary_tab = QWidget()
        self.create_summary_preview_tab()
        self.preview_tabs.addTab(self.summary_tab, "الملخص")
        
        layout.addWidget(preview_title)
        layout.addWidget(self.preview_tabs)
        
        parent_splitter.addWidget(preview_widget)
        
    def create_data_preview_tab(self):
        """إنشاء تبويب معاينة البيانات"""
        layout = QVBoxLayout(self.data_tab)
        
        self.data_table = QTableWidget()
        self.data_table.setAlternatingRowColors(True)
        
        layout.addWidget(self.data_table)
        
    def create_charts_preview_tab(self):
        """إنشاء تبويب معاينة الرسوم البيانية"""
        layout = QVBoxLayout(self.charts_tab)
        
        # إنشاء الشكل
        self.figure = Figure(figsize=(8, 6), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        
        layout.addWidget(self.canvas)
        
    def create_summary_preview_tab(self):
        """إنشاء تبويب معاينة الملخص"""
        layout = QVBoxLayout(self.summary_tab)
        
        self.summary_text = QTextEdit()
        self.summary_text.setReadOnly(True)
        self.summary_text.setStyleSheet(f"""
            QTextEdit {{
                background-color: {ModernStyleManager.COLORS['surface']};
                border: 1px solid {ModernStyleManager.COLORS['border']};
                border-radius: 4px;
                font-size: {ModernStyleManager.FONT_SIZES['body']}px;
                padding: 8px;
            }}
        """)
        
        layout.addWidget(self.summary_text)
        
    def on_report_type_changed(self, report_type: str):
        """عند تغيير نوع التقرير"""
        # تحديث الفلاتر والخيارات حسب نوع التقرير
        pass
        
    def preview_report(self):
        """معاينة التقرير"""
        try:
            config = self.create_report_config()
            
            # محاكاة البيانات للمعاينة
            if config.report_type == ReportType.FINANCIAL:
                data = self.get_sample_financial_data()
            elif config.report_type == ReportType.INVENTORY:
                data = self.get_sample_inventory_data()
            else:
                data = {"message": "بيانات تجريبية"}
                
            self.update_preview(data, config)
            
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في المعاينة: {str(e)}")
            
    def generate_report(self):
        """إنشاء التقرير"""
        try:
            config = self.create_report_config()
            
            # إظهار شريط التقدم
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            
            # إنشاء مولد التقرير
            self.generator = ReportGenerator(config, self.data_sources)
            self.generator.progress_updated.connect(self.update_progress)
            self.generator.report_generated.connect(self.on_report_generated)
            self.generator.generation_failed.connect(self.on_generation_failed)
            self.generator.start()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء التقرير: {str(e)}")
            
    def create_report_config(self) -> ReportConfig:
        """إنشاء إعدادات التقرير"""
        report_type_map = {
            "تقرير مالي": ReportType.FINANCIAL,
            "تقرير المخزون": ReportType.INVENTORY,
            "تقرير العملاء": ReportType.CLIENTS,
            "تقرير المشاريع": ReportType.PROJECTS,
            "تقرير الإنتاج": ReportType.PRODUCTION,
            "تقرير المبيعات": ReportType.SALES
        }
        
        format_map = {
            "PDF": ReportFormat.PDF,
            "Excel": ReportFormat.EXCEL,
            "CSV": ReportFormat.CSV,
            "HTML": ReportFormat.HTML
        }
        
        return ReportConfig(
            report_type=report_type_map[self.report_type_combo.currentText()],
            title=f"{self.report_type_combo.currentText()} - {datetime.now().strftime('%Y-%m-%d')}",
            date_from=self.date_from.date().toString("yyyy-MM-dd"),
            date_to=self.date_to.date().toString("yyyy-MM-dd"),
            filters={},
            format=format_map[self.format_combo.currentText()],
            include_charts=self.include_charts_check.isChecked(),
            include_summary=self.include_summary_check.isChecked()
        )
        
    def update_preview(self, data: Dict[str, Any], config: ReportConfig):
        """تحديث المعاينة"""
        # تحديث جدول البيانات
        self.update_data_table(data, config)
        
        # تحديث الرسوم البيانية
        if MATPLOTLIB_AVAILABLE and config.include_charts:
            self.update_charts(data, config)
            
        # تحديث الملخص
        if config.include_summary:
            self.update_summary(data, config)
            
    def update_data_table(self, data: Dict[str, Any], config: ReportConfig):
        """تحديث جدول البيانات"""
        if config.report_type == ReportType.FINANCIAL and 'revenue' in data:
            revenue_data = data['revenue']
            self.data_table.setRowCount(len(revenue_data))
            self.data_table.setColumnCount(3)
            self.data_table.setHorizontalHeaderLabels(["التاريخ", "المبلغ", "المصدر"])
            
            for row, item in enumerate(revenue_data):
                self.data_table.setItem(row, 0, QTableWidgetItem(item['date']))
                self.data_table.setItem(row, 1, QTableWidgetItem(f"{item['amount']:,}"))
                self.data_table.setItem(row, 2, QTableWidgetItem(item['source']))
                
        elif config.report_type == ReportType.INVENTORY and 'items' in data:
            items_data = data['items']
            self.data_table.setRowCount(len(items_data))
            self.data_table.setColumnCount(4)
            self.data_table.setHorizontalHeaderLabels(["الاسم", "الكمية", "الوحدة", "القيمة"])
            
            for row, item in enumerate(items_data):
                self.data_table.setItem(row, 0, QTableWidgetItem(item['name']))
                self.data_table.setItem(row, 1, QTableWidgetItem(str(item['quantity'])))
                self.data_table.setItem(row, 2, QTableWidgetItem(item['unit']))
                self.data_table.setItem(row, 3, QTableWidgetItem(f"{item['value']:,}"))
                
    def update_charts(self, data: Dict[str, Any], config: ReportConfig):
        """تحديث الرسوم البيانية"""
        self.figure.clear()
        
        if config.report_type == ReportType.FINANCIAL:
            ax = self.figure.add_subplot(111)
            
            # رسم بياني للإيرادات
            dates = [item['date'] for item in data['revenue']]
            amounts = [item['amount'] for item in data['revenue']]
            
            ax.plot(dates, amounts, marker='o', linewidth=2, markersize=6)
            ax.set_title('الإيرادات اليومية', fontsize=14, fontweight='bold')
            ax.set_xlabel('التاريخ')
            ax.set_ylabel('المبلغ (ريال)')
            ax.grid(True, alpha=0.3)
            
        elif config.report_type == ReportType.INVENTORY:
            ax = self.figure.add_subplot(111)
            
            # رسم بياني دائري للمخزون
            names = [item['name'] for item in data['items']]
            values = [item['value'] for item in data['items']]
            
            ax.pie(values, labels=names, autopct='%1.1f%%', startangle=90)
            ax.set_title('توزيع قيمة المخزون', fontsize=14, fontweight='bold')
            
        self.figure.tight_layout()
        self.canvas.draw()
        
    def update_summary(self, data: Dict[str, Any], config: ReportConfig):
        """تحديث الملخص"""
        summary_text = f"<h2>{config.title}</h2>"
        summary_text += f"<p><strong>الفترة:</strong> {config.date_from} إلى {config.date_to}</p>"
        
        if config.report_type == ReportType.FINANCIAL:
            summary_text += f"""
            <h3>الملخص المالي</h3>
            <ul>
                <li>إجمالي الإيرادات: {data['total_revenue']:,} ريال</li>
                <li>إجمالي المصروفات: {data['total_expenses']:,} ريال</li>
                <li>صافي الربح: {data['net_profit']:,} ريال</li>
                <li>هامش الربح: {data['profit_margin']*100:.1f}%</li>
            </ul>
            """
            
        elif config.report_type == ReportType.INVENTORY:
            summary_text += f"""
            <h3>ملخص المخزون</h3>
            <ul>
                <li>إجمالي قيمة المخزون: {data['total_value']:,} ريال</li>
                <li>عدد الأصناف: {len(data['items'])}</li>
                <li>أصناف منخفضة المخزون: {len(data['low_stock_items'])}</li>
            </ul>
            """
            
        self.summary_text.setHtml(summary_text)
        
    def get_sample_financial_data(self) -> Dict[str, Any]:
        """الحصول على بيانات مالية تجريبية"""
        return {
            'revenue': [
                {'date': '2024-01-01', 'amount': 15000, 'source': 'مبيعات'},
                {'date': '2024-01-02', 'amount': 8500, 'source': 'خدمات'},
                {'date': '2024-01-03', 'amount': 12000, 'source': 'مبيعات'},
            ],
            'expenses': [
                {'date': '2024-01-01', 'amount': 5000, 'category': 'مواد خام'},
                {'date': '2024-01-02', 'amount': 2000, 'category': 'رواتب'},
            ],
            'total_revenue': 35500,
            'total_expenses': 7000,
            'net_profit': 28500,
            'profit_margin': 0.8
        }
        
    def get_sample_inventory_data(self) -> Dict[str, Any]:
        """الحصول على بيانات مخزون تجريبية"""
        return {
            'items': [
                {'name': 'خشب بلوط', 'quantity': 150, 'unit': 'متر مربع', 'value': 45000},
                {'name': 'مسامير', 'quantity': 5000, 'unit': 'قطعة', 'value': 2500},
                {'name': 'غراء خشب', 'quantity': 25, 'unit': 'لتر', 'value': 1250},
            ],
            'total_value': 48750,
            'low_stock_items': ['غراء خشب']
        }
        
    def update_progress(self, value: int, message: str):
        """تحديث شريط التقدم"""
        self.progress_bar.setValue(value)
        
    def on_report_generated(self, file_path: str, report_data: Dict[str, Any]):
        """عند إنشاء التقرير بنجاح"""
        self.progress_bar.setVisible(False)
        
        QMessageBox.information(self, "نجح الإنشاء", 
                              f"تم إنشاء التقرير بنجاح!\n\nالملف: {file_path}")
        
    def on_generation_failed(self, error_message: str):
        """عند فشل إنشاء التقرير"""
        self.progress_bar.setVisible(False)
        
        QMessageBox.critical(self, "خطأ في الإنشاء", 
                           f"فشل في إنشاء التقرير:\n\n{error_message}")
        
    def save_config(self):
        """حفظ إعدادات التقرير"""
        try:
            config = self.create_report_config()
            
            file_dialog = QFileDialog()
            file_path, _ = file_dialog.getSaveFileName(
                self,
                "حفظ إعدادات التقرير",
                f"report_config_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                "ملفات JSON (*.json);;جميع الملفات (*)"
            )
            
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(asdict(config), f, ensure_ascii=False, indent=2)
                    
                QMessageBox.information(self, "نجح الحفظ", "تم حفظ إعدادات التقرير بنجاح!")
                
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في حفظ الإعدادات: {str(e)}")
            
    def load_config(self):
        """تحميل إعدادات التقرير"""
        try:
            file_dialog = QFileDialog()
            file_path, _ = file_dialog.getOpenFileName(
                self,
                "تحميل إعدادات التقرير",
                "",
                "ملفات JSON (*.json);;جميع الملفات (*)"
            )
            
            if file_path:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    
                # تطبيق الإعدادات على الواجهة
                self.apply_config_to_ui(config_data)
                
                QMessageBox.information(self, "نجح التحميل", "تم تحميل إعدادات التقرير بنجاح!")
                
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"خطأ في تحميل الإعدادات: {str(e)}")
            
    def apply_config_to_ui(self, config_data: Dict[str, Any]):
        """تطبيق الإعدادات على الواجهة"""
        # تطبيق نوع التقرير
        report_type_map = {
            "financial": "تقرير مالي",
            "inventory": "تقرير المخزون",
            "clients": "تقرير العملاء",
            "projects": "تقرير المشاريع",
            "production": "تقرير الإنتاج",
            "sales": "تقرير المبيعات"
        }
        
        if config_data.get('report_type') in report_type_map:
            self.report_type_combo.setCurrentText(report_type_map[config_data['report_type']])
            
        # تطبيق التواريخ
        if config_data.get('date_from'):
            self.date_from.setDate(QDate.fromString(config_data['date_from'], "yyyy-MM-dd"))
        if config_data.get('date_to'):
            self.date_to.setDate(QDate.fromString(config_data['date_to'], "yyyy-MM-dd"))
            
        # تطبيق الخيارات
        self.include_charts_check.setChecked(config_data.get('include_charts', True))
        self.include_summary_check.setChecked(config_data.get('include_summary', True))
        
        # تطبيق صيغة التصدير
        format_map = {
            "pdf": "PDF",
            "excel": "Excel",
            "csv": "CSV",
            "html": "HTML"
        }
        
        if config_data.get('format') in format_map:
            self.format_combo.setCurrentText(format_map[config_data['format']])
