# Advanced Professional Furniture Designer - Version 2.0 Requirements
# متطلبات تطبيق مصمم الأثاث الاحترافي المتقدم - الإصدار 2.0 - PySide6

# Core GUI Framework - إطار العمل الأساسي المحدث
PySide6>=6.4.0
PySide6-Addons>=6.4.0

# 3D Model Processing and Advanced Visualization - معالجة النماذج ثلاثية الأبعاد المتقدمة
trimesh>=4.0.0
numpy>=1.20.0
scipy>=1.7.0

# Data Processing and Analysis - معالجة وتحليل البيانات
pandas>=1.3.0

# Advanced Excel Export Features - ميزات تصدير Excel المتقدمة
openpyxl>=3.0.0
xlsxwriter>=3.0.0

# PDF Export with Full Arabic Support - تصدير PDF مع دعم العربية الكامل
reportlab>=3.6.0
arabic-reshaper>=3.0.0
python-bidi>=0.4.0

# Advanced Image Processing and Visualization - معالجة الصور والتصور المتقدم
Pillow>=8.0.0
matplotlib>=3.5.0
seaborn>=0.11.0

# Serial Communication for Advanced CNC Integration - الاتصال التسلسلي لتكامل CNC المتقدم
pyserial>=3.5

# System Information and Performance Monitoring - معلومات النظام ومراقبة الأداء
psutil>=5.8.0

# Advanced Date and Time Processing - معالجة التاريخ والوقت المتقدمة
python-dateutil>=2.8.0

# QR Code and Barcode Support - دعم رموز QR والباركود
qrcode>=7.3.0

# Database Management - إدارة قواعد البيانات
# sqlite3 مدمج مع Python ولا يحتاج تثبيت منفصل

# Advanced Mathematical Operations - العمليات الرياضية المتقدمة
sympy>=1.9.0

# Color Management and Themes - إدارة الألوان والثيمات
colorcet>=2.0.0

# Optional Professional Features - ميزات احترافية اختيارية
# Open3D>=0.15.0          # تصور ثلاثي الأبعاد متقدم
# vtk>=9.1.0              # مكتبة التصور العلمي
# scikit-learn>=1.0.0     # تعلم الآلة للتحسين
# SQLAlchemy>=1.4.0       # دعم قواعد البيانات المتقدمة

# Development Tools - أدوات التطوير (اختيارية)
# pytest>=6.2.0          # اختبار الوحدة
# black>=21.0.0           # تنسيق الكود
# flake8>=4.0.0           # فحص جودة الكود
