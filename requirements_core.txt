# Core Requirements for Advanced Professional Furniture Designer - Version 2.1
# المتطلبات الأساسية لمصمم الأثاث الاحترافي المتقدم - الإصدار 2.1 - PySide6

# ===== ESSENTIAL REQUIREMENTS (مطلوبة للتشغيل الأساسي) =====

# Core GUI Framework - إطار العمل الأساسي
PySide6>=6.4.0

# 3D Model Processing - معالجة النماذج ثلاثية الأبعاد
trimesh>=4.0.0
numpy>=1.20.0

# Data Processing - معالجة البيانات
pandas>=1.3.0

# Visualization - التصور والرسوم البيانية
matplotlib>=3.5.0

# Image Processing - معالجة الصور
Pillow>=8.0.0

# ===== ADVANCED FEATURES (للميزات المتقدمة) =====

# Excel Export - تصدير Excel
openpyxl>=3.0.0

# Date and Time Processing - معالجة التاريخ والوقت
python-dateutil>=2.8.0

# ===== OPTIONAL FEATURES (ميزات اختيارية) =====

# PDF Export with Arabic Support - تصدير PDF مع دعم العربية
# تثبيت هذه المكتبات اختياري للحصول على ميزات PDF المتقدمة
# reportlab>=3.6.0
# arabic-reshaper>=3.0.0
# python-bidi>=0.4.0

# Advanced Visualization - تصور متقدم
# seaborn>=0.11.0

# Scientific Computing - الحوسبة العلمية
# scipy>=1.7.0

# Serial Communication for CNC - الاتصال التسلسلي لآلات CNC
# pyserial>=3.5

# System Information - معلومات النظام
# psutil>=5.8.0

# QR Code Support - دعم رموز QR
# qrcode>=7.3.0

# Mathematical Operations - العمليات الرياضية
# sympy>=1.9.0

# Color Management - إدارة الألوان
# colorcet>=2.0.0

# ===== NOTES (ملاحظات) =====
# 
# 1. sqlite3 مدمج مع Python ولا يحتاج تثبيت منفصل
# 2. PySide6-Addons اختياري ويمكن تثبيته لاحقاً إذا لزم الأمر
# 3. المكتبات المعلقة (بـ #) اختيارية ويمكن تثبيتها حسب الحاجة
# 4. للحصول على جميع الميزات، استخدم: pip install -r requirements.txt
# 5. للتثبيت الأساسي فقط، استخدم: pip install -r requirements_core.txt
