#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل تطبيق مصمم الأثاث الاحترافي المتقدم - PySide6
Advanced Professional Furniture Designer Application Launcher - PySide6
"""

import sys
import os
import traceback
from pathlib import Path
from PySide6.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QPixmap, QFont, QPainter, QColor

# إضافة المجلد الحالي لمسار Python
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# التحقق من المتطلبات
def check_requirements():
    """التحقق من توفر المكتبات المطلوبة"""
    missing_packages = []

    try:
        import PySide6
    except ImportError:
        missing_packages.append("PySide6")

    try:
        import trimesh
    except ImportError:
        missing_packages.append("trimesh")

    try:
        import numpy
    except ImportError:
        missing_packages.append("numpy")

    try:
        import matplotlib
    except ImportError:
        missing_packages.append("matplotlib")

    try:
        import pandas
    except ImportError:
        missing_packages.append("pandas")

    try:
        import openpyxl
    except ImportError:
        missing_packages.append("openpyxl")

    return missing_packages

def create_splash_screen():
    """إنشاء شاشة البداية"""
    # إنشاء صورة شاشة البداية
    pixmap = QPixmap(600, 400)
    pixmap.fill(QColor(52, 73, 94))  # لون خلفية داكن

    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing)

    # رسم العنوان
    painter.setPen(QColor(255, 255, 255))
    title_font = QFont("Arial", 24, QFont.Weight.Bold)
    painter.setFont(title_font)
    painter.drawText(50, 150, "🪑 مصمم الأثاث الاحترافي المتقدم")

    # رسم العنوان الفرعي
    subtitle_font = QFont("Arial", 14)
    painter.setFont(subtitle_font)
    painter.drawText(50, 180, "Advanced Professional Furniture Designer")

    # رسم معلومات الإصدار
    version_font = QFont("Arial", 10)
    painter.setFont(version_font)
    painter.drawText(50, 220, "الإصدار 2.1 - PySide6 + العملة الليبية")
    painter.drawText(50, 240, "✓ تحديث إلى PySide6 لتحسين الأداء")
    painter.drawText(50, 260, "✓ دعم شامل للدينار الليبي (د.ل)")
    painter.drawText(50, 280, "✓ معاين ثلاثي الأبعاد محسن مع إضاءة واقعية")
    painter.drawText(50, 300, "✓ نظام إدارة العملاء والمخزون المتقدم")
    painter.drawText(50, 320, "✓ تكامل CNC وتقارير PDF مع العربية")

    painter.end()

    return QSplashScreen(pixmap)

def setup_application():
    """إعداد التطبيق"""
    app = QApplication(sys.argv)
    app.setApplicationName("مصمم الأثاث الاحترافي المتقدم")
    app.setApplicationVersion("2.1")
    app.setOrganizationName("Libyan Furniture Design Solutions")

    # إعداد الخط الافتراضي
    font = QFont("Segoe UI", 10)
    app.setFont(font)

    return app

def main():
    """الدالة الرئيسية"""
    try:
        # إعداد التطبيق
        app = setup_application()

        # إنشاء شاشة البداية
        splash = create_splash_screen()
        splash.show()
        app.processEvents()

        # التحقق من المتطلبات
        splash.showMessage("التحقق من المتطلبات...", Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter, QColor(255, 255, 255))
        app.processEvents()

        missing_packages = check_requirements()
        if missing_packages:
            splash.close()
            error_msg = f"""
المكتبات التالية مفقودة:
{', '.join(missing_packages)}

يرجى تثبيتها باستخدام:
pip install {' '.join(missing_packages)}

أو تشغيل:
pip install -r requirements.txt
            """
            QMessageBox.critical(None, "مكتبات مفقودة", error_msg)
            return 1

        # تحميل الوحدات
        splash.showMessage("تحميل الوحدات...", Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter, QColor(255, 255, 255))
        app.processEvents()

        try:
            from main_application import ModernMainWindow
        except ImportError as e:
            splash.close()
            QMessageBox.critical(None, "خطأ في التحميل", f"فشل في تحميل التطبيق الرئيسي:\n{str(e)}")
            return 1

        # إنشاء النافذة الرئيسية
        splash.showMessage("إنشاء النافذة الرئيسية...", Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter, QColor(255, 255, 255))
        app.processEvents()

        main_window = ModernMainWindow()

        # إخفاء شاشة البداية وعرض النافذة الرئيسية
        def show_main_window():
            splash.close()
            main_window.show()

            # عرض رسالة ترحيب
            welcome_msg = """
🎉 مرحباً بك في مصمم الأثاث الاحترافي المتقدم - الإصدار 2.1!

🆕 الجديد في هذا الإصدار:

🔹 تحديث إطار العمل:
   • الانتقال إلى PySide6 لتحسين الأداء
   • استقرار أعلى وسرعة أكبر
   • دعم أفضل للأنظمة الحديثة

🔹 دعم العملة الليبية:
   • تطبيق شامل للدينار الليبي (د.ل)
   • تنسيق احترافي للمبالغ المالية
   • تقارير مالية بالعملة المحلية

🔹 الميزات المتقدمة:
   • معاين ثلاثي الأبعاد مع إضاءة واقعية
   • نظام إدارة العملاء والمخزون الذكي
   • تكامل ماكينات CNC المحسن
   • تقارير PDF مع دعم العربية الكامل

🔹 تحسينات الواجهة:
   • تصميم أكثر عصرية وسهولة
   • أيقونات محسنة ومتجاوبة
   • دعم أفضل للغة العربية

مصمم خصيصاً لمصنعي الأثاث في ليبيا والمنطقة العربية! 🪑✨🇱🇾
            """

            QMessageBox.information(main_window, "مرحباً بك!", welcome_msg)

        # تأخير عرض النافذة الرئيسية
        QTimer.singleShot(2000, show_main_window)

        # تشغيل التطبيق
        return app.exec()

    except Exception as e:
        error_msg = f"""
حدث خطأ غير متوقع:
{str(e)}

تفاصيل الخطأ:
{traceback.format_exc()}

يرجى التأكد من:
1. تثبيت جميع المتطلبات
2. صحة ملفات التطبيق
3. صلاحيات الكتابة في المجلد
        """

        if 'app' in locals():
            QMessageBox.critical(None, "خطأ في التطبيق", error_msg)
        else:
            print(error_msg)

        return 1

if __name__ == "__main__":
    # تعيين متغيرات البيئة للدعم الأفضل
    os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '1'
    os.environ['QT_ENABLE_HIGHDPI_SCALING'] = '1'

    # تشغيل التطبيق
    exit_code = main()
    sys.exit(exit_code)
