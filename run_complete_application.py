#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل التطبيق المكتمل - نظام إدارة الأثاث المتقدم
Run Complete Application - Advanced Furniture Management System

هذا الملف يشغل التطبيق المكتمل مع جميع التحسينات والميزات الجديدة
"""

import sys
import os
from pathlib import Path

# إضافة المجلد الحالي لمسار Python
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def check_dependencies():
    """فحص التبعيات المطلوبة"""
    print("🔍 فحص التبعيات المطلوبة...")
    
    required_packages = [
        ('PySide6', 'PySide6'),
        ('sqlite3', 'sqlite3'),
        ('json', 'json'),
        ('datetime', 'datetime'),
        ('uuid', 'uuid'),
        ('pathlib', 'pathlib')
    ]
    
    missing_packages = []
    
    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
            print(f"   ✅ {package_name}: متاح")
        except ImportError:
            print(f"   ❌ {package_name}: مفقود")
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"\n⚠️ المكتبات المفقودة: {', '.join(missing_packages)}")
        print("يرجى تثبيتها باستخدام:")
        for package in missing_packages:
            if package == 'PySide6':
                print(f"   pip install {package}>=6.4.0")
            else:
                print(f"   pip install {package}")
        return False
    
    print("✅ جميع التبعيات متاحة")
    return True

def check_application_files():
    """فحص ملفات التطبيق"""
    print("\n🔍 فحص ملفات التطبيق...")
    
    required_files = [
        'main_application.py',
        'ui/modern_styles.py',
        'ui/icons_manager.py',
        'clients/advanced_client_system.py',
        'inventory/advanced_inventory_system.py',
        'reports/advanced_reports_system.py',
        'cnc/advanced_cnc_system.py'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}: موجود")
        else:
            print(f"   ❌ {file_path}: مفقود")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n⚠️ الملفات المفقودة: {', '.join(missing_files)}")
        return False
    
    print("✅ جميع ملفات التطبيق موجودة")
    return True

def run_application():
    """تشغيل التطبيق"""
    print("\n🚀 تشغيل التطبيق المكتمل...")
    
    try:
        # استيراد التطبيق الرئيسي
        from main_application import AdvancedFurnitureDesignerApp
        from PySide6.QtWidgets import QApplication
        from PySide6.QtCore import Qt
        from PySide6.QtGui import QIcon
        
        # إنشاء تطبيق Qt
        app = QApplication(sys.argv)
        app.setApplicationName("نظام إدارة الأثاث المتقدم")
        app.setApplicationVersion("2.0.0")
        app.setOrganizationName("Furniture Design Pro")
        
        # تعيين أيقونة التطبيق إذا كانت متاحة
        try:
            if os.path.exists("assets/icons/app_icon.png"):
                app.setWindowIcon(QIcon("assets/icons/app_icon.png"))
        except:
            pass
        
        # إنشاء النافذة الرئيسية
        main_window = AdvancedFurnitureDesignerApp()
        
        # عرض النافذة
        main_window.show()
        
        print("✅ تم تشغيل التطبيق بنجاح!")
        print("\n📋 الميزات المتاحة:")
        print("   • إدارة العملاء المتقدمة")
        print("   • إدارة المخزون مع التنبيهات")
        print("   • حاسبة التكلفة الذكية")
        print("   • تقارير مالية شاملة")
        print("   • تكامل ماكينات CNC")
        print("   • تصدير PDF/Excel/CSV")
        print("   • دعم العملة الليبية")
        
        print("\n💡 نصائح الاستخدام:")
        print("   • استخدم تبويب 'العملاء' لإدارة قاعدة العملاء")
        print("   • استخدم تبويب 'المخزون' لتتبع المواد")
        print("   • استخدم 'حاسبة التكلفة' لتقدير المشاريع")
        print("   • استخدم 'التقارير' للتحليل المالي")
        print("   • استخدم 'CNC' لإدارة الماكينات")
        
        # تشغيل حلقة الأحداث
        sys.exit(app.exec())
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد التطبيق: {e}")
        print("تأكد من وجود جميع ملفات التطبيق")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return False

def show_welcome_message():
    """عرض رسالة الترحيب"""
    print("=" * 70)
    print("🎉 مرحباً بك في نظام إدارة الأثاث المتقدم - الإصدار المكتمل")
    print("=" * 70)
    print()
    print("🔧 تم إكمال جميع الميزات والتحسينات:")
    print("   ✅ نظام إدارة العملاء المتقدم")
    print("   ✅ نظام إدارة المخزون الذكي")
    print("   ✅ نظام التقارير الشامل")
    print("   ✅ تكامل ماكينات CNC")
    print("   ✅ دعم العملة الليبية")
    print("   ✅ تصدير متعدد الصيغ")
    print()
    print("🎯 التطبيق جاهز للاستخدام التجاري!")
    print()

def main():
    """الدالة الرئيسية"""
    show_welcome_message()
    
    # فحص التبعيات
    if not check_dependencies():
        print("\n❌ فشل في فحص التبعيات")
        input("اضغط Enter للخروج...")
        return False
    
    # فحص ملفات التطبيق
    if not check_application_files():
        print("\n❌ فشل في فحص ملفات التطبيق")
        input("اضغط Enter للخروج...")
        return False
    
    # تشغيل التطبيق
    try:
        run_application()
        return True
    except KeyboardInterrupt:
        print("\n\n⏸️ تم إيقاف التطبيق بواسطة المستخدم")
        return True
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 خطأ فادح: {e}")
        input("اضغط Enter للخروج...")
        sys.exit(1)
