#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل حاسبة التكلفة المحسنة
Run Enhanced Cost Calculator
"""

import sys
import os
from pathlib import Path

# إضافة المجلد الحالي لمسار Python
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QMessageBox
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

try:
    from calculator.enhanced_cost_calculator import EnhancedCostCalculatorWidget
    from ui.modern_styles import ModernStyleManager
    from ui.icons_manager import IconsManager
except ImportError as e:
    print(f"⚠️ خطأ في الاستيراد: {e}")
    print("تأكد من وجود جميع الملفات المطلوبة")
    sys.exit(1)


class EnhancedCalculatorMainWindow(QMainWindow):
    """النافذة الرئيسية لحاسبة التكلفة المحسنة"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_calculator()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("🧮 حاسبة التكلفة المتقدمة - مصمم الأثاث الاحترافي")
        self.setWindowIcon(IconsManager.get_action_icon('calculator'))
        
        # تعيين الحد الأدنى لحجم النافذة
        self.setMinimumSize(1400, 900)
        
        # تطبيق الأنماط الحديثة
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: {ModernStyleManager.COLORS['background']};
            }}
            
            QMenuBar {{
                background-color: {ModernStyleManager.COLORS['primary']};
                color: {ModernStyleManager.COLORS['text_light']};
                border: none;
                padding: 4px;
            }}
            
            QMenuBar::item {{
                background-color: transparent;
                padding: 8px 12px;
                border-radius: 4px;
            }}
            
            QMenuBar::item:selected {{
                background-color: {ModernStyleManager.COLORS['secondary']};
            }}
            
            QStatusBar {{
                background-color: {ModernStyleManager.COLORS['card']};
                border-top: 1px solid {ModernStyleManager.COLORS['primary']};
                color: {ModernStyleManager.COLORS['text']};
            }}
        """)
        
        # إنشاء شريط القوائم
        self.create_menu_bar()
        
        # إنشاء شريط الحالة
        self.statusBar().showMessage("🚀 مرحباً بك في حاسبة التكلفة المتقدمة")
        
        # توسيط النافذة
        self.center_window()
    
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu("ملف")
        
        new_action = file_menu.addAction("مشروع جديد")
        new_action.setShortcut("Ctrl+N")
        new_action.setIcon(IconsManager.get_action_icon('new_project'))
        new_action.triggered.connect(self.new_project)
        
        open_action = file_menu.addAction("فتح مشروع")
        open_action.setShortcut("Ctrl+O")
        open_action.setIcon(IconsManager.get_action_icon('open_project'))
        open_action.triggered.connect(self.open_project)
        
        save_action = file_menu.addAction("حفظ المشروع")
        save_action.setShortcut("Ctrl+S")
        save_action.setIcon(IconsManager.get_action_icon('save_project'))
        save_action.triggered.connect(self.save_project)
        
        file_menu.addSeparator()
        
        export_action = file_menu.addAction("تصدير تقرير")
        export_action.setIcon(IconsManager.get_action_icon('export_excel'))
        export_action.triggered.connect(self.export_report)
        
        file_menu.addSeparator()
        
        exit_action = file_menu.addAction("خروج")
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        
        # قائمة الأدوات
        tools_menu = menubar.addMenu("أدوات")
        
        calculator_action = tools_menu.addAction("آلة حاسبة")
        calculator_action.setIcon(IconsManager.get_action_icon('calculator'))
        calculator_action.triggered.connect(self.show_calculator)
        
        templates_action = tools_menu.addAction("قوالب المشاريع")
        templates_action.setIcon(IconsManager.get_action_icon('templates'))
        templates_action.triggered.connect(self.manage_templates)
        
        settings_action = tools_menu.addAction("الإعدادات")
        settings_action.setIcon(IconsManager.get_action_icon('settings'))
        settings_action.triggered.connect(self.show_settings)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")
        
        guide_action = help_menu.addAction("دليل الاستخدام")
        guide_action.setIcon(IconsManager.get_action_icon('help'))
        guide_action.triggered.connect(self.show_user_guide)
        
        about_action = help_menu.addAction("حول البرنامج")
        about_action.triggered.connect(self.show_about)
    
    def setup_calculator(self):
        """إعداد حاسبة التكلفة"""
        try:
            # إنشاء الودجت المركزي
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            # إنشاء التخطيط الرئيسي
            layout = QVBoxLayout(central_widget)
            layout.setContentsMargins(0, 0, 0, 0)
            
            # إنشاء حاسبة التكلفة المحسنة
            self.calculator_widget = EnhancedCostCalculatorWidget()
            
            # ربط الإشارات
            self.calculator_widget.project_saved.connect(self.on_project_saved)
            self.calculator_widget.budget_exceeded.connect(self.on_budget_exceeded)
            
            layout.addWidget(self.calculator_widget)
            
            self.statusBar().showMessage("✅ تم تحميل حاسبة التكلفة المحسنة بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في تحميل حاسبة التكلفة:\n{str(e)}")
            self.close()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
    
    # دوال القوائم
    def new_project(self):
        """إنشاء مشروع جديد"""
        reply = QMessageBox.question(
            self, "مشروع جديد",
            "هل تريد إنشاء مشروع جديد؟\nسيتم فقدان التغييرات غير المحفوظة.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            if hasattr(self, 'calculator_widget'):
                # إعادة تعيين الحاسبة
                self.statusBar().showMessage("تم إنشاء مشروع جديد")
    
    def open_project(self):
        """فتح مشروع موجود"""
        QMessageBox.information(self, "فتح مشروع", "سيتم تطوير هذه الميزة قريباً")
    
    def save_project(self):
        """حفظ المشروع الحالي"""
        QMessageBox.information(self, "حفظ المشروع", "سيتم تطوير هذه الميزة قريباً")
    
    def export_report(self):
        """تصدير تقرير"""
        QMessageBox.information(self, "تصدير تقرير", "سيتم تطوير هذه الميزة قريباً")
    
    def show_calculator(self):
        """عرض آلة حاسبة"""
        QMessageBox.information(self, "آلة حاسبة", "آلة الحاسبة متاحة في اللوحة اليمنى")
    
    def manage_templates(self):
        """إدارة قوالب المشاريع"""
        QMessageBox.information(self, "قوالب المشاريع", "سيتم تطوير هذه الميزة قريباً")
    
    def show_settings(self):
        """عرض الإعدادات"""
        QMessageBox.information(self, "الإعدادات", "سيتم تطوير هذه الميزة قريباً")
    
    def show_user_guide(self):
        """عرض دليل الاستخدام"""
        guide_text = """
🧮 دليل استخدام حاسبة التكلفة المتقدمة

📋 معلومات المشروع:
• أدخل اسم المشروع والعميل
• حدد نوع العميل للحصول على خصم تلقائي
• اختر حجم المشروع لتطبيق معامل التعقيد

🔨 المواد:
• أضف المواد مع تحديد النوع للحصول على نسبة هدر تلقائية
• أدخل تكلفة التوصيل لكل مادة
• يمكن استيراد المواد من نظام المخزون

👷 العمالة:
• حدد مستوى المهارة لتطبيق معامل الأجر
• أدخل معامل الصعوبة للمهام المعقدة
• احسب الساعات الإضافية بمعامل منفصل

⚡ المرافق:
• احسب تكاليف الكهرباء والمياه والغاز
• أدخل تكلفة الإنترنت اليومية

⚙️ الإعدادات المتقدمة:
• اضبط نسب التكاليف العامة والأرباح
• احسب عائد الاستثمار للمشاريع الكبيرة
• طبق الضرائب والخصومات

📊 النتائج:
• اعرض ملخص التكاليف التفصيلي
• شاهد الرسوم البيانية لتوزيع التكاليف
• قارن بين بدائل مختلفة للمشروع
        """
        
        QMessageBox.information(self, "دليل الاستخدام", guide_text)
    
    def show_about(self):
        """عرض معلومات حول البرنامج"""
        about_text = """
🧮 حاسبة التكلفة المتقدمة

الإصدار 2.0 - Enhanced Edition
جزء من مصمم الأثاث الاحترافي

الميزات الجديدة:
✅ حساب تكاليف المواد مع نسب هدر متغيرة
✅ نظام عمالة متقدم مع مستويات مهارة
✅ حساب تكاليف المرافق الشاملة
✅ حساب عائد الاستثمار (ROI) مع NPV
✅ خصومات تلقائية حسب نوع العميل
✅ رسوم بيانية تفاعلية للتكاليف
✅ آلة حاسبة مدمجة
✅ قوالب مشاريع قابلة للحفظ
✅ مقارنة البدائل المختلفة
✅ تقارير احترافية قابلة للتصدير

العملة المدعومة: الدينار الليبي (د.ل)

تطوير: فريق التطوير الاحترافي
        """
        
        QMessageBox.about(self, "حول البرنامج", about_text)
    
    # دوال الإشارات
    def on_project_saved(self, project_name):
        """عند حفظ المشروع"""
        self.statusBar().showMessage(f"✅ تم حفظ المشروع: {project_name}")
    
    def on_budget_exceeded(self, variance):
        """عند تجاوز الميزانية"""
        self.statusBar().showMessage(f"⚠️ تجاوز الميزانية بمقدار: {variance:.2f} د.ل")


def main():
    """الدالة الرئيسية"""
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    
    # تعيين معلومات التطبيق
    app.setApplicationName("حاسبة التكلفة المتقدمة")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("مصمم الأثاث الاحترافي")
    
    try:
        # إنشاء النافذة الرئيسية
        window = EnhancedCalculatorMainWindow()
        window.show()
        
        print("🚀 تم تشغيل حاسبة التكلفة المحسنة بنجاح!")
        print("🧮 استمتع بالميزات الجديدة والمتقدمة!")
        
        # تشغيل التطبيق
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        QMessageBox.critical(None, "خطأ", f"فشل في تشغيل التطبيق:\n{str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
