#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل سريع لاختبار الانتقال إلى PySide6 ودعم العملة الليبية
Quick Run for PySide6 Migration and Libyan Currency Support Test
"""

import sys
import os
from pathlib import Path

# إضافة المجلد الحالي لمسار Python
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def main():
    """الدالة الرئيسية للاختبار السريع"""
    print("🚀 اختبار سريع للانتقال إلى PySide6 ودعم العملة الليبية")
    print("=" * 60)
    
    # اختبار 1: استيراد PySide6
    print("\n1️⃣ اختبار استيراد PySide6...")
    try:
        from PySide6.QtWidgets import QApplication, QWidget
        from PySide6.QtCore import Qt, Signal
        from PySide6.QtGui import QIcon
        print("✅ PySide6 متاح ويعمل بشكل صحيح")
    except ImportError as e:
        print(f"❌ خطأ في استيراد PySide6: {e}")
        print("💡 يرجى تثبيت PySide6: pip install PySide6")
        return False
    
    # اختبار 2: استيراد منسق العملة
    print("\n2️⃣ اختبار منسق العملة الليبية...")
    try:
        from ui.modern_styles import CurrencyFormatter
        
        # اختبار التنسيق
        amount = 1234.56
        formatted = CurrencyFormatter.format_currency(amount)
        print(f"✅ تنسيق العملة: {amount} → {formatted}")
        
        # اختبار التنسيق المختصر
        large_amount = 1500000
        short_format = CurrencyFormatter.format_currency_short(large_amount)
        print(f"✅ التنسيق المختصر: {large_amount} → {short_format}")
        
    except Exception as e:
        print(f"❌ خطأ في منسق العملة: {e}")
        return False
    
    # اختبار 3: استيراد الوحدات الرئيسية
    print("\n3️⃣ اختبار استيراد الوحدات الرئيسية...")
    
    modules_to_test = [
        ("المعاين ثلاثي الأبعاد", "models.advanced_3d_viewer"),
        ("نظام العملاء", "clients.advanced_client_system"),
        ("نظام المخزون", "inventory.advanced_inventory_system"),
        ("نظام CNC", "cnc.advanced_cnc_system"),
        ("نظام التقارير", "reports.advanced_reports_system"),
        ("الأنماط الحديثة", "ui.modern_styles"),
        ("مدير الأيقونات", "ui.icons_manager")
    ]
    
    failed_modules = []
    for module_name, module_path in modules_to_test:
        try:
            __import__(module_path)
            print(f"✅ {module_name}: متاح")
        except ImportError as e:
            print(f"❌ {module_name}: غير متاح - {e}")
            failed_modules.append(module_name)
    
    # اختبار 4: اختبار إنشاء كائنات العملة
    print("\n4️⃣ اختبار كائنات العملة...")
    try:
        from clients.advanced_client_system import Client
        from inventory.advanced_inventory_system import InventoryItem
        
        # إنشاء عميل تجريبي
        client = Client(
            id="test_001",
            name="عميل تجريبي",
            company="شركة الأثاث الليبية",
            phone="0912345678",
            email="<EMAIL>",
            address="طرابلس، ليبيا",
            city="طرابلس",
            country="ليبيا",
            postal_code="12345",
            tax_number="*********",
            payment_terms=30,
            discount_rate=0.05,
            credit_limit=50000.0,  # د.ل
            notes="عميل تجريبي",
            total_revenue=25000.0  # د.ل
        )
        
        formatted_credit = CurrencyFormatter.format_currency(client.credit_limit)
        formatted_revenue = CurrencyFormatter.format_currency(client.total_revenue)
        print(f"✅ عميل تجريبي: حد الائتمان {formatted_credit}, الإيرادات {formatted_revenue}")
        
        # إنشاء عنصر مخزون تجريبي
        item = InventoryItem(
            id="item_001",
            name="خشب البلوط الليبي",
            category="أخشاب محلية",
            subcategory="أخشاب صلبة",
            description="خشب بلوط محلي عالي الجودة",
            unit="متر مربع",
            current_stock=100.0,
            minimum_stock=20.0,
            maximum_stock=500.0,
            unit_cost=150.0,  # د.ل
            selling_price=200.0,  # د.ل
            supplier="مورد الأخشاب الليبي",
            location="مستودع طرابلس",
            barcode="*********0",
            notes="خشب محلي ممتاز"
        )
        
        formatted_cost = CurrencyFormatter.format_currency(item.unit_cost)
        formatted_price = CurrencyFormatter.format_currency(item.selling_price)
        print(f"✅ عنصر مخزون: التكلفة {formatted_cost}, السعر {formatted_price}")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار كائنات العملة: {e}")
        return False
    
    # اختبار 5: اختبار أيقونات العملة
    print("\n5️⃣ اختبار أيقونات العملة...")
    try:
        from ui.icons_manager import IconsManager
        
        currency_icon = IconsManager.get_standard_icon('currency')
        dinar_icon = IconsManager.get_standard_icon('dinar')
        price_icon = IconsManager.get_standard_icon('price')
        
        print("✅ أيقونات العملة متاحة (currency, dinar, price)")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار أيقونات العملة: {e}")
        return False
    
    # النتائج النهائية
    print("\n" + "=" * 60)
    if not failed_modules:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ التطبيق جاهز للتشغيل مع PySide6 والعملة الليبية")
        print("\n🚀 لتشغيل التطبيق:")
        print("   python run_advanced_furniture_designer.py")
        print("\n🧪 لتشغيل الاختبارات الشاملة:")
        print("   python test_pyside6_migration.py")
        return True
    else:
        print(f"⚠️ فشل في تحميل {len(failed_modules)} وحدة:")
        for module in failed_modules:
            print(f"   • {module}")
        print("\n💡 يرجى التأكد من:")
        print("   1. تثبيت جميع المتطلبات: pip install -r requirements.txt")
        print("   2. وجود جميع ملفات التطبيق")
        print("   3. صحة مسارات الملفات")
        return False

if __name__ == "__main__":
    success = main()
    
    print(f"\n{'='*60}")
    if success:
        print("🎯 الاختبار السريع مكتمل بنجاح!")
    else:
        print("⚠️ الاختبار السريع فشل - يرجى مراجعة الأخطاء أعلاه")
    
    sys.exit(0 if success else 1)
