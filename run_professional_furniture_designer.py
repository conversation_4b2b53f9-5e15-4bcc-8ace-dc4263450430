#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل تطبيق مصمم الأثاث الاحترافي - PyQt6 Edition
Professional Furniture Designer Launcher - PyQt6 Edition
"""

import sys
import os
import subprocess
import traceback
from pathlib import Path


def check_python_version():
    """فحص إصدار Python"""
    print("🔍 فحص إصدار Python...")

    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print(f"   الإصدار الحالي: {python_version.major}.{python_version.minor}.{python_version.micro}")
        return False

    print(f"✅ Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    return True


def check_required_packages():
    """فحص المكتبات المطلوبة"""
    print("\n🔍 فحص المكتبات المطلوبة...")

    required_packages = {
        'PyQt6': 'PyQt6',
        'trimesh': 'trimesh',
        'numpy': 'numpy',
        'pandas': 'pandas',
        'openpyxl': 'openpyxl',
        'matplotlib': 'matplotlib',
        'pyserial': 'serial'
    }

    optional_packages = {
        'reportlab': 'reportlab',
        'arabic-reshaper': 'arabic-reshaper',
        'python-bidi': 'python-bidi',
        'seaborn': 'seaborn',
        'scipy': 'scipy'
    }

    missing_required = []
    missing_optional = []

    # فحص المكتبات المطلوبة
    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
            print(f"✅ {package_name}")
        except ImportError:
            print(f"❌ {package_name} - غير مثبت")
            missing_required.append(package_name)

    # فحص المكتبات الاختيارية
    for package_name, import_name in optional_packages.items():
        try:
            __import__(import_name)
            print(f"✅ {package_name} (اختياري)")
        except ImportError:
            print(f"⚠️  {package_name} - غير مثبت (اختياري)")
            missing_optional.append(package_name)

    if missing_required:
        print(f"\n❌ مكتبات مطلوبة مفقودة: {', '.join(missing_required)}")
        print("يرجى تثبيتها باستخدام:")
        print(f"pip install {' '.join(missing_required)}")
        return False

    if missing_optional:
        print(f"\n⚠️  مكتبات اختيارية مفقودة: {', '.join(missing_optional)}")
        print("يمكن تثبيتها لميزات إضافية:")
        print(f"pip install {' '.join(missing_optional)}")

    return True


def install_missing_packages():
    """تثبيت المكتبات المفقودة"""
    print("\n📦 محاولة تثبيت المكتبات المفقودة...")

    try:
        # تحديث pip أولاً
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'])

        # تثبيت المتطلبات من الملف
        if os.path.exists('requirements.txt'):
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
            print("✅ تم تثبيت المكتبات من requirements.txt")
        else:
            # تثبيت المكتبات الأساسية
            basic_packages = [
                'PyQt6>=6.4.0',
                'trimesh>=4.0.0',
                'numpy>=1.20.0',
                'pandas>=1.3.0',
                'openpyxl>=3.0.0',
                'matplotlib>=3.5.0',
                'pyserial>=3.5'
            ]

            for package in basic_packages:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])

            print("✅ تم تثبيت المكتبات الأساسية")

        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في تثبيت المكتبات: {e}")
        return False


def create_directories():
    """إنشاء المجلدات المطلوبة"""
    print("\n📁 إنشاء المجلدات المطلوبة...")

    directories = [
        'assets',
        'assets/icons',
        'exports',
        'projects',
        'cnc_output',
        'clients_data',
        'inventory_data',
        'ui/widgets',
        'ui/dialogs'
    ]

    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"✅ {directory}")
        except Exception as e:
            print(f"⚠️  خطأ في إنشاء {directory}: {e}")

    print("✅ تم إنشاء جميع المجلدات")


def check_database_files():
    """فحص ملفات قواعد البيانات"""
    print("\n💾 فحص قواعد البيانات...")

    db_files = [
        'cutlist_pro.db',
        'clients.db',
        'inventory.db',
        'quotations.db'
    ]

    for db_file in db_files:
        if os.path.exists(db_file):
            print(f"✅ {db_file} موجود")
        else:
            print(f"ℹ️  {db_file} سيتم إنشاؤه عند الحاجة")


def run_application():
    """تشغيل التطبيق"""
    print("\n🚀 تشغيل تطبيق مصمم الأثاث الاحترافي...")

    try:
        # استيراد التطبيق الرئيسي
        from main_application import FurnitureDesignerApp

        # إنشاء وتشغيل التطبيق
        app = FurnitureDesignerApp(sys.argv)

        print("✅ تم تشغيل التطبيق بنجاح!")
        print("🎉 مرحباً بك في مصمم الأثاث الاحترافي!")

        # تشغيل حلقة الأحداث
        sys.exit(app.exec())

    except ImportError as e:
        print(f"❌ خطأ في استيراد التطبيق: {e}")
        print("تأكد من وجود جميع الملفات المطلوبة")
        return False

    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        print("\nتفاصيل الخطأ:")
        traceback.print_exc()
        return False


def run_simple_version():
    """تشغيل الإصدار المبسط"""
    print("\n🚀 تشغيل الإصدار المبسط من مصمم الأثاث الاحترافي...")

    try:
        # استيراد الإصدار المبسط
        from main_simple import SimpleFurnitureDesignerApp

        # إنشاء وتشغيل التطبيق
        app = SimpleFurnitureDesignerApp(sys.argv)

        print("✅ تم تشغيل الإصدار المبسط بنجاح!")
        print("🎉 مرحباً بك في مصمم الأثاث الاحترافي - الإصدار المبسط!")

        # تشغيل حلقة الأحداث
        sys.exit(app.exec())

    except ImportError as e:
        print(f"❌ خطأ في استيراد الإصدار المبسط: {e}")
        print("تأكد من وجود ملف main_simple.py")
        return False

    except Exception as e:
        print(f"❌ خطأ في تشغيل الإصدار المبسط: {e}")
        print("\nتفاصيل الخطأ:")
        traceback.print_exc()
        return False


def show_welcome_message():
    """عرض رسالة الترحيب"""
    print("=" * 80)
    print("🪑 مصمم الأثاث الاحترافي - Professional Furniture Designer")
    print("   الإصدار 3.0 - PyQt6 Edition")
    print("=" * 80)
    print()
    print("🌟 الميزات الجديدة في هذا الإصدار:")
    print("   • واجهة مستخدم حديثة ومهنية مع PyQt6")
    print("   • معاين ثلاثي الأبعاد متقدم للنماذج")
    print("   • نظام السحب والإفلات للملفات")
    print("   • تكامل حقيقي مع آلات CNC")
    print("   • نظام تقارير متقدم ومتكامل")
    print("   • نظام نسخ احتياطي ذكي")
    print("   • إدارة شاملة للعملاء والمخزون")
    print("   • أنماط وأيقونات عصرية")
    print()


def show_system_info():
    """عرض معلومات النظام"""
    print("💻 معلومات النظام:")
    print(f"   نظام التشغيل: {os.name}")
    print(f"   Python: {sys.version}")
    print(f"   مجلد العمل: {os.getcwd()}")
    print()


def main():
    """الدالة الرئيسية"""
    show_welcome_message()
    show_system_info()

    # فحص متطلبات النظام
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return

    # فحص المكتبات المطلوبة
    if not check_required_packages():
        print("\n🔧 محاولة تثبيت المكتبات المفقودة...")

        install_choice = input("هل تريد محاولة تثبيت المكتبات المفقودة؟ (y/n/s للإصدار المبسط): ").lower()
        if install_choice in ['y', 'yes', 'نعم']:
            if install_missing_packages():
                print("✅ تم تثبيت المكتبات بنجاح!")
                # إعادة فحص المكتبات
                if not check_required_packages():
                    print("❌ لا تزال هناك مكتبات مفقودة")
                    simple_choice = input("هل تريد تشغيل الإصدار المبسط؟ (y/n): ").lower()
                    if simple_choice in ['y', 'yes', 'نعم']:
                        run_simple_version()
                        return
                    else:
                        input("اضغط Enter للخروج...")
                        return
            else:
                print("❌ فشل في تثبيت المكتبات")
                simple_choice = input("هل تريد تشغيل الإصدار المبسط؟ (y/n): ").lower()
                if simple_choice in ['y', 'yes', 'نعم']:
                    run_simple_version()
                    return
                else:
                    input("اضغط Enter للخروج...")
                    return
        elif install_choice in ['s', 'simple', 'مبسط']:
            run_simple_version()
            return
        else:
            print("❌ لا يمكن تشغيل التطبيق بدون المكتبات المطلوبة")
            simple_choice = input("هل تريد تشغيل الإصدار المبسط؟ (y/n): ").lower()
            if simple_choice in ['y', 'yes', 'نعم']:
                run_simple_version()
                return
            else:
                input("اضغط Enter للخروج...")
                return

    # إنشاء المجلدات المطلوبة
    create_directories()

    # فحص قواعد البيانات
    check_database_files()

    # تشغيل التطبيق
    try:
        run_application()
    except KeyboardInterrupt:
        print("\n\n⏹️  تم إيقاف التطبيق بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        traceback.print_exc()
        input("اضغط Enter للخروج...")


if __name__ == "__main__":
    main()
