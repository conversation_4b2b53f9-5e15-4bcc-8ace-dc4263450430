#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل آمن لتطبيق مصمم الأثاث الاحترافي مع فحص المتطلبات
Safe Launcher for Professional Furniture Designer with Requirements Check
"""

import sys
import os
import subprocess
from pathlib import Path

# إضافة المجلد الحالي لمسار Python
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def check_python_version():
    """فحص إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        print(f"   الإصدار الحالي: {sys.version}")
        return False
    print(f"✅ إصدار Python مناسب: {sys.version_info.major}.{sys.version_info.minor}")
    return True

def check_package(package_name, import_name=None):
    """فحص توفر مكتبة معينة"""
    if import_name is None:
        import_name = package_name
    
    try:
        __import__(import_name)
        return True
    except ImportError:
        return False

def check_core_requirements():
    """فحص المتطلبات الأساسية"""
    print("\n🔍 فحص المتطلبات الأساسية...")
    
    core_packages = [
        ("PySide6", "PySide6.QtWidgets", "إطار العمل الأساسي"),
        ("numpy", "numpy", "الحوسبة العلمية"),
        ("pandas", "pandas", "معالجة البيانات"),
        ("matplotlib", "matplotlib", "الرسوم البيانية"),
        ("PIL", "PIL", "معالجة الصور"),
        ("trimesh", "trimesh", "النماذج ثلاثية الأبعاد"),
        ("openpyxl", "openpyxl", "تصدير Excel")
    ]
    
    missing_packages = []
    available_packages = []
    
    for package_name, import_name, description in core_packages:
        if check_package(package_name, import_name):
            print(f"✅ {package_name}: متاح")
            available_packages.append(package_name)
        else:
            print(f"❌ {package_name}: غير متاح - {description}")
            missing_packages.append(package_name)
    
    return missing_packages, available_packages

def check_optional_requirements():
    """فحص المتطلبات الاختيارية"""
    print("\n🔍 فحص المتطلبات الاختيارية...")
    
    optional_packages = [
        ("scipy", "scipy", "حوسبة علمية متقدمة"),
        ("seaborn", "seaborn", "رسوم بيانية متقدمة"),
        ("reportlab", "reportlab", "إنشاء PDF"),
        ("arabic_reshaper", "arabic_reshaper", "دعم العربية في PDF"),
        ("bidi", "bidi", "ترتيب النصوص"),
        ("serial", "serial", "تواصل CNC"),
        ("psutil", "psutil", "معلومات النظام"),
        ("qrcode", "qrcode", "رموز QR")
    ]
    
    available_optional = []
    missing_optional = []
    
    for package_name, import_name, description in optional_packages:
        if check_package(package_name, import_name):
            print(f"✅ {package_name}: متاح")
            available_optional.append(package_name)
        else:
            print(f"⚠️ {package_name}: غير متاح - {description}")
            missing_optional.append(package_name)
    
    return missing_optional, available_optional

def offer_installation(missing_packages):
    """عرض تثبيت المكتبات المفقودة"""
    if not missing_packages:
        return True
    
    print(f"\n📦 المكتبات المفقودة ({len(missing_packages)}):")
    for package in missing_packages:
        print(f"   • {package}")
    
    print("\n🤔 هل تريد تثبيت المكتبات المفقودة؟")
    print("   خيارات التثبيت:")
    print("   1. تثبيت تلقائي (موصى به)")
    print("   2. تثبيت يدوي")
    print("   3. تخطي والمتابعة")
    print("   4. إنهاء")
    
    try:
        choice = input("\n   اختر رقم الخيار [1]: ").strip()
        if not choice:
            choice = "1"
    except KeyboardInterrupt:
        print("\n\n⏹️ تم الإلغاء")
        return False
    
    if choice == "1":
        print("\n🔄 بدء التثبيت التلقائي...")
        try:
            result = subprocess.run([sys.executable, "install_requirements.py"], 
                                  check=True, capture_output=False)
            return True
        except subprocess.CalledProcessError:
            print("❌ فشل التثبيت التلقائي")
            return False
        except FileNotFoundError:
            print("❌ ملف التثبيت غير موجود")
            return False
    
    elif choice == "2":
        print("\n📝 للتثبيت اليدوي، استخدم:")
        print("   pip install -r requirements_core.txt")
        print("   أو")
        print("   python install_requirements.py")
        return False
    
    elif choice == "3":
        print("\n⚠️ المتابعة بدون تثبيت المكتبات المفقودة")
        print("   بعض الميزات قد لا تعمل بشكل صحيح")
        return True
    
    else:
        print("\n👋 تم الإنهاء")
        return False

def test_basic_functionality():
    """اختبار الوظائف الأساسية"""
    print("\n🧪 اختبار الوظائف الأساسية...")
    
    try:
        # اختبار PySide6
        from PySide6.QtWidgets import QApplication
        print("✅ PySide6: يعمل بشكل صحيح")
        
        # اختبار منسق العملة
        from ui.modern_styles import CurrencyFormatter
        test_amount = 1234.56
        formatted = CurrencyFormatter.format_currency(test_amount)
        print(f"✅ منسق العملة: {test_amount} → {formatted}")
        
        # اختبار استيراد الوحدات الرئيسية
        modules_to_test = [
            "models.advanced_3d_viewer",
            "clients.advanced_client_system", 
            "inventory.advanced_inventory_system",
            "ui.modern_styles",
            "ui.icons_manager"
        ]
        
        for module in modules_to_test:
            try:
                __import__(module)
                print(f"✅ {module}: متاح")
            except ImportError as e:
                print(f"⚠️ {module}: مشكلة في التحميل - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية للمشغل الآمن"""
    print("🛡️ المشغل الآمن - مصمم الأثاث الاحترافي المتقدم")
    print("=" * 60)
    
    # فحص إصدار Python
    if not check_python_version():
        return False
    
    # فحص المتطلبات الأساسية
    missing_core, available_core = check_core_requirements()
    
    # فحص المتطلبات الاختيارية
    missing_optional, available_optional = check_optional_requirements()
    
    # تقييم الوضع
    print(f"\n📊 ملخص الفحص:")
    print(f"✅ المكتبات الأساسية المتاحة: {len(available_core)}")
    print(f"❌ المكتبات الأساسية المفقودة: {len(missing_core)}")
    print(f"✅ المكتبات الاختيارية المتاحة: {len(available_optional)}")
    print(f"⚠️ المكتبات الاختيارية المفقودة: {len(missing_optional)}")
    
    # تحديد إمكانية التشغيل
    if len(missing_core) == 0:
        print("\n🎉 جميع المتطلبات الأساسية متوفرة!")
        can_run = True
    elif len(missing_core) <= 2:
        print("\n⚠️ معظم المتطلبات متوفرة، قد يعمل التطبيق")
        can_run = True
    else:
        print("\n❌ العديد من المتطلبات الأساسية مفقودة")
        can_run = False
    
    # عرض التثبيت إذا لزم الأمر
    if missing_core:
        if not offer_installation(missing_core):
            return False
    
    # اختبار الوظائف الأساسية
    if can_run:
        if not test_basic_functionality():
            print("\n⚠️ فشل في اختبار الوظائف الأساسية")
            print("   قد تواجه مشاكل عند التشغيل")
    
    # تشغيل التطبيق
    if can_run:
        print("\n🚀 بدء تشغيل التطبيق...")
        try:
            # استيراد وتشغيل التطبيق الرئيسي
            import run_advanced_furniture_designer
            return True
        except Exception as e:
            print(f"❌ خطأ في تشغيل التطبيق: {e}")
            print("\n💡 جرب:")
            print("   1. python run_migration_test.py (للاختبار)")
            print("   2. python install_requirements.py (لإعادة التثبيت)")
            return False
    else:
        print("\n❌ لا يمكن تشغيل التطبيق بسبب المتطلبات المفقودة")
        print("\n💡 للمساعدة:")
        print("   1. python install_requirements.py")
        print("   2. pip install -r requirements_core.txt")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            input("\n⏸️ اضغط Enter للخروج...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️ تم الإلغاء بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("\n⏸️ اضغط Enter للخروج...")
        sys.exit(1)
