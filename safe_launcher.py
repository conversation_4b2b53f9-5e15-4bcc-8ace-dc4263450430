#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل آمن للتطبيق - يتعامل مع الأخطاء بذكاء
Safe Application Launcher - Handles Errors Intelligently
"""

import sys
import os
from pathlib import Path

# إضافة المجلد الحالي لمسار Python
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def safe_import_and_run():
    """استيراد وتشغيل آمن للتطبيق"""
    try:
        from PySide6.QtWidgets import QApplication, QMessageBox
        from PySide6.QtCore import Qt
        
        app = QApplication(sys.argv)
        app.setApplicationName("مصمم الأثاث الاحترافي الآمن")
        
        print("🚀 بدء التشغيل الآمن...")
        
        # محاولة تشغيل التطبيق المبسط أولاً
        try:
            from simple_main_app import SimpleFurnitureDesignerApp
            window = SimpleFurnitureDesignerApp()
            window.show()
            
            print("✅ تم تشغيل التطبيق المبسط بنجاح")
            return app.exec()
            
        except Exception as e:
            print(f"⚠️ فشل التطبيق المبسط: {e}")
            
            # محاولة تشغيل التطبيق الأصلي
            try:
                import main_application
                return main_application.main()
                
            except Exception as e2:
                print(f"⚠️ فشل التطبيق الأصلي: {e2}")
                
                # عرض رسالة خطأ ودية
                QMessageBox.critical(
                    None,
                    "خطأ في التشغيل",
                    f"تعذر تشغيل التطبيق:\n\n"
                    f"خطأ التطبيق المبسط: {e}\n"
                    f"خطأ التطبيق الأصلي: {e2}\n\n"
                    f"يرجى التحقق من:\n"
                    f"• تثبيت PySide6\n"
                    f"• سلامة ملفات التطبيق\n"
                    f"• صلاحيات الملفات"
                )
                return 1
    
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 يرجى تثبيت PySide6:")
        print("   pip install PySide6")
        return 1
    
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        return 1

def main():
    """الدالة الرئيسية"""
    print("🪑 مصمم الأثاث الاحترافي - المشغل الآمن")
    print("=" * 50)
    
    try:
        return safe_import_and_run()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        return 0
    except Exception as e:
        print(f"❌ خطأ فادح: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
