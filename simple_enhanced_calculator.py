#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حاسبة التكلفة المحسنة المبسطة
Simplified Enhanced Cost Calculator
"""

import sys
import os
from pathlib import Path

# إضافة المجلد الحالي لمسار Python
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QLineEdit, QDoubleSpinBox, QComboBox,
    QTabWidget, QFormLayout, QGroupBox, QTextEdit, QMessageBox,
    QTableWidget, QTableWidgetItem, QHeaderView
)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QFont

try:
    from ui.modern_styles import ModernStyleManager
    from ui.icons_manager import IconsManager
except ImportError:
    class ModernStyleManager:
        COLORS = {
            'primary': '#2C3E50',
            'secondary': '#3498DB',
            'success': '#27AE60',
            'warning': '#F39C12',
            'danger': '#E74C3C',
            'background': '#ECF0F1',
            'card': '#FFFFFF',
            'text': '#2C3E50',
            'text_light': '#FFFFFF'
        }

        FONT_SIZES = {
            'small': 10,
            'normal': 12,
            'heading': 14,
            'title': 16
        }

    class IconsManager:
        @staticmethod
        def get_action_icon(name):
            from PySide6.QtGui import QIcon
            return QIcon()


class SimplifiedEnhancedCalculator(QMainWindow):
    """حاسبة التكلفة المحسنة المبسطة"""

    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_data()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("🧮 حاسبة التكلفة المحسنة - إصدار مبسط")
        self.setMinimumSize(1000, 700)

        # تطبيق الأنماط
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: {ModernStyleManager.COLORS['background']};
            }}
            QTabWidget::pane {{
                border: 1px solid {ModernStyleManager.COLORS['primary']};
                background-color: {ModernStyleManager.COLORS['card']};
            }}
            QTabBar::tab {{
                background-color: {ModernStyleManager.COLORS['secondary']};
                color: white;
                padding: 8px 16px;
                margin-right: 2px;
            }}
            QTabBar::tab:selected {{
                background-color: {ModernStyleManager.COLORS['primary']};
            }}
        """)

        # إنشاء الودجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)

        # العنوان
        title_label = QLabel("🧮 حاسبة التكلفة المتقدمة")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                font-size: {ModernStyleManager.FONT_SIZES['title']}px;
                font-weight: bold;
                color: {ModernStyleManager.COLORS['primary']};
                padding: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {ModernStyleManager.COLORS['primary']},
                    stop:1 {ModernStyleManager.COLORS['secondary']});
                color: white;
                border-radius: 10px;
                margin-bottom: 10px;
            }}
        """)

        # التبويبات
        self.tabs = QTabWidget()

        # تبويب معلومات المشروع
        self.create_project_tab()

        # تبويب المواد
        self.create_materials_tab()

        # تبويب العمالة
        self.create_labor_tab()

        # تبويب النتائج
        self.create_results_tab()

        # أزرار التحكم
        buttons_layout = QHBoxLayout()

        calculate_btn = QPushButton("🧮 احسب التكلفة")
        calculate_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {ModernStyleManager.COLORS['success']};
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {ModernStyleManager.COLORS['primary']};
            }}
        """)
        calculate_btn.clicked.connect(self.calculate_costs)

        clear_btn = QPushButton("🗑️ مسح الكل")
        clear_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: #E74C3C;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: #C0392B;
            }}
        """)
        clear_btn.clicked.connect(self.clear_all)

        buttons_layout.addWidget(calculate_btn)
        buttons_layout.addWidget(clear_btn)
        buttons_layout.addStretch()

        main_layout.addWidget(title_label)
        main_layout.addWidget(self.tabs)
        main_layout.addLayout(buttons_layout)

        # شريط الحالة
        self.statusBar().showMessage("🚀 مرحباً بك في حاسبة التكلفة المحسنة")

        # توسيط النافذة
        self.center_window()

    def create_project_tab(self):
        """إنشاء تبويب معلومات المشروع"""
        tab = QWidget()
        layout = QFormLayout(tab)

        # معلومات المشروع
        self.project_name_edit = QLineEdit()
        self.project_name_edit.setPlaceholderText("أدخل اسم المشروع")
        layout.addRow("اسم المشروع:", self.project_name_edit)

        self.client_name_edit = QLineEdit()
        self.client_name_edit.setPlaceholderText("أدخل اسم العميل")
        layout.addRow("اسم العميل:", self.client_name_edit)

        self.client_type_combo = QComboBox()
        self.client_type_combo.addItems(["عميل جديد", "عميل عادي", "عميل مميز (VIP)", "عميل بالجملة"])
        layout.addRow("نوع العميل:", self.client_type_combo)

        self.project_size_combo = QComboBox()
        self.project_size_combo.addItems(["مشروع صغير", "مشروع متوسط", "مشروع كبير", "مشروع مؤسسي"])
        layout.addRow("حجم المشروع:", self.project_size_combo)

        self.budget_limit_spin = QDoubleSpinBox()
        self.budget_limit_spin.setRange(0, 999999999)
        self.budget_limit_spin.setSuffix(" د.ل")
        layout.addRow("الميزانية المحددة:", self.budget_limit_spin)

        self.tabs.addTab(tab, "📋 معلومات المشروع")

    def create_materials_tab(self):
        """إنشاء تبويب المواد"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # أزرار التحكم
        controls_layout = QHBoxLayout()

        add_material_btn = QPushButton("➕ إضافة مادة")
        add_material_btn.clicked.connect(self.add_material)

        controls_layout.addWidget(add_material_btn)
        controls_layout.addStretch()

        # جدول المواد
        self.materials_table = QTableWidget()
        self.materials_table.setColumnCount(6)
        self.materials_table.setHorizontalHeaderLabels([
            "المادة", "النوع", "الكمية", "الوحدة", "سعر الوحدة", "الإجمالي"
        ])

        header = self.materials_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)

        layout.addLayout(controls_layout)
        layout.addWidget(self.materials_table)

        self.tabs.addTab(tab, "🔨 المواد")

    def create_labor_tab(self):
        """إنشاء تبويب العمالة"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # أزرار التحكم
        controls_layout = QHBoxLayout()

        add_labor_btn = QPushButton("➕ إضافة مهمة")
        add_labor_btn.clicked.connect(self.add_labor)

        controls_layout.addWidget(add_labor_btn)
        controls_layout.addStretch()

        # جدول العمالة
        self.labor_table = QTableWidget()
        self.labor_table.setColumnCount(5)
        self.labor_table.setHorizontalHeaderLabels([
            "المهمة", "مستوى المهارة", "الساعات", "أجر الساعة", "الإجمالي"
        ])

        header = self.labor_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.Stretch)

        layout.addLayout(controls_layout)
        layout.addWidget(self.labor_table)

        self.tabs.addTab(tab, "👷 العمالة")

    def create_results_tab(self):
        """إنشاء تبويب النتائج"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # ملخص التكاليف
        summary_group = QGroupBox("💰 ملخص التكاليف")
        summary_layout = QFormLayout(summary_group)

        self.materials_total_label = QLabel("0.00 د.ل")
        self.materials_total_label.setStyleSheet("font-weight: bold; color: #2C3E50;")
        summary_layout.addRow("إجمالي المواد:", self.materials_total_label)

        self.labor_total_label = QLabel("0.00 د.ل")
        self.labor_total_label.setStyleSheet("font-weight: bold; color: #2C3E50;")
        summary_layout.addRow("إجمالي العمالة:", self.labor_total_label)

        self.overheads_label = QLabel("0.00 د.ل")
        self.overheads_label.setStyleSheet("font-weight: bold; color: #E67E22;")
        summary_layout.addRow("التكاليف العامة (15%):", self.overheads_label)

        self.profit_label = QLabel("0.00 د.ل")
        self.profit_label.setStyleSheet("font-weight: bold; color: #27AE60;")
        summary_layout.addRow("الربح (25%):", self.profit_label)

        self.discount_label = QLabel("0.00 د.ل")
        self.discount_label.setStyleSheet("font-weight: bold; color: #8E44AD;")
        summary_layout.addRow("الخصم:", self.discount_label)

        self.final_total_label = QLabel("0.00 د.ل")
        self.final_total_label.setStyleSheet(f"""
            font-size: {ModernStyleManager.FONT_SIZES['heading']}px;
            font-weight: bold;
            color: {ModernStyleManager.COLORS['primary']};
            padding: 8px;
            background-color: {ModernStyleManager.COLORS['card']};
            border: 2px solid {ModernStyleManager.COLORS['primary']};
            border-radius: 6px;
        """)
        summary_layout.addRow("الإجمالي النهائي:", self.final_total_label)

        layout.addWidget(summary_group)
        layout.addStretch()

        self.tabs.addTab(tab, "📊 النتائج")

    def setup_data(self):
        """إعداد البيانات الأولية"""
        self.materials_data = []
        self.labor_data = []

    def add_material(self):
        """إضافة مادة جديدة"""
        # إضافة صف جديد للجدول
        row = self.materials_table.rowCount()
        self.materials_table.insertRow(row)

        # إضافة عناصر افتراضية
        self.materials_table.setItem(row, 0, QTableWidgetItem("مادة جديدة"))
        self.materials_table.setItem(row, 1, QTableWidgetItem("خشب"))
        self.materials_table.setItem(row, 2, QTableWidgetItem("1.0"))
        self.materials_table.setItem(row, 3, QTableWidgetItem("متر"))
        self.materials_table.setItem(row, 4, QTableWidgetItem("100.0"))
        self.materials_table.setItem(row, 5, QTableWidgetItem("100.0"))

        self.statusBar().showMessage("تم إضافة مادة جديدة")

    def add_labor(self):
        """إضافة مهمة عمالة جديدة"""
        # إضافة صف جديد للجدول
        row = self.labor_table.rowCount()
        self.labor_table.insertRow(row)

        # إضافة عناصر افتراضية
        self.labor_table.setItem(row, 0, QTableWidgetItem("مهمة جديدة"))
        self.labor_table.setItem(row, 1, QTableWidgetItem("متوسط"))
        self.labor_table.setItem(row, 2, QTableWidgetItem("8.0"))
        self.labor_table.setItem(row, 3, QTableWidgetItem("20.0"))
        self.labor_table.setItem(row, 4, QTableWidgetItem("160.0"))

        self.statusBar().showMessage("تم إضافة مهمة عمالة جديدة")

    def calculate_costs(self):
        """حساب التكاليف"""
        try:
            # حساب إجمالي المواد
            materials_total = 0.0
            for row in range(self.materials_table.rowCount()):
                quantity_item = self.materials_table.item(row, 2)
                price_item = self.materials_table.item(row, 4)

                if quantity_item and price_item:
                    quantity = float(quantity_item.text())
                    price = float(price_item.text())
                    total = quantity * price * 1.15  # مع 15% هدر
                    materials_total += total

                    # تحديث الإجمالي في الجدول
                    self.materials_table.setItem(row, 5, QTableWidgetItem(f"{total:.2f}"))

            # حساب إجمالي العمالة
            labor_total = 0.0
            for row in range(self.labor_table.rowCount()):
                hours_item = self.labor_table.item(row, 2)
                rate_item = self.labor_table.item(row, 3)
                skill_item = self.labor_table.item(row, 1)

                if hours_item and rate_item and skill_item:
                    hours = float(hours_item.text())
                    rate = float(rate_item.text())

                    # معامل المهارة
                    skill_multipliers = {
                        "مبتدئ": 0.8,
                        "متوسط": 1.0,
                        "خبير": 1.3,
                        "أستاذ": 1.6
                    }
                    skill_multiplier = skill_multipliers.get(skill_item.text(), 1.0)

                    total = hours * rate * skill_multiplier
                    labor_total += total

                    # تحديث الإجمالي في الجدول
                    self.labor_table.setItem(row, 4, QTableWidgetItem(f"{total:.2f}"))

            # حساب التكاليف الإضافية
            subtotal = materials_total + labor_total
            overheads = subtotal * 0.15  # 15% تكاليف عامة
            profit = (subtotal + overheads) * 0.25  # 25% ربح

            # حساب الخصم حسب نوع العميل
            client_discounts = {
                "عميل جديد": 0.0,
                "عميل عادي": 0.05,
                "عميل مميز (VIP)": 0.10,
                "عميل بالجملة": 0.15
            }

            client_type = self.client_type_combo.currentText()
            discount_rate = client_discounts.get(client_type, 0.0)

            total_before_discount = subtotal + overheads + profit
            discount_amount = total_before_discount * discount_rate
            final_total = total_before_discount - discount_amount

            # تحديث التسميات
            self.materials_total_label.setText(f"{materials_total:.2f} د.ل")
            self.labor_total_label.setText(f"{labor_total:.2f} د.ل")
            self.overheads_label.setText(f"{overheads:.2f} د.ل")
            self.profit_label.setText(f"{profit:.2f} د.ل")
            self.discount_label.setText(f"{discount_amount:.2f} د.ل ({discount_rate*100:.0f}%)")
            self.final_total_label.setText(f"{final_total:.2f} د.ل")

            # فحص تجاوز الميزانية
            budget_limit = self.budget_limit_spin.value()
            if budget_limit > 0 and final_total > budget_limit:
                variance = final_total - budget_limit
                self.statusBar().showMessage(f"⚠️ تجاوز الميزانية بمقدار: {variance:.2f} د.ل")
            else:
                self.statusBar().showMessage("✅ تم حساب التكاليف بنجاح")

            # الانتقال لتبويب النتائج
            self.tabs.setCurrentIndex(3)

        except Exception as e:
            QMessageBox.warning(self, "خطأ في الحساب", f"حدث خطأ أثناء حساب التكاليف:\n{str(e)}")

    def clear_all(self):
        """مسح جميع البيانات"""
        reply = QMessageBox.question(
            self, "مسح الكل",
            "هل تريد مسح جميع البيانات؟\nلا يمكن التراجع عن هذا الإجراء.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # مسح الحقول
            self.project_name_edit.clear()
            self.client_name_edit.clear()
            self.client_type_combo.setCurrentIndex(0)
            self.project_size_combo.setCurrentIndex(0)
            self.budget_limit_spin.setValue(0)

            # مسح الجداول
            self.materials_table.setRowCount(0)
            self.labor_table.setRowCount(0)

            # مسح النتائج
            self.materials_total_label.setText("0.00 د.ل")
            self.labor_total_label.setText("0.00 د.ل")
            self.overheads_label.setText("0.00 د.ل")
            self.profit_label.setText("0.00 د.ل")
            self.discount_label.setText("0.00 د.ل")
            self.final_total_label.setText("0.00 د.ل")

            self.statusBar().showMessage("تم مسح جميع البيانات")

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)


def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)

    app.setApplicationName("حاسبة التكلفة المحسنة")
    app.setApplicationVersion("2.0")

    try:
        window = SimplifiedEnhancedCalculator()
        window.show()

        print("🚀 تم تشغيل حاسبة التكلفة المحسنة المبسطة بنجاح!")
        print("🧮 جرب الميزات الجديدة:")
        print("   • أضف مواد مع حساب الهدر التلقائي")
        print("   • أضف مهام عمالة مع مستويات مهارة")
        print("   • احسب التكاليف مع خصومات العملاء")
        print("   • راقب تجاوز الميزانية")

        sys.exit(app.exec())

    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
