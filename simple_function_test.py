#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للدوال المضافة
Simple Test for Added Functions
"""

import sys
import inspect

def test_function_exists(module_name, class_name, function_names):
    """اختبار وجود الدوال في فئة معينة"""
    try:
        module = __import__(module_name, fromlist=[class_name])
        cls = getattr(module, class_name)
        
        print(f"\n📦 اختبار {class_name} في {module_name}:")
        
        missing_functions = []
        for func_name in function_names:
            if hasattr(cls, func_name):
                # التحقق من أن الدالة قابلة للاستدعاء
                func = getattr(cls, func_name)
                if callable(func):
                    print(f"   ✅ {func_name}: موجود وقابل للاستدعاء")
                else:
                    print(f"   ⚠️ {func_name}: موجود لكن غير قابل للاستدعاء")
                    missing_functions.append(func_name)
            else:
                print(f"   ❌ {func_name}: مفقود")
                missing_functions.append(func_name)
        
        success_rate = ((len(function_names) - len(missing_functions)) / len(function_names)) * 100
        print(f"   📊 معدل النجاح: {success_rate:.1f}%")
        
        return len(missing_functions) == 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار {module_name}.{class_name}: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار بسيط للدوال المضافة")
    print("=" * 40)
    
    # قائمة الاختبارات
    tests = [
        {
            'module': 'reports.advanced_reports_system',
            'class': 'AdvancedReportsWidget',
            'functions': [
                'create_reports_panel',
                'generate_financial_report',
                'generate_clients_report',
                'generate_inventory_report',
                'generate_profit_loss_report',
                'export_current_report'
            ]
        },
        {
            'module': 'cnc.advanced_cnc_system',
            'class': 'AdvancedCNCManagerWidget',
            'functions': [
                'add_new_machine',
                'load_gcode_file',
                'preview_gcode',
                'start_cnc_job',
                'monitor_cnc_status'
            ]
        },
        {
            'module': 'clients.advanced_client_system',
            'class': 'AdvancedClientManagerWidget',
            'functions': [
                'filter_clients',
                'apply_filters',
                'refresh_clients_list',
                'export_clients_data',
                'import_clients_data',
                'generate_client_report'
            ]
        },
        {
            'module': 'inventory.advanced_inventory_system',
            'class': 'AdvancedInventoryManagerWidget',
            'functions': [
                'filter_inventory',
                'apply_filters',
                'add_new_item',
                'edit_selected_item',
                'delete_selected_item',
                'add_stock_movement',
                'refresh_data',
                'export_inventory'
            ]
        }
    ]
    
    # تشغيل الاختبارات
    results = []
    for test in tests:
        result = test_function_exists(
            test['module'],
            test['class'],
            test['functions']
        )
        results.append((test['class'], result))
    
    # تقييم النتائج
    print("\n" + "=" * 40)
    print("📊 ملخص النتائج:")
    
    passed = 0
    total = len(results)
    
    for class_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {class_name}: {status}")
        if result:
            passed += 1
    
    success_rate = (passed / total) * 100
    print(f"\n🎯 معدل النجاح الإجمالي: {passed}/{total} ({success_rate:.1f}%)")
    
    # اختبار العملة الليبية
    print("\n💰 اختبار العملة الليبية:")
    try:
        from ui.modern_styles import CurrencyFormatter
        
        test_amounts = [1234.56, 50000, 1000000]
        for amount in test_amounts:
            formatted = CurrencyFormatter.format_currency(amount)
            short_format = CurrencyFormatter.format_currency_short(amount)
            print(f"   {amount} → {formatted} | {short_format}")
        
        print("✅ العملة الليبية تعمل بشكل صحيح")
        currency_success = True
    except Exception as e:
        print(f"❌ خطأ في العملة الليبية: {e}")
        currency_success = False
    
    # النتيجة النهائية
    overall_success = success_rate >= 75 and currency_success
    
    print("\n" + "=" * 40)
    if overall_success:
        print("🎉 ممتاز! جميع الدوال المطلوبة متاحة")
        print("✅ التطبيق جاهز للتشغيل")
        print("\n🚀 يمكنك الآن تشغيل:")
        print("   python run_advanced_furniture_designer.py")
        print("   أو")
        print("   python simple_main_app.py")
    else:
        print("⚠️ بعض الدوال لا تزال مفقودة")
        print("💡 لكن التطبيق قد يعمل مع قيود")
        print("\n🔧 جرب:")
        print("   python simple_main_app.py")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
