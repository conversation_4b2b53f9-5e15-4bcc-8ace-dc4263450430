#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التطبيق الرئيسي المبسط - مصمم الأثاث الاحترافي
Simplified Main Application - Professional Furniture Designer
"""

import sys
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QTabWidget, QWidget, QVBoxLayout,
    QLabel, QMessageBox, QPushButton, QHBoxLayout
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

# استيراد الوحدات الأساسية
try:
    from models.advanced_3d_viewer import Advanced3DViewer
except ImportError:
    Advanced3DViewer = None

try:
    from clients.advanced_client_system import AdvancedClientManagerWidget
except ImportError:
    AdvancedClientManagerWidget = None

try:
    from inventory.advanced_inventory_system import AdvancedInventoryManagerWidget
except ImportError:
    AdvancedInventoryManagerWidget = None

try:
    from cnc.advanced_cnc_system import AdvancedCNCManagerWidget
except ImportError:
    AdvancedCNCManagerWidget = None

try:
    from reports.advanced_reports_system import AdvancedReportsWidget
except ImportError:
    AdvancedReportsWidget = None

try:
    from fallback_widget import FallbackWidget
except ImportError:
    FallbackWidget = None

try:
    from ui.modern_styles import CurrencyFormatter
except ImportError:
    CurrencyFormatter = None


class SimpleFurnitureDesignerApp(QMainWindow):
    """تطبيق مصمم الأثاث المبسط"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🪑 مصمم الأثاث الاحترافي المتقدم - الإصدار 2.1")
        self.setGeometry(100, 100, 1200, 800)
        self.init_ui()
        self.center_on_screen()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # عنوان التطبيق
        title_label = QLabel("🪑 مصمم الأثاث الاحترافي المتقدم")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #2C3E50; margin: 10px; padding: 10px;")
        
        # معلومات الإصدار
        version_label = QLabel("الإصدار 2.1 - PySide6 + دعم الدينار الليبي (د.ل)")
        version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        version_label.setStyleSheet("color: #7F8C8D; margin-bottom: 20px;")
        
        # التبويبات
        self.tab_widget = QTabWidget()
        
        # إضافة التبويبات
        self.add_tabs()
        
        layout.addWidget(title_label)
        layout.addWidget(version_label)
        layout.addWidget(self.tab_widget)
        
        # شريط الحالة
        self.statusBar().showMessage("جاهز - مرحباً بك في مصمم الأثاث الاحترافي")
    
    def add_tabs(self):
        """إضافة التبويبات"""
        tabs_config = [
            ("المعاين ثلاثي الأبعاد", Advanced3DViewer, "معاين ثلاثي الأبعاد متقدم"),
            ("إدارة العملاء", AdvancedClientManagerWidget, "نظام إدارة العملاء"),
            ("إدارة المخزون", AdvancedInventoryManagerWidget, "نظام إدارة المخزون"),
            ("إدارة CNC", AdvancedCNCManagerWidget, "نظام إدارة ماكينات CNC"),
            ("التقارير", AdvancedReportsWidget, "نظام التقارير المتقدم")
        ]
        
        for tab_name, widget_class, description in tabs_config:
            try:
                if widget_class:
                    widget = widget_class()
                    self.tab_widget.addTab(widget, tab_name)
                    print(f"✅ تم تحميل {tab_name}")
                else:
                    raise ImportError(f"فئة {tab_name} غير متاحة")
                    
            except Exception as e:
                print(f"⚠️ تعذر تحميل {tab_name}: {e}")
                
                # إنشاء واجهة احتياطية
                if FallbackWidget:
                    fallback = FallbackWidget(tab_name)
                else:
                    fallback = self.create_simple_placeholder(tab_name, description)
                
                self.tab_widget.addTab(fallback, tab_name)
        
        # إضافة تبويب اختبار العملة
        if CurrencyFormatter:
            currency_test_widget = self.create_currency_test_widget()
            self.tab_widget.addTab(currency_test_widget, "اختبار العملة")
    
    def create_simple_placeholder(self, name: str, description: str):
        """إنشاء واجهة بديلة بسيطة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # رسالة الحالة
        status_label = QLabel(f"🚧 {name} قيد التطوير")
        status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        status_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        status_label.setStyleSheet("""
            QLabel {
                color: #E67E22;
                padding: 30px;
                border: 2px dashed #E67E22;
                border-radius: 10px;
                background-color: #FEF9E7;
            }
        """)
        
        # وصف الوحدة
        desc_label = QLabel(description)
        desc_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        desc_label.setStyleSheet("color: #7F8C8D; margin: 10px;")
        
        # زر المعلومات
        info_btn = QPushButton("معلومات الوحدة")
        info_btn.clicked.connect(lambda: self.show_module_info(name, description))
        
        layout.addStretch()
        layout.addWidget(status_label)
        layout.addWidget(desc_label)
        layout.addWidget(info_btn)
        layout.addStretch()
        
        return widget
    
    def create_currency_test_widget(self):
        """إنشاء واجهة اختبار العملة"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # عنوان
        title_label = QLabel("🪙 اختبار تنسيق الدينار الليبي")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setStyleSheet("color: #27AE60; margin: 20px;")
        
        # أمثلة على التنسيق
        examples = [
            (1234.56, "مبلغ عادي"),
            (1000000, "مليون دينار"),
            (5000, "خمسة آلاف دينار"),
            (100, "مائة دينار"),
            (0.50, "نصف دينار")
        ]
        
        for amount, description in examples:
            example_widget = QWidget()
            example_layout = QHBoxLayout(example_widget)
            
            desc_label = QLabel(f"{description}:")
            desc_label.setMinimumWidth(150)
            
            formatted = CurrencyFormatter.format_currency(amount)
            short_format = CurrencyFormatter.format_currency_short(amount)
            
            formatted_label = QLabel(f"{formatted} | {short_format}")
            formatted_label.setStyleSheet("font-weight: bold; color: #2C3E50;")
            
            example_layout.addWidget(desc_label)
            example_layout.addWidget(formatted_label)
            example_layout.addStretch()
            
            layout.addWidget(example_widget)
        
        layout.addWidget(title_label)
        layout.addStretch()
        
        return widget
    
    def show_module_info(self, name: str, description: str):
        """عرض معلومات الوحدة"""
        QMessageBox.information(
            self,
            f"معلومات {name}",
            f"{description}\n\n"
            "هذه الوحدة قيد التطوير حالياً.\n"
            "سيتم إضافة الميزات الكاملة في التحديثات القادمة.\n\n"
            "الميزات المتاحة حالياً:\n"
            "• دعم الدينار الليبي (د.ل)\n"
            "• واجهة مستخدم حديثة\n"
            "• تكامل مع النظام الرئيسي"
        )
    
    def center_on_screen(self):
        """توسيط النافذة على الشاشة"""
        screen = QApplication.primaryScreen().geometry()
        x = (screen.width() - self.width()) // 2
        y = (screen.height() - self.height()) // 2
        self.move(x, y)


def main():
    """الدالة الرئيسية"""
    app = QApplication(sys.argv)
    app.setApplicationName("مصمم الأثاث الاحترافي المتقدم")
    app.setApplicationVersion("2.1")
    app.setOrganizationName("Libyan Furniture Design Solutions")
    
    # إنشاء النافذة الرئيسية
    window = SimpleFurnitureDesignerApp()
    window.show()
    
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
