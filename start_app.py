#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل مبسط للتطبيق المكتمل
Simple Launcher for Complete Application
"""

import sys
import os
from pathlib import Path

# إضافة المجلد الحالي لمسار Python
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def main():
    """تشغيل التطبيق مباشرة"""
    print("🚀 تشغيل نظام إدارة الأثاث المتقدم...")
    
    try:
        # استيراد وتشغيل التطبيق
        from main_application import main as run_main_app
        run_main_app()
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("تأكد من وجود جميع الملفات المطلوبة")
        input("اضغط Enter للخروج...")
        
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
