#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الميزات المتقدمة لتطبيق مصمم الأثاث الاحترافي
Advanced Features Test for Professional Furniture Designer
"""

import sys
import os
import unittest
import tempfile
from pathlib import Path
from datetime import datetime

# إضافة المجلد الحالي لمسار Python
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

class TestAdvanced3DViewer(unittest.TestCase):
    """اختبار المعاين ثلاثي الأبعاد المحسن"""
    
    def setUp(self):
        """إعداد الاختبار"""
        try:
            from models.advanced_3d_viewer import Advanced3DViewer, LightingSettings, MaterialSettings, ViewSettings
            self.Advanced3DViewer = Advanced3DViewer
            self.LightingSettings = LightingSettings
            self.MaterialSettings = MaterialSettings
            self.ViewSettings = ViewSettings
            self.available = True
        except ImportError as e:
            print(f"⚠️ تعذر استيراد المعاين ثلاثي الأبعاد المحسن: {e}")
            self.available = False
    
    def test_lighting_settings(self):
        """اختبار إعدادات الإضاءة"""
        if not self.available:
            self.skipTest("المعاين ثلاثي الأبعاد غير متاح")
        
        lighting = self.LightingSettings()
        self.assertEqual(lighting.ambient_intensity, 0.3)
        self.assertEqual(lighting.diffuse_intensity, 0.7)
        self.assertEqual(lighting.specular_intensity, 0.5)
        self.assertTrue(lighting.shadow_enabled)
        print("✅ اختبار إعدادات الإضاءة نجح")
    
    def test_material_settings(self):
        """اختبار إعدادات المواد"""
        if not self.available:
            self.skipTest("المعاين ثلاثي الأبعاد غير متاح")
        
        material = self.MaterialSettings()
        self.assertEqual(material.material_type, "خشب")
        self.assertEqual(material.base_color, [0.8, 0.6, 0.4])
        self.assertEqual(material.metallic, 0.0)
        self.assertEqual(material.roughness, 0.8)
        print("✅ اختبار إعدادات المواد نجح")
    
    def test_view_settings(self):
        """اختبار إعدادات العرض"""
        if not self.available:
            self.skipTest("المعاين ثلاثي الأبعاد غير متاح")
        
        view = self.ViewSettings()
        self.assertEqual(view.camera_distance, 5.0)
        self.assertEqual(view.camera_elevation, 30.0)
        self.assertEqual(view.camera_azimuth, 45.0)
        self.assertTrue(view.grid_enabled)
        self.assertTrue(view.axes_enabled)
        print("✅ اختبار إعدادات العرض نجح")


class TestAdvancedClientSystem(unittest.TestCase):
    """اختبار نظام إدارة العملاء المتقدم"""
    
    def setUp(self):
        """إعداد الاختبار"""
        try:
            from clients.advanced_client_system import ClientDatabaseManager, Client, ClientContact
            self.ClientDatabaseManager = ClientDatabaseManager
            self.Client = Client
            self.ClientContact = ClientContact
            
            # إنشاء قاعدة بيانات مؤقتة للاختبار
            self.temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
            self.db_manager = ClientDatabaseManager(self.temp_db.name)
            self.available = True
        except ImportError as e:
            print(f"⚠️ تعذر استيراد نظام إدارة العملاء المتقدم: {e}")
            self.available = False
    
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        if self.available and hasattr(self, 'temp_db'):
            os.unlink(self.temp_db.name)
    
    def test_client_creation(self):
        """اختبار إنشاء عميل"""
        if not self.available:
            self.skipTest("نظام إدارة العملاء غير متاح")
        
        client = self.Client(
            id="test_001",
            name="أحمد محمد",
            company="شركة الأثاث الحديث",
            phone="0501234567",
            email="<EMAIL>",
            address="الرياض، السعودية",
            city="الرياض",
            country="السعودية",
            postal_code="12345",
            tax_number="*********",
            payment_terms=30,
            discount_rate=0.05,
            credit_limit=50000.0,
            notes="عميل مهم",
            created_date=datetime.now().isoformat(),
            last_contact="",
            priority="عالي",
            source="موقع إلكتروني",
            assigned_to="مدير المبيعات"
        )
        
        # اختبار إضافة العميل
        result = self.db_manager.add_client(client)
        self.assertTrue(result)
        
        # اختبار استرجاع العملاء
        clients = self.db_manager.get_all_clients()
        self.assertEqual(len(clients), 1)
        self.assertEqual(clients[0]['name'], "أحمد محمد")
        print("✅ اختبار إنشاء العميل نجح")
    
    def test_client_search(self):
        """اختبار البحث في العملاء"""
        if not self.available:
            self.skipTest("نظام إدارة العملاء غير متاح")
        
        # إضافة عميل للبحث
        client = self.Client(
            id="test_002",
            name="فاطمة أحمد",
            company="مؤسسة التصميم الداخلي",
            phone="0507654321",
            email="<EMAIL>",
            address="جدة، السعودية",
            city="جدة",
            country="السعودية",
            postal_code="54321",
            tax_number="*********",
            payment_terms=15,
            discount_rate=0.10,
            credit_limit=75000.0,
            notes="عميلة VIP",
            created_date=datetime.now().isoformat(),
            last_contact="",
            priority="عالي",
            source="إحالة",
            assigned_to="مدير الحسابات"
        )
        
        self.db_manager.add_client(client)
        
        # اختبار البحث بالاسم
        results = self.db_manager.search_clients("فاطمة")
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0]['name'], "فاطمة أحمد")
        
        # اختبار البحث بالشركة
        results = self.db_manager.search_clients("التصميم")
        self.assertEqual(len(results), 1)
        
        print("✅ اختبار البحث في العملاء نجح")


class TestAdvancedInventorySystem(unittest.TestCase):
    """اختبار نظام المخزون المتقدم"""
    
    def setUp(self):
        """إعداد الاختبار"""
        try:
            from inventory.advanced_inventory_system import InventoryDatabaseManager, InventoryItem, StockMovement
            self.InventoryDatabaseManager = InventoryDatabaseManager
            self.InventoryItem = InventoryItem
            self.StockMovement = StockMovement
            
            # إنشاء قاعدة بيانات مؤقتة للاختبار
            self.temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
            self.db_manager = InventoryDatabaseManager(self.temp_db.name)
            self.available = True
        except ImportError as e:
            print(f"⚠️ تعذر استيراد نظام المخزون المتقدم: {e}")
            self.available = False
    
    def tearDown(self):
        """تنظيف بعد الاختبار"""
        if self.available and hasattr(self, 'temp_db'):
            os.unlink(self.temp_db.name)
    
    def test_inventory_item_creation(self):
        """اختبار إنشاء عنصر مخزون"""
        if not self.available:
            self.skipTest("نظام المخزون غير متاح")
        
        item = self.InventoryItem(
            id="item_001",
            name="خشب البلوط",
            category="أخشاب",
            subcategory="أخشاب صلبة",
            description="خشب بلوط عالي الجودة",
            unit="متر مربع",
            current_stock=100.0,
            minimum_stock=20.0,
            maximum_stock=500.0,
            unit_cost=150.0,
            selling_price=200.0,
            supplier="مورد الأخشاب المتميز",
            location="مستودع A - رف 1",
            barcode="*********0123",
            notes="خشب ممتاز للأثاث الفاخر",
            created_date=datetime.now().isoformat(),
            last_updated=datetime.now().isoformat()
        )
        
        # اختبار إضافة العنصر
        result = self.db_manager.add_inventory_item(item)
        self.assertTrue(result)
        
        # اختبار استرجاع العناصر
        items = self.db_manager.get_all_inventory_items()
        self.assertEqual(len(items), 1)
        self.assertEqual(items[0]['name'], "خشب البلوط")
        print("✅ اختبار إنشاء عنصر المخزون نجح")
    
    def test_stock_movement(self):
        """اختبار حركة المخزون"""
        if not self.available:
            self.skipTest("نظام المخزون غير متاح")
        
        # إضافة عنصر أولاً
        item = self.InventoryItem(
            id="item_002",
            name="مسامير خشب",
            category="مستلزمات",
            subcategory="مسامير",
            description="مسامير خشب متنوعة الأحجام",
            unit="علبة",
            current_stock=50.0,
            minimum_stock=10.0,
            maximum_stock=200.0,
            unit_cost=25.0,
            selling_price=35.0,
            supplier="مورد المستلزمات",
            location="مستودع B - رف 3",
            barcode="*********0987",
            notes="مسامير عالية الجودة",
            created_date=datetime.now().isoformat(),
            last_updated=datetime.now().isoformat()
        )
        
        self.db_manager.add_inventory_item(item)
        
        # إضافة حركة دخول
        movement = self.StockMovement(
            id="move_001",
            item_id="item_002",
            movement_type="دخول",
            quantity=25.0,
            unit_cost=25.0,
            total_cost=625.0,
            reference_number="PO-2024-001",
            notes="شراء جديد",
            created_date=datetime.now().isoformat(),
            created_by="مدير المخزون"
        )
        
        result = self.db_manager.add_stock_movement(movement)
        self.assertTrue(result)
        
        # التحقق من تحديث المخزون
        items = self.db_manager.get_all_inventory_items()
        updated_item = next(item for item in items if item['id'] == 'item_002')
        self.assertEqual(updated_item['current_stock'], 75.0)  # 50 + 25
        print("✅ اختبار حركة المخزون نجح")


class TestAdvancedCNCSystem(unittest.TestCase):
    """اختبار نظام CNC المتقدم"""
    
    def setUp(self):
        """إعداد الاختبار"""
        try:
            from cnc.advanced_cnc_system import CNCMachine, CNCJob, GCodeParser
            self.CNCMachine = CNCMachine
            self.CNCJob = CNCJob
            self.GCodeParser = GCodeParser
            self.available = True
        except ImportError as e:
            print(f"⚠️ تعذر استيراد نظام CNC المتقدم: {e}")
            self.available = False
    
    def test_cnc_machine_creation(self):
        """اختبار إنشاء ماكينة CNC"""
        if not self.available:
            self.skipTest("نظام CNC غير متاح")
        
        machine = self.CNCMachine(
            id="cnc_001",
            name="ماكينة CNC الرئيسية",
            model="CNC-3018 Pro",
            manufacturer="Generic CNC",
            serial_port="COM3",
            baud_rate=115200,
            work_area_x=300.0,
            work_area_y=180.0,
            work_area_z=45.0,
            spindle_speed_max=10000,
            feed_rate_max=1000.0,
            tool_changer=False,
            coolant_system=True
        )
        
        self.assertEqual(machine.name, "ماكينة CNC الرئيسية")
        self.assertEqual(machine.work_area_x, 300.0)
        self.assertEqual(machine.baud_rate, 115200)
        print("✅ اختبار إنشاء ماكينة CNC نجح")
    
    def test_gcode_parser(self):
        """اختبار محلل G-Code"""
        if not self.available:
            self.skipTest("نظام CNC غير متاح")
        
        # إنشاء ملف G-Code مؤقت للاختبار
        gcode_content = """
; Test G-Code file
G21 ; Set units to millimeters
G90 ; Absolute positioning
G0 X0 Y0 Z5 ; Move to start position
G1 X10 Y10 F1000 ; Linear move
G1 X20 Y20 Z-1 F500 ; Cut move
G0 Z5 ; Lift tool
M30 ; End program
        """.strip()
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.gcode', delete=False) as f:
            f.write(gcode_content)
            temp_gcode_file = f.name
        
        try:
            parser = self.GCodeParser()
            commands = parser.parse_file(temp_gcode_file)
            
            self.assertGreater(len(commands), 0)
            self.assertGreater(parser.total_lines, 0)
            print("✅ اختبار محلل G-Code نجح")
        finally:
            os.unlink(temp_gcode_file)


def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار الميزات المتقدمة لتطبيق مصمم الأثاث الاحترافي")
    print("=" * 60)
    
    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestSuite()
    
    # إضافة اختبارات المعاين ثلاثي الأبعاد
    test_suite.addTest(unittest.makeSuite(TestAdvanced3DViewer))
    
    # إضافة اختبارات نظام العملاء
    test_suite.addTest(unittest.makeSuite(TestAdvancedClientSystem))
    
    # إضافة اختبارات نظام المخزون
    test_suite.addTest(unittest.makeSuite(TestAdvancedInventorySystem))
    
    # إضافة اختبارات نظام CNC
    test_suite.addTest(unittest.makeSuite(TestAdvancedCNCSystem))
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("🎉 جميع الاختبارات نجحت! التطبيق جاهز للاستخدام.")
    else:
        print(f"⚠️ فشل {len(result.failures)} اختبار و {len(result.errors)} خطأ.")
        print("يرجى مراجعة الأخطاء أعلاه وإصلاحها.")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
