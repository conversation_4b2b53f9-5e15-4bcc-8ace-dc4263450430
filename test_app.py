#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لتطبيق مصمم الأثاث الاحترافي
Quick Test for Professional Furniture Designer Application
"""

import sys
import os

def test_imports():
    """اختبار استيراد المكتبات الأساسية"""
    print("🔍 اختبار استيراد المكتبات...")
    
    try:
        # اختبار PyQt6
        from PyQt6.QtWidgets import QApplication, QMainWindow, QLabel
        from PyQt6.QtCore import Qt
        from PyQt6.QtGui import QFont
        print("✅ PyQt6 - متوفر")
    except ImportError as e:
        print(f"❌ PyQt6 - غير متوفر: {e}")
        return False
    
    try:
        # اختبار المكتبات الأساسية
        import numpy as np
        print("✅ NumPy - متوفر")
    except ImportError:
        print("⚠️  NumPy - غير متوفر (اختياري)")
    
    try:
        import pandas as pd
        print("✅ Pandas - متوفر")
    except ImportError:
        print("⚠️  Pandas - غير متوفر (اختياري)")
    
    try:
        import trimesh
        print("✅ Trimesh - متوفر")
    except ImportError:
        print("⚠️  Trimesh - غير متوفر (اختياري)")
    
    try:
        import matplotlib.pyplot as plt
        print("✅ Matplotlib - متوفر")
    except ImportError:
        print("⚠️  Matplotlib - غير متوفر (اختياري)")
    
    return True


def test_ui_modules():
    """اختبار وحدات واجهة المستخدم"""
    print("\n🔍 اختبار وحدات واجهة المستخدم...")
    
    try:
        from ui.modern_styles import ModernStyleManager
        print("✅ ModernStyleManager - متوفر")
    except ImportError as e:
        print(f"❌ ModernStyleManager - غير متوفر: {e}")
        return False
    
    try:
        from ui.icons_manager import IconsManager
        print("✅ IconsManager - متوفر")
    except ImportError as e:
        print(f"❌ IconsManager - غير متوفر: {e}")
        return False
    
    return True


def test_application_modules():
    """اختبار وحدات التطبيق"""
    print("\n🔍 اختبار وحدات التطبيق...")
    
    modules_to_test = [
        ('models.advanced_3d_viewer', 'Advanced3DViewer'),
        ('drag_drop.file_handler', 'DragDropFileManager'),
        ('cnc.cnc_integration', 'CNCMachineManager'),
        ('reports.reports_widget', 'AdvancedReportsWidget'),
        ('backup.backup_manager', 'BackupManager')
    ]
    
    all_passed = True
    
    for module_name, class_name in modules_to_test:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"✅ {class_name} - متوفر")
        except ImportError as e:
            print(f"❌ {class_name} - غير متوفر: {e}")
            all_passed = False
        except AttributeError as e:
            print(f"❌ {class_name} - خطأ في الفئة: {e}")
            all_passed = False
    
    return all_passed


def create_simple_test_app():
    """إنشاء تطبيق اختبار بسيط"""
    print("\n🚀 إنشاء تطبيق اختبار بسيط...")
    
    try:
        from PyQt6.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
        from PyQt6.QtCore import Qt
        from PyQt6.QtGui import QFont
        
        class SimpleTestApp(QMainWindow):
            def __init__(self):
                super().__init__()
                self.setWindowTitle("اختبار مصمم الأثاث الاحترافي")
                self.setGeometry(100, 100, 600, 400)
                
                # إنشاء الواجهة
                central_widget = QWidget()
                self.setCentralWidget(central_widget)
                
                layout = QVBoxLayout(central_widget)
                
                # العنوان
                title_label = QLabel("🪑 مصمم الأثاث الاحترافي")
                title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                title_label.setFont(QFont("Arial", 24, QFont.Weight.Bold))
                
                # رسالة النجاح
                success_label = QLabel("✅ تم تشغيل التطبيق بنجاح!")
                success_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                success_label.setFont(QFont("Arial", 16))
                
                # معلومات النظام
                info_label = QLabel(f"""
                نظام التشغيل: {os.name}
                Python: {sys.version.split()[0]}
                PyQt6: متوفر
                
                يمكنك الآن تشغيل التطبيق الكامل باستخدام:
                python run_professional_furniture_designer.py
                """)
                info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                info_label.setFont(QFont("Arial", 12))
                
                layout.addWidget(title_label)
                layout.addWidget(success_label)
                layout.addWidget(info_label)
                
        # إنشاء وتشغيل التطبيق
        app = QApplication(sys.argv)
        window = SimpleTestApp()
        window.show()
        
        print("✅ تم إنشاء تطبيق الاختبار بنجاح!")
        print("🎉 يمكنك إغلاق النافذة للمتابعة")
        
        app.exec()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء تطبيق الاختبار: {e}")
        return False


def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 60)
    print("🧪 اختبار تطبيق مصمم الأثاث الاحترافي")
    print("=" * 60)
    
    # اختبار المكتبات الأساسية
    if not test_imports():
        print("\n❌ فشل في اختبار المكتبات الأساسية")
        print("يرجى تثبيت PyQt6 أولاً:")
        print("pip install PyQt6")
        input("اضغط Enter للخروج...")
        return
    
    # اختبار وحدات واجهة المستخدم
    ui_test_passed = test_ui_modules()
    
    # اختبار وحدات التطبيق
    app_test_passed = test_application_modules()
    
    print("\n" + "=" * 60)
    print("📊 نتائج الاختبار:")
    print("=" * 60)
    
    if ui_test_passed and app_test_passed:
        print("✅ جميع الاختبارات نجحت!")
        print("🚀 يمكنك تشغيل التطبيق الكامل الآن")
        
        # عرض تطبيق اختبار بسيط
        run_test = input("\nهل تريد تشغيل تطبيق اختبار بسيط؟ (y/n): ").lower()
        if run_test in ['y', 'yes', 'نعم']:
            create_simple_test_app()
            
    else:
        print("⚠️  بعض الاختبارات فشلت")
        print("يمكنك المتابعة ولكن قد تواجه مشاكل في بعض الميزات")
        
        if not ui_test_passed:
            print("❌ وحدات واجهة المستخدم غير متوفرة")
        if not app_test_passed:
            print("❌ بعض وحدات التطبيق غير متوفرة")
    
    print("\n🔧 للحصول على التطبيق الكامل، استخدم:")
    print("python run_professional_furniture_designer.py")
    
    input("\nاضغط Enter للخروج...")


if __name__ == "__main__":
    main()
