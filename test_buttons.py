#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار عمل الأزرار في واجهة المستخدم
Test Buttons Functionality in User Interface
"""

import sys
import os
from pathlib import Path

# إضافة المجلد الحالي لمسار Python
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_button_connections():
    """اختبار اتصالات الأزرار"""
    print("🧪 اختبار اتصالات الأزرار...")

    try:
        # استيراد الفئة الرئيسية
        from main_application import ModernMainWindow

        # قائمة الدوال المطلوبة
        required_methods = [
            'new_project',
            'open_project',
            'save_project',
            'add_component',
            'optimize_cutting',
            'show_financial_report',
            'show_inventory_report',
            'export_to_excel',
            'add_client',
            'show_inventory_manager',
            'generate_quote'
        ]

        # فحص وجود الدوال في الفئة (بدون إنشاء كائن)
        missing_methods = []
        for method in required_methods:
            if hasattr(ModernMainWindow, method):
                print(f"   ✅ {method}: موجود")
            else:
                print(f"   ❌ {method}: مفقود")
                missing_methods.append(method)

        if missing_methods:
            print(f"\n⚠️ دوال مفقودة: {len(missing_methods)}")
            return False

        print("✅ جميع دوال الأزرار موجودة")
        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار الأزرار: {e}")
        return False

def test_imports():
    """اختبار الاستيرادات"""
    print("\n🧪 اختبار الاستيرادات...")

    try:
        from main_application import ModernMainWindow, FurnitureDesignerApp
        print("   ✅ استيراد الفئات الرئيسية: نجح")

        from PySide6.QtWidgets import QApplication
        print("   ✅ استيراد PySide6: نجح")

        return True

    except Exception as e:
        print(f"❌ خطأ في الاستيرادات: {e}")
        return False

def test_file_structure():
    """اختبار هيكل الملفات"""
    print("\n🧪 اختبار هيكل الملفات...")

    try:
        required_files = [
            'main_application.py',
            'ui/modern_styles.py',
            'ui/icons_manager.py'
        ]

        missing_files = []
        for file_path in required_files:
            if os.path.exists(file_path):
                print(f"   ✅ {file_path}: موجود")
            else:
                print(f"   ❌ {file_path}: مفقود")
                missing_files.append(file_path)

        if missing_files:
            print(f"\n⚠️ ملفات مفقودة: {len(missing_files)}")
            return False

        print("✅ جميع الملفات الأساسية موجودة")
        return True

    except Exception as e:
        print(f"❌ خطأ في فحص الملفات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار عمل الأزرار في واجهة المستخدم")
    print("=" * 60)

    tests = [
        ("اتصالات الأزرار", test_button_connections),
        ("الاستيرادات", test_imports),
        ("هيكل الملفات", test_file_structure)
    ]

    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))

    # تقييم النتائج
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج اختبار الأزرار:")

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1

    success_rate = (passed / total) * 100
    print(f"\n🎯 معدل النجاح: {passed}/{total} ({success_rate:.1f}%)")

    if success_rate >= 80:
        print("\n🎉 ممتاز! جميع الأزرار تعمل بشكل صحيح")
        print("✅ واجهة المستخدم جاهزة للاستخدام")
    elif success_rate >= 60:
        print("\n✅ جيد! معظم الأزرار تعمل")
        print("⚠️ قد تحتاج بعض التحسينات الإضافية")
    else:
        print("\n⚠️ يحتاج إلى مزيد من الإصلاحات")
        print("🔧 بعض الأزرار المهمة لا تعمل")

    return success_rate >= 80

if __name__ == "__main__":
    success = main()

    print(f"\n{'='*60}")
    print("🏁 انتهى اختبار الأزرار")

    if success:
        print("\n🎯 جميع الأزرار تعمل بشكل مثالي!")
        print("🚀 يمكنك الآن استخدام التطبيق بثقة")
    else:
        print("\n⚠️ بعض الأزرار تحتاج إلى مراجعة")

    sys.exit(0 if success else 1)
