#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار حاسبة التكلفة المحسنة
Test Enhanced Cost Calculator
"""

import sys
import os
from pathlib import Path

# إضافة المجلد الحالي لمسار Python
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_enhanced_calculator_imports():
    """اختبار استيراد حاسبة التكلفة المحسنة"""
    print("🧪 اختبار استيراد حاسبة التكلفة المحسنة...")
    
    try:
        from calculator.enhanced_cost_calculator import (
            EnhancedMaterialCost, EnhancedLaborCost, UtilityCost,
            EnhancedProjectCalculation, MaterialType, SkillLevel,
            ClientType, ProjectSize, ROICalculation
        )
        print("   ✅ استيراد الفئات الأساسية: نجح")
        
        from calculator.enhanced_cost_calculator import (
            CostChartWidget, BuiltInCalculatorWidget, EnhancedCostCalculatorWidget
        )
        print("   ✅ استيراد واجهات المستخدم: نجح")
        
        from calculator.enhanced_dialogs import (
            EnhancedMaterialDialog, EnhancedLaborDialog
        )
        print("   ✅ استيراد الحوارات المحسنة: نجح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False

def test_enhanced_material_cost():
    """اختبار فئة تكلفة المواد المحسنة"""
    print("\n🧪 اختبار فئة تكلفة المواد المحسنة...")
    
    try:
        from calculator.enhanced_cost_calculator import EnhancedMaterialCost, MaterialType
        
        # إنشاء مادة اختبار
        material = EnhancedMaterialCost(
            id="test_001",
            name="خشب صنوبر",
            material_type=MaterialType.WOOD,
            quantity=10.0,
            unit="متر مربع",
            unit_cost=50.0,
            supplier="مورد الخشب المحلي",
            delivery_cost=25.0,
            waste_percentage=15.0,
            notes="خشب عالي الجودة"
        )
        
        print(f"   ✅ إنشاء المادة: {material.name}")
        print(f"   ✅ نوع المادة: {material.material_type.value}")
        print(f"   ✅ نسبة الهدر التلقائية: {material.get_waste_percentage_by_type()}%")
        
        # حساب التكلفة
        total_cost = material.calculate_total_cost()
        print(f"   ✅ التكلفة الإجمالية: {total_cost:.2f} د.ل")
        
        # التحقق من صحة الحساب
        expected_base = 10.0 * 50.0  # 500
        expected_waste = expected_base * 0.15  # 75
        expected_total = expected_base + expected_waste + 25.0  # 600
        
        if abs(total_cost - expected_total) < 0.01:
            print("   ✅ حساب التكلفة صحيح")
        else:
            print(f"   ❌ خطأ في حساب التكلفة: متوقع {expected_total}, فعلي {total_cost}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تكلفة المواد: {e}")
        return False

def test_enhanced_labor_cost():
    """اختبار فئة تكلفة العمالة المحسنة"""
    print("\n🧪 اختبار فئة تكلفة العمالة المحسنة...")
    
    try:
        from calculator.enhanced_cost_calculator import EnhancedLaborCost, SkillLevel
        
        # إنشاء مهمة عمالة اختبار
        labor = EnhancedLaborCost(
            id="labor_001",
            task_name="تجميع الخزانة",
            skill_level=SkillLevel.EXPERT,
            hours=8.0,
            hourly_rate=20.0,
            difficulty_multiplier=1.5,
            overtime_hours=2.0,
            overtime_rate_multiplier=1.5,
            notes="مهمة معقدة تتطلب خبرة"
        )
        
        print(f"   ✅ إنشاء مهمة العمالة: {labor.task_name}")
        print(f"   ✅ مستوى المهارة: {labor.skill_level.value}")
        print(f"   ✅ معامل المهارة: {labor.get_skill_multiplier()}")
        
        # حساب التكلفة
        total_cost = labor.calculate_total_cost()
        print(f"   ✅ التكلفة الإجمالية: {total_cost:.2f} د.ل")
        
        # التحقق من صحة الحساب
        base_cost = 8.0 * 20.0 * 1.3  # ساعات × أجر × معامل الخبرة
        overtime_cost = 2.0 * 20.0 * 1.5  # ساعات إضافية × أجر × معامل إضافي
        expected_total = (base_cost + overtime_cost) * 1.5  # تطبيق معامل الصعوبة
        
        if abs(total_cost - expected_total) < 0.01:
            print("   ✅ حساب التكلفة صحيح")
        else:
            print(f"   ❌ خطأ في حساب التكلفة: متوقع {expected_total:.2f}, فعلي {total_cost:.2f}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تكلفة العمالة: {e}")
        return False

def test_utility_cost():
    """اختبار فئة تكاليف المرافق"""
    print("\n🧪 اختبار فئة تكاليف المرافق...")
    
    try:
        from calculator.enhanced_cost_calculator import UtilityCost
        
        # إنشاء تكاليف مرافق اختبار
        utilities = UtilityCost(
            electricity_kwh=100.0,
            electricity_rate=0.15,
            water_cubic_meters=5.0,
            water_rate=2.0,
            gas_cubic_meters=3.0,
            gas_rate=1.5,
            internet_days=30.0,
            internet_daily_rate=5.0
        )
        
        # حساب التكلفة
        total_cost = utilities.calculate_total_cost()
        print(f"   ✅ تكلفة الكهرباء: {utilities.electricity_kwh * utilities.electricity_rate:.2f} د.ل")
        print(f"   ✅ تكلفة المياه: {utilities.water_cubic_meters * utilities.water_rate:.2f} د.ل")
        print(f"   ✅ تكلفة الغاز: {utilities.gas_cubic_meters * utilities.gas_rate:.2f} د.ل")
        print(f"   ✅ تكلفة الإنترنت: {utilities.internet_days * utilities.internet_daily_rate:.2f} د.ل")
        print(f"   ✅ إجمالي تكاليف المرافق: {total_cost:.2f} د.ل")
        
        # التحقق من صحة الحساب
        expected_total = (100.0 * 0.15) + (5.0 * 2.0) + (3.0 * 1.5) + (30.0 * 5.0)
        
        if abs(total_cost - expected_total) < 0.01:
            print("   ✅ حساب تكاليف المرافق صحيح")
        else:
            print(f"   ❌ خطأ في حساب تكاليف المرافق: متوقع {expected_total}, فعلي {total_cost}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار تكاليف المرافق: {e}")
        return False

def test_roi_calculation():
    """اختبار حساب عائد الاستثمار"""
    print("\n🧪 اختبار حساب عائد الاستثمار...")
    
    try:
        from calculator.enhanced_cost_calculator import ROICalculation
        
        # إنشاء حساب ROI اختبار
        roi = ROICalculation(
            initial_investment=100000.0,
            annual_revenue=150000.0,
            annual_costs=80000.0,
            project_lifespan_years=5,
            discount_rate=0.1
        )
        
        # حساب المؤشرات
        npv = roi.calculate_npv()
        roi_percentage = roi.calculate_roi_percentage()
        payback_period = roi.calculate_payback_period()
        
        print(f"   ✅ صافي القيمة الحالية (NPV): {npv:.2f} د.ل")
        print(f"   ✅ نسبة عائد الاستثمار: {roi_percentage:.2f}%")
        print(f"   ✅ فترة الاسترداد: {payback_period:.2f} سنة")
        
        # التحقق من منطقية النتائج
        if npv > 0:
            print("   ✅ المشروع مربح (NPV موجب)")
        else:
            print("   ⚠️ المشروع غير مربح (NPV سالب)")
        
        if roi_percentage > 0:
            print("   ✅ عائد الاستثمار موجب")
        else:
            print("   ⚠️ عائد الاستثمار سالب")
        
        if payback_period < roi.project_lifespan_years:
            print("   ✅ فترة الاسترداد معقولة")
        else:
            print("   ⚠️ فترة الاسترداد طويلة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار ROI: {e}")
        return False

def test_enhanced_project_calculation():
    """اختبار حساب المشروع المحسن"""
    print("\n🧪 اختبار حساب المشروع المحسن...")
    
    try:
        from calculator.enhanced_cost_calculator import (
            EnhancedProjectCalculation, EnhancedMaterialCost, EnhancedLaborCost,
            UtilityCost, MaterialType, SkillLevel, ClientType, ProjectSize
        )
        from PySide6.QtCore import QDate
        
        # إنشاء مواد اختبار
        materials = [
            EnhancedMaterialCost(
                id="mat1", name="خشب", material_type=MaterialType.WOOD,
                quantity=5.0, unit="متر", unit_cost=100.0
            ),
            EnhancedMaterialCost(
                id="mat2", name="مسامير", material_type=MaterialType.HARDWARE,
                quantity=50.0, unit="قطعة", unit_cost=2.0
            )
        ]
        
        # إنشاء عمالة اختبار
        labor = [
            EnhancedLaborCost(
                id="lab1", task_name="التجميع", skill_level=SkillLevel.EXPERT,
                hours=10.0, hourly_rate=25.0
            )
        ]
        
        # إنشاء تكاليف مرافق
        utilities = UtilityCost(electricity_kwh=50.0, electricity_rate=0.15)
        
        # إنشاء مشروع اختبار
        project = EnhancedProjectCalculation(
            id="proj_001",
            project_name="خزانة مكتبية",
            client_name="شركة الأثاث المحدودة",
            client_type=ClientType.VIP,
            project_size=ProjectSize.MEDIUM,
            materials=materials,
            labor=labor,
            utilities=utilities,
            overhead_percentage=15.0,
            profit_margin=25.0,
            tax_rate=5.0,
            discount_percentage=0.0,
            budget_limit=2000.0
        )
        
        print(f"   ✅ إنشاء المشروع: {project.project_name}")
        print(f"   ✅ العميل: {project.client_name} ({project.client_type.value})")
        print(f"   ✅ حجم المشروع: {project.project_size.value}")
        
        # حساب التكاليف
        breakdown = project.get_cost_breakdown()
        
        print(f"   ✅ تكلفة المواد: {breakdown['materials']:.2f} د.ل")
        print(f"   ✅ تكلفة العمالة: {breakdown['labor']:.2f} د.ل")
        print(f"   ✅ تكلفة المرافق: {breakdown['utilities']:.2f} د.ل")
        print(f"   ✅ التكاليف العامة: {breakdown['overheads']:.2f} د.ل")
        print(f"   ✅ الربح: {breakdown['profit']:.2f} د.ل")
        print(f"   ✅ الضريبة: {breakdown['tax']:.2f} د.ل")
        print(f"   ✅ الخصم: {breakdown['discount']:.2f} د.ل")
        print(f"   ✅ الإجمالي النهائي: {breakdown['total']:.2f} د.ل")
        
        # فحص تجاوز الميزانية
        if project.is_over_budget():
            variance = project.get_budget_variance()
            print(f"   ⚠️ تجاوز الميزانية بمقدار: {variance:.2f} د.ل")
        else:
            print("   ✅ المشروع ضمن الميزانية المحددة")
        
        # فحص خصم العميل
        client_discount = project.get_client_discount()
        print(f"   ✅ خصم العميل: {client_discount}%")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار المشروع: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار حاسبة التكلفة المحسنة")
    print("=" * 60)
    
    tests = [
        ("استيراد الحاسبة المحسنة", test_enhanced_calculator_imports),
        ("تكلفة المواد المحسنة", test_enhanced_material_cost),
        ("تكلفة العمالة المحسنة", test_enhanced_labor_cost),
        ("تكاليف المرافق", test_utility_cost),
        ("حساب عائد الاستثمار", test_roi_calculation),
        ("حساب المشروع المحسن", test_enhanced_project_calculation)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    # تقييم النتائج
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج اختبار الحاسبة المحسنة:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    success_rate = (passed / total) * 100
    print(f"\n🎯 معدل النجاح: {passed}/{total} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print("\n🎉 ممتاز! حاسبة التكلفة المحسنة تعمل بشكل صحيح")
        print("✅ جميع الميزات الجديدة متاحة ومختبرة")
        print("\n🚀 الميزات المحسنة:")
        print("   • حساب تكاليف المواد مع نسب هدر متغيرة")
        print("   • نظام عمالة متقدم مع مستويات مهارة")
        print("   • حساب تكاليف المرافق (كهرباء، مياه، غاز، إنترنت)")
        print("   • حساب عائد الاستثمار (ROI) مع NPV")
        print("   • خصومات تلقائية حسب نوع العميل")
        print("   • معاملات تعقيد حسب حجم المشروع")
    elif success_rate >= 60:
        print("\n✅ جيد! معظم ميزات الحاسبة المحسنة تعمل")
        print("⚠️ قد تحتاج بعض التحسينات الإضافية")
    else:
        print("\n⚠️ يحتاج إلى مزيد من الإصلاحات")
        print("🔧 بعض الميزات المهمة لا تعمل")
    
    return success_rate >= 80

if __name__ == "__main__":
    success = main()
    
    print(f"\n{'='*60}")
    print("🏁 انتهى اختبار حاسبة التكلفة المحسنة")
    
    if success:
        print("\n🎯 الحاسبة المحسنة جاهزة للاستخدام!")
        print("🧮 يمكنك الآن الاستفادة من جميع الميزات المتقدمة")
    else:
        print("\n⚠️ تحتاج الحاسبة إلى مراجعة إضافية")
    
    sys.exit(0 if success else 1)
