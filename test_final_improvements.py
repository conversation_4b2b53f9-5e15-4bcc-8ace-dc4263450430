#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي للتحسينات المضافة للتطبيق
Final Test for Application Improvements
"""

import sys
import os
from pathlib import Path

# إضافة المجلد الحالي لمسار Python
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_basic_imports():
    """اختبار الاستيرادات الأساسية"""
    print("🧪 اختبار الاستيرادات الأساسية...")
    
    try:
        # اختبار نظام العملاء
        from clients.advanced_client_system import ClientDatabaseManager
        print("   ✅ نظام العملاء: متاح")
        
        # اختبار نظام المخزون
        from inventory.advanced_inventory_system import InventoryDatabaseManager
        print("   ✅ نظام المخزون: متاح")
        
        # اختبار نظام التقارير
        from reports.advanced_reports_system import FinancialReportGenerator
        print("   ✅ نظام التقارير: متاح")
        
        # اختبار نظام CNC
        from cnc.advanced_cnc_system import CNCMachine, GCodeParser
        print("   ✅ نظام CNC: متاح")
        
        # اختبار العملة
        from ui.modern_styles import CurrencyFormatter
        print("   ✅ نظام العملة: متاح")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في الاستيرادات: {e}")
        return False

def test_database_functions():
    """اختبار دوال قواعد البيانات"""
    print("\n🧪 اختبار دوال قواعد البيانات...")
    
    try:
        from clients.advanced_client_system import ClientDatabaseManager
        from inventory.advanced_inventory_system import InventoryDatabaseManager
        
        # اختبار العملاء
        client_db = ClientDatabaseManager("test_clients.db")
        required_client_methods = [
            'get_client_by_id', 'update_client', 'delete_client',
            'search_clients', 'get_client_statistics'
        ]
        
        for method in required_client_methods:
            if hasattr(client_db, method):
                print(f"   ✅ {method}: موجود")
            else:
                print(f"   ❌ {method}: مفقود")
                return False
        
        # اختبار المخزون
        inventory_db = InventoryDatabaseManager("test_inventory.db")
        required_inventory_methods = [
            'update_inventory_item', 'delete_inventory_item',
            'search_inventory_items', 'get_inventory_statistics'
        ]
        
        for method in required_inventory_methods:
            if hasattr(inventory_db, method):
                print(f"   ✅ {method}: موجود")
            else:
                print(f"   ❌ {method}: مفقود")
                return False
        
        # تنظيف ملفات الاختبار
        try:
            os.remove("test_clients.db")
            os.remove("test_inventory.db")
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار قواعد البيانات: {e}")
        return False

def test_currency_system():
    """اختبار نظام العملة"""
    print("\n🧪 اختبار نظام العملة الليبية...")
    
    try:
        from ui.modern_styles import CurrencyFormatter
        
        # اختبار التنسيق
        test_amounts = [1234.56, 50000, 1000000]
        
        for amount in test_amounts:
            formatted = CurrencyFormatter.format_currency(amount)
            short_format = CurrencyFormatter.format_currency_short(amount)
            print(f"   💰 {amount} → {formatted} | {short_format}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار العملة: {e}")
        return False

def test_cnc_components():
    """اختبار مكونات CNC"""
    print("\n🧪 اختبار مكونات CNC...")
    
    try:
        from cnc.advanced_cnc_system import CNCMachine, GCodeParser
        
        # إنشاء ماكينة اختبار
        machine = CNCMachine(
            id="test_001",
            name="ماكينة اختبار",
            model="Test-Model",
            manufacturer="Test",
            serial_port="COM1",
            baud_rate=115200,
            work_area_x=300,
            work_area_y=180,
            work_area_z=45,
            spindle_speed_max=10000,
            feed_rate_max=1000,
            tool_changer=False,
            coolant_system=False
        )
        print("   ✅ إنشاء ماكينة CNC: نجح")
        
        # إنشاء محلل G-Code
        parser = GCodeParser()
        print("   ✅ إنشاء محلل G-Code: نجح")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار CNC: {e}")
        return False

def test_reports_components():
    """اختبار مكونات التقارير"""
    print("\n🧪 اختبار مكونات التقارير...")
    
    try:
        from reports.advanced_reports_system import (
            FinancialReportGenerator, InventoryReportGenerator, 
            ClientReportGenerator, ArabicPDFGenerator
        )
        
        # إنشاء مولدات التقارير
        db_managers = {}
        
        financial_gen = FinancialReportGenerator(db_managers)
        print("   ✅ مولد التقارير المالية: نجح")
        
        inventory_gen = InventoryReportGenerator(db_managers)
        print("   ✅ مولد تقارير المخزون: نجح")
        
        client_gen = ClientReportGenerator(db_managers)
        print("   ✅ مولد تقارير العملاء: نجح")
        
        pdf_gen = ArabicPDFGenerator()
        print("   ✅ مولد PDF العربي: نجح")
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار التقارير: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 اختبار نهائي للتحسينات المضافة للتطبيق")
    print("=" * 60)
    
    tests = [
        ("الاستيرادات الأساسية", test_basic_imports),
        ("دوال قواعد البيانات", test_database_functions),
        ("نظام العملة الليبية", test_currency_system),
        ("مكونات CNC", test_cnc_components),
        ("مكونات التقارير", test_reports_components)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    # تقييم النتائج
    print("\n" + "=" * 60)
    print("📊 ملخص نتائج الاختبار النهائي:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    success_rate = (passed / total) * 100
    print(f"\n🎯 معدل النجاح: {passed}/{total} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print("\n🎉 ممتاز! جميع التحسينات تعمل بشكل صحيح")
        print("✅ التطبيق جاهز للاستخدام التجاري")
        print("\n🚀 لتشغيل التطبيق:")
        print("   python run_advanced_furniture_designer.py")
    elif success_rate >= 60:
        print("\n✅ جيد! معظم التحسينات تعمل")
        print("⚠️ قد تحتاج بعض التحسينات الإضافية")
    else:
        print("\n⚠️ يحتاج إلى مزيد من الإصلاحات")
        print("🔧 بعض التحسينات المهمة لا تعمل")
    
    return success_rate >= 60

if __name__ == "__main__":
    success = main()
    
    print(f"\n{'='*60}")
    print("🏁 انتهى الاختبار النهائي للتحسينات")
    
    if success:
        print("\n🎯 التطبيق جاهز للاستخدام!")
    else:
        print("\n⚠️ يحتاج إلى مراجعة إضافية")
    
    sys.exit(0 if success else 1)
