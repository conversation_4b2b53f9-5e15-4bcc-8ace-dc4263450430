#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التحسينات المضافة للتطبيق
Test Application Improvements
"""

import sys
import os
from pathlib import Path

# إضافة المجلد الحالي لمسار Python
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_clients_system():
    """اختبار نظام العملاء المحسن"""
    print("🧪 اختبار نظام إدارة العملاء المحسن...")
    try:
        from clients.advanced_client_system import AdvancedClientManagerWidget, ClientDatabaseManager

        # اختبار مدير قاعدة البيانات
        db_manager = ClientDatabaseManager()
        print("   ✅ تم إنشاء مدير قاعدة البيانات")

        # اختبار الدوال الجديدة
        required_methods = [
            'get_client_by_id',
            'update_client',
            'delete_client',
            'search_clients',
            'get_client_statistics'
        ]

        missing_methods = []
        for method in required_methods:
            if hasattr(db_manager, method):
                print(f"   ✅ {method}: موجود")
            else:
                print(f"   ❌ {method}: مفقود")
                missing_methods.append(method)

        if not missing_methods:
            print("✅ جميع دوال نظام العملاء متاحة")
            return True
        else:
            print(f"⚠️ {len(missing_methods)} دالة مفقودة في نظام العملاء")
            return False

    except Exception as e:
        print(f"❌ خطأ في اختبار نظام العملاء: {e}")
        return False

def test_inventory_system():
    """اختبار نظام المخزون المحسن"""
    print("\n🧪 اختبار نظام إدارة المخزون المحسن...")
    try:
        from inventory.advanced_inventory_system import AdvancedInventoryManagerWidget, InventoryDatabaseManager

        # اختبار مدير قاعدة البيانات
        db_manager = InventoryDatabaseManager()
        print("   ✅ تم إنشاء مدير قاعدة البيانات")

        # اختبار الدوال الجديدة
        required_methods = [
            'update_inventory_item',
            'delete_inventory_item',
            'search_inventory_items',
            'get_inventory_statistics',
            'add_stock_movement',
            'get_low_stock_items'
        ]

        missing_methods = []
        for method in required_methods:
            if hasattr(db_manager, method):
                print(f"   ✅ {method}: موجود")
            else:
                print(f"   ❌ {method}: مفقود")
                missing_methods.append(method)

        if not missing_methods:
            print("✅ جميع دوال نظام المخزون متاحة")
            return True
        else:
            print(f"⚠️ {len(missing_methods)} دالة مفقودة في نظام المخزون")
            return False

    except Exception as e:
        print(f"❌ خطأ في اختبار نظام المخزون: {e}")
        return False

def test_currency_integration():
    """اختبار تكامل العملة الليبية"""
    print("\n🧪 اختبار تكامل العملة الليبية...")
    try:
        from ui.modern_styles import CurrencyFormatter

        # اختبار التنسيق
        test_amounts = [1234.56, 50000, 1000000, 0.5]

        for amount in test_amounts:
            formatted = CurrencyFormatter.format_currency(amount)
            short_format = CurrencyFormatter.format_currency_short(amount)
            print(f"   💰 {amount} → {formatted} | {short_format}")

        print("✅ تكامل العملة الليبية يعمل بشكل صحيح")
        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار العملة: {e}")
        return False

def test_dialogs():
    """اختبار الحوارات الجديدة"""
    print("\n🧪 اختبار الحوارات الجديدة...")
    try:
        # اختبار حوارات العملاء
        from clients.advanced_client_system import ClientEditDialog, ClientProjectsDialog
        print("   ✅ حوارات العملاء: متاحة")

        # اختبار حوارات المخزون
        from inventory.advanced_inventory_system import InventoryItemDialog, StockMovementDialog
        print("   ✅ حوارات المخزون: متاحة")

        print("✅ جميع الحوارات الجديدة متاحة")
        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار الحوارات: {e}")
        return False

def test_reports_system():
    """اختبار نظام التقارير المحسن"""
    print("\n🧪 اختبار نظام التقارير المحسن...")
    try:
        # اختبار الاستيراد فقط (بدون إنشاء واجهات)
        from reports.advanced_reports_system import FinancialReportGenerator, InventoryReportGenerator, ClientReportGenerator
        print("   ✅ مولدات التقارير: متاحة")

        # اختبار مولد التقارير المالية
        db_managers = {}  # قاموس فارغ للاختبار
        financial_generator = FinancialReportGenerator(db_managers)
        print("   ✅ تم إنشاء مولد التقارير المالية")

        # اختبار الحوارات (استيراد فقط)
        from reports.advanced_reports_system import ReportPeriodDialog, ReportExportDialog
        print("   ✅ حوارات التقارير: متاحة")

        # اختبار مولد PDF
        from reports.advanced_reports_system import ArabicPDFGenerator
        pdf_generator = ArabicPDFGenerator()
        print("   ✅ مولد PDF العربي: متاح")

        print("✅ جميع مكونات نظام التقارير متاحة")
        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار نظام التقارير: {e}")
        return False

def test_cnc_system():
    """اختبار نظام CNC المحسن"""
    print("\n🧪 اختبار نظام CNC المحسن...")
    try:
        # اختبار الاستيراد فقط (بدون إنشاء واجهات)
        from cnc.advanced_cnc_system import CNCMachine, GCodeParser, CNCCommunicator
        print("   ✅ فئات CNC الأساسية: متاحة")

        # اختبار محلل G-Code
        parser = GCodeParser()
        print("   ✅ تم إنشاء محلل G-Code")

        # اختبار إنشاء ماكينة CNC
        machine = CNCMachine(
            id="test_001",
            name="ماكينة اختبار",
            model="Test-Model",
            manufacturer="Test",
            serial_port="COM1",
            baud_rate=115200,
            work_area_x=300,
            work_area_y=180,
            work_area_z=45,
            spindle_speed_max=10000,
            feed_rate_max=1000,
            tool_changer=False,
            coolant_system=False
        )
        print("   ✅ تم إنشاء كائن ماكينة CNC")

        # اختبار الحوارات (استيراد فقط)
        from cnc.advanced_cnc_system import CNCMachineDialog, GCodePreviewDialog, CNCStatusMonitorDialog
        print("   ✅ حوارات CNC: متاحة")

        print("✅ جميع مكونات نظام CNC متاحة")
        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار نظام CNC: {e}")
        return False

def test_database_creation():
    """اختبار إنشاء قواعد البيانات"""
    print("\n🧪 اختبار إنشاء قواعد البيانات...")
    try:
        from clients.advanced_client_system import ClientDatabaseManager
        from inventory.advanced_inventory_system import InventoryDatabaseManager

        # إنشاء قواعد البيانات
        client_db = ClientDatabaseManager("test_clients.db")
        inventory_db = InventoryDatabaseManager("test_inventory.db")

        print("   ✅ قاعدة بيانات العملاء: تم إنشاؤها")
        print("   ✅ قاعدة بيانات المخزون: تم إنشاؤها")

        # تنظيف ملفات الاختبار
        import os
        try:
            os.remove("test_clients.db")
            os.remove("test_inventory.db")
            print("   🧹 تم تنظيف ملفات الاختبار")
        except:
            pass

        print("✅ إنشاء قواعد البيانات يعمل بشكل صحيح")
        return True

    except Exception as e:
        print(f"❌ خطأ في اختبار قواعد البيانات: {e}")
        return False

def run_comprehensive_test():
    """تشغيل الاختبار الشامل للتحسينات"""
    print("🚀 اختبار التحسينات المضافة للتطبيق")
    print("=" * 50)

    tests = [
        ("نظام العملاء المحسن", test_clients_system),
        ("نظام المخزون المحسن", test_inventory_system),
        ("تكامل العملة الليبية", test_currency_integration),
        ("الحوارات الجديدة", test_dialogs),
        ("نظام التقارير المحسن", test_reports_system),
        ("نظام CNC المحسن", test_cnc_system),
        ("إنشاء قواعد البيانات", test_database_creation)
    ]

    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))

    # تقييم النتائج
    print("\n" + "=" * 50)
    print("📊 ملخص نتائج اختبار التحسينات:")

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1

    success_rate = (passed / total) * 100
    print(f"\n🎯 معدل النجاح: {passed}/{total} ({success_rate:.1f}%)")

    if success_rate >= 80:
        print("🎉 ممتاز! جميع التحسينات تعمل بشكل صحيح")
        print("✅ التطبيق جاهز للاستخدام التجاري")
    elif success_rate >= 60:
        print("✅ جيد! معظم التحسينات تعمل")
        print("⚠️ قد تحتاج بعض التحسينات الإضافية")
    else:
        print("⚠️ يحتاج إلى مزيد من الإصلاحات")
        print("🔧 بعض التحسينات المهمة لا تعمل")

    return success_rate >= 60

def main():
    """الدالة الرئيسية"""
    try:
        success = run_comprehensive_test()

        print(f"\n{'='*50}")
        if success:
            print("🎯 اختبار التحسينات مكتمل بنجاح!")
            print("✅ يمكنك الآن تشغيل التطبيق المحسن")
            print("\n🚀 لتشغيل التطبيق:")
            print("   python run_advanced_furniture_designer.py")
        else:
            print("⚠️ اختبار التحسينات يحتاج إلى مراجعة")
            print("💡 راجع الأخطاء أعلاه وأعد المحاولة")

        return success

    except Exception as e:
        print(f"\n❌ خطأ غير متوقع في الاختبار: {e}")
        return False

if __name__ == "__main__":
    success = main()

    print(f"\n{'='*50}")
    print("🏁 انتهى اختبار التحسينات")

    if not success:
        input("\n⏸️ اضغط Enter للخروج...")

    sys.exit(0 if success else 1)
