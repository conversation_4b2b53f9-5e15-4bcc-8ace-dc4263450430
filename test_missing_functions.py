#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الدوال المفقودة المضافة
Test Missing Functions Added
"""

import sys
import os
from pathlib import Path

# إضافة المجلد الحالي لمسار Python
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_reports_functions():
    """اختبار دوال التقارير"""
    print("1️⃣ اختبار دوال نظام التقارير...")
    try:
        from reports.advanced_reports_system import AdvancedReportsWidget
        
        # إنشاء كائن للاختبار
        reports_widget = AdvancedReportsWidget()
        
        # اختبار وجود الدوال المطلوبة
        required_functions = [
            'create_reports_panel',
            'generate_financial_report',
            'generate_clients_report', 
            'generate_inventory_report',
            'generate_profit_loss_report',
            'export_current_report'
        ]
        
        missing_functions = []
        for func_name in required_functions:
            if hasattr(reports_widget, func_name):
                print(f"   ✅ {func_name}: موجود")
            else:
                print(f"   ❌ {func_name}: مفقود")
                missing_functions.append(func_name)
        
        if not missing_functions:
            print("✅ جميع دوال التقارير متاحة")
            return True
        else:
            print(f"⚠️ {len(missing_functions)} دالة مفقودة في التقارير")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التقارير: {e}")
        return False

def test_cnc_functions():
    """اختبار دوال CNC"""
    print("\n2️⃣ اختبار دوال نظام CNC...")
    try:
        from cnc.advanced_cnc_system import AdvancedCNCManagerWidget
        
        # إنشاء كائن للاختبار
        cnc_widget = AdvancedCNCManagerWidget()
        
        # اختبار وجود الدوال المطلوبة
        required_functions = [
            'add_new_machine',
            'load_gcode_file',
            'preview_gcode',
            'start_cnc_job',
            'monitor_cnc_status'
        ]
        
        missing_functions = []
        for func_name in required_functions:
            if hasattr(cnc_widget, func_name):
                print(f"   ✅ {func_name}: موجود")
            else:
                print(f"   ❌ {func_name}: مفقود")
                missing_functions.append(func_name)
        
        if not missing_functions:
            print("✅ جميع دوال CNC متاحة")
            return True
        else:
            print(f"⚠️ {len(missing_functions)} دالة مفقودة في CNC")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار CNC: {e}")
        return False

def test_clients_functions():
    """اختبار دوال العملاء"""
    print("\n3️⃣ اختبار دوال نظام العملاء...")
    try:
        from clients.advanced_client_system import AdvancedClientManagerWidget
        
        # إنشاء كائن للاختبار
        clients_widget = AdvancedClientManagerWidget()
        
        # اختبار وجود الدوال المطلوبة
        required_functions = [
            'filter_clients',
            'apply_filters',
            'refresh_clients_list',
            'export_clients_data',
            'import_clients_data',
            'generate_client_report'
        ]
        
        missing_functions = []
        for func_name in required_functions:
            if hasattr(clients_widget, func_name):
                print(f"   ✅ {func_name}: موجود")
            else:
                print(f"   ❌ {func_name}: مفقود")
                missing_functions.append(func_name)
        
        if not missing_functions:
            print("✅ جميع دوال العملاء متاحة")
            return True
        else:
            print(f"⚠️ {len(missing_functions)} دالة مفقودة في العملاء")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار العملاء: {e}")
        return False

def test_inventory_functions():
    """اختبار دوال المخزون"""
    print("\n4️⃣ اختبار دوال نظام المخزون...")
    try:
        from inventory.advanced_inventory_system import AdvancedInventoryManagerWidget
        
        # إنشاء كائن للاختبار
        inventory_widget = AdvancedInventoryManagerWidget()
        
        # اختبار وجود الدوال المطلوبة
        required_functions = [
            'filter_inventory',
            'apply_filters',
            'add_new_item',
            'edit_selected_item',
            'delete_selected_item',
            'add_stock_movement',
            'refresh_data',
            'export_inventory'
        ]
        
        missing_functions = []
        for func_name in required_functions:
            if hasattr(inventory_widget, func_name):
                print(f"   ✅ {func_name}: موجود")
            else:
                print(f"   ❌ {func_name}: مفقود")
                missing_functions.append(func_name)
        
        if not missing_functions:
            print("✅ جميع دوال المخزون متاحة")
            return True
        else:
            print(f"⚠️ {len(missing_functions)} دالة مفقودة في المخزون")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار المخزون: {e}")
        return False

def test_currency_integration():
    """اختبار تكامل العملة الليبية"""
    print("\n5️⃣ اختبار تكامل العملة الليبية...")
    try:
        from ui.modern_styles import CurrencyFormatter
        
        # اختبار التنسيق في سياق الوحدات
        test_amounts = [1234.56, 50000, 1000000]
        
        for amount in test_amounts:
            formatted = CurrencyFormatter.format_currency(amount)
            short_format = CurrencyFormatter.format_currency_short(amount)
            print(f"   💰 {amount} → {formatted} | {short_format}")
        
        print("✅ تكامل العملة الليبية يعمل بشكل صحيح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار العملة: {e}")
        return False

def run_comprehensive_test():
    """تشغيل الاختبار الشامل للدوال المفقودة"""
    print("🧪 اختبار الدوال المفقودة المضافة")
    print("=" * 50)
    
    tests = [
        ("نظام التقارير", test_reports_functions),
        ("نظام CNC", test_cnc_functions),
        ("نظام العملاء", test_clients_functions),
        ("نظام المخزون", test_inventory_functions),
        ("تكامل العملة", test_currency_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            results.append((test_name, False))
    
    # تقييم النتائج
    print("\n" + "=" * 50)
    print("📊 ملخص نتائج اختبار الدوال المفقودة:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    success_rate = (passed / total) * 100
    print(f"\n🎯 معدل النجاح: {passed}/{total} ({success_rate:.1f}%)")
    
    if success_rate >= 80:
        print("🎉 ممتاز! جميع الدوال المطلوبة متاحة")
        print("✅ التطبيق جاهز للتشغيل بدون مشاكل")
    elif success_rate >= 60:
        print("✅ جيد! معظم الدوال متاحة")
        print("⚠️ قد تواجه بعض المشاكل البسيطة")
    else:
        print("⚠️ يحتاج إلى مزيد من الإصلاحات")
        print("🔧 بعض الدوال المهمة لا تزال مفقودة")
    
    return success_rate >= 60

def main():
    """الدالة الرئيسية"""
    try:
        success = run_comprehensive_test()
        
        print(f"\n{'='*50}")
        if success:
            print("🎯 اختبار الدوال المفقودة مكتمل بنجاح!")
            print("✅ يمكنك الآن تشغيل التطبيق بثقة")
            print("\n🚀 لتشغيل التطبيق:")
            print("   python run_advanced_furniture_designer.py")
            print("   أو")
            print("   python simple_main_app.py")
        else:
            print("⚠️ اختبار الدوال المفقودة يحتاج إلى مراجعة")
            print("💡 راجع الأخطاء أعلاه وأعد المحاولة")
        
        return success
        
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع في الاختبار: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    print(f"\n{'='*50}")
    print("🏁 انتهى اختبار الدوال المفقودة")
    
    if not success:
        input("\n⏸️ اضغط Enter للخروج...")
    
    sys.exit(0 if success else 1)
