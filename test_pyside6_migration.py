#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الانتقال إلى PySide6 ودعم العملة الليبية
PySide6 Migration and Libyan Currency Support Test
"""

import sys
import os
import unittest
from pathlib import Path

# إضافة المجلد الحالي لمسار Python
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))


class TestPySide6Migration(unittest.TestCase):
    """اختبار الانتقال إلى PySide6"""
    
    def test_pyside6_imports(self):
        """اختبار استيراد PySide6"""
        try:
            from PySide6.QtWidgets import QApplication, QWidget, QPushButton
            from PySide6.QtCore import Qt, Signal
            from PySide6.QtGui import QIcon, QFont
            print("✅ استيراد PySide6 نجح")
            return True
        except ImportError as e:
            print(f"❌ فشل استيراد PySide6: {e}")
            return False
    
    def test_advanced_3d_viewer_import(self):
        """اختبار استيراد المعاين ثلاثي الأبعاد"""
        try:
            from models.advanced_3d_viewer import Advanced3DViewer, LightingSettings
            print("✅ استيراد المعاين ثلاثي الأبعاد نجح")
            return True
        except ImportError as e:
            print(f"❌ فشل استيراد المعاين ثلاثي الأبعاد: {e}")
            return False
    
    def test_client_system_import(self):
        """اختبار استيراد نظام العملاء"""
        try:
            from clients.advanced_client_system import AdvancedClientManagerWidget, Client
            print("✅ استيراد نظام العملاء نجح")
            return True
        except ImportError as e:
            print(f"❌ فشل استيراد نظام العملاء: {e}")
            return False
    
    def test_inventory_system_import(self):
        """اختبار استيراد نظام المخزون"""
        try:
            from inventory.advanced_inventory_system import InventoryDatabaseManager, InventoryItem
            print("✅ استيراد نظام المخزون نجح")
            return True
        except ImportError as e:
            print(f"❌ فشل استيراد نظام المخزون: {e}")
            return False
    
    def test_cnc_system_import(self):
        """اختبار استيراد نظام CNC"""
        try:
            from cnc.advanced_cnc_system import AdvancedCNCManagerWidget, CNCMachine
            print("✅ استيراد نظام CNC نجح")
            return True
        except ImportError as e:
            print(f"❌ فشل استيراد نظام CNC: {e}")
            return False
    
    def test_reports_system_import(self):
        """اختبار استيراد نظام التقارير"""
        try:
            from reports.advanced_reports_system import AdvancedReportsWidget
            print("✅ استيراد نظام التقارير نجح")
            return True
        except ImportError as e:
            print(f"❌ فشل استيراد نظام التقارير: {e}")
            return False


class TestLibyanCurrencySupport(unittest.TestCase):
    """اختبار دعم العملة الليبية"""
    
    def test_currency_formatter_import(self):
        """اختبار استيراد منسق العملة"""
        try:
            from ui.modern_styles import CurrencyFormatter
            print("✅ استيراد منسق العملة نجح")
            return True
        except ImportError as e:
            print(f"❌ فشل استيراد منسق العملة: {e}")
            return False
    
    def test_currency_formatting(self):
        """اختبار تنسيق العملة الليبية"""
        try:
            from ui.modern_styles import CurrencyFormatter
            
            # اختبار التنسيق الأساسي
            formatted = CurrencyFormatter.format_currency(1234.56)
            expected = "1,234.56 د.ل"
            self.assertEqual(formatted, expected)
            print(f"✅ تنسيق العملة الأساسي: {formatted}")
            
            # اختبار التنسيق بدون رمز
            formatted_no_symbol = CurrencyFormatter.format_currency(1234.56, show_symbol=False)
            expected_no_symbol = "1,234.56"
            self.assertEqual(formatted_no_symbol, expected_no_symbol)
            print(f"✅ تنسيق العملة بدون رمز: {formatted_no_symbol}")
            
            # اختبار التنسيق المختصر
            formatted_short = CurrencyFormatter.format_currency_short(1500000)
            expected_short = "1.5م د.ل"
            self.assertEqual(formatted_short, expected_short)
            print(f"✅ تنسيق العملة المختصر: {formatted_short}")
            
            return True
        except Exception as e:
            print(f"❌ فشل اختبار تنسيق العملة: {e}")
            return False
    
    def test_currency_parsing(self):
        """اختبار تحليل العملة"""
        try:
            from ui.modern_styles import CurrencyFormatter
            
            # اختبار تحليل العملة
            parsed = CurrencyFormatter.parse_currency("1,234.56 د.ل")
            expected = 1234.56
            self.assertEqual(parsed, expected)
            print(f"✅ تحليل العملة: {parsed}")
            
            return True
        except Exception as e:
            print(f"❌ فشل اختبار تحليل العملة: {e}")
            return False
    
    def test_client_currency_fields(self):
        """اختبار حقول العملة في نظام العملاء"""
        try:
            from clients.advanced_client_system import Client
            
            client = Client(
                id="test_001",
                name="عميل تجريبي",
                company="شركة تجريبية",
                phone="0912345678",
                email="<EMAIL>",
                address="طرابلس، ليبيا",
                city="طرابلس",
                country="ليبيا",
                postal_code="12345",
                tax_number="*********",
                payment_terms=30,
                discount_rate=0.05,
                credit_limit=50000.0,  # د.ل
                notes="عميل تجريبي",
                total_revenue=25000.0  # د.ل
            )
            
            # التحقق من أن الحقول تحتوي على القيم الصحيحة
            self.assertEqual(client.credit_limit, 50000.0)
            self.assertEqual(client.total_revenue, 25000.0)
            print("✅ حقول العملة في نظام العملاء تعمل بشكل صحيح")
            
            return True
        except Exception as e:
            print(f"❌ فشل اختبار حقول العملة في نظام العملاء: {e}")
            return False
    
    def test_inventory_currency_fields(self):
        """اختبار حقول العملة في نظام المخزون"""
        try:
            from inventory.advanced_inventory_system import InventoryItem
            
            item = InventoryItem(
                id="item_001",
                name="خشب البلوط",
                category="أخشاب",
                subcategory="أخشاب صلبة",
                description="خشب بلوط عالي الجودة",
                unit="متر مربع",
                current_stock=100.0,
                minimum_stock=20.0,
                maximum_stock=500.0,
                unit_cost=150.0,  # د.ل
                selling_price=200.0,  # د.ل
                supplier="مورد الأخشاب",
                location="مستودع A",
                barcode="*********0",
                notes="خشب ممتاز"
            )
            
            # التحقق من أن الحقول تحتوي على القيم الصحيحة
            self.assertEqual(item.unit_cost, 150.0)
            self.assertEqual(item.selling_price, 200.0)
            print("✅ حقول العملة في نظام المخزون تعمل بشكل صحيح")
            
            return True
        except Exception as e:
            print(f"❌ فشل اختبار حقول العملة في نظام المخزون: {e}")
            return False


class TestUIComponents(unittest.TestCase):
    """اختبار مكونات واجهة المستخدم"""
    
    def test_modern_styles_import(self):
        """اختبار استيراد الأنماط الحديثة"""
        try:
            from ui.modern_styles import ModernStyleManager, CurrencyFormatter
            print("✅ استيراد الأنماط الحديثة نجح")
            return True
        except ImportError as e:
            print(f"❌ فشل استيراد الأنماط الحديثة: {e}")
            return False
    
    def test_icons_manager_import(self):
        """اختبار استيراد مدير الأيقونات"""
        try:
            from ui.icons_manager import IconsManager
            print("✅ استيراد مدير الأيقونات نجح")
            return True
        except ImportError as e:
            print(f"❌ فشل استيراد مدير الأيقونات: {e}")
            return False
    
    def test_currency_icons(self):
        """اختبار أيقونات العملة"""
        try:
            from ui.icons_manager import IconsManager
            
            # اختبار الحصول على أيقونة العملة
            currency_icon = IconsManager.get_standard_icon('currency')
            dinar_icon = IconsManager.get_standard_icon('dinar')
            price_icon = IconsManager.get_standard_icon('price')
            
            self.assertIsNotNone(currency_icon)
            self.assertIsNotNone(dinar_icon)
            self.assertIsNotNone(price_icon)
            print("✅ أيقونات العملة متاحة")
            
            return True
        except Exception as e:
            print(f"❌ فشل اختبار أيقونات العملة: {e}")
            return False


def run_migration_tests():
    """تشغيل اختبارات الانتقال"""
    print("🧪 بدء اختبار الانتقال إلى PySide6 ودعم العملة الليبية")
    print("=" * 70)
    
    # إنشاء مجموعة الاختبارات
    test_suite = unittest.TestSuite()
    
    # إضافة اختبارات PySide6
    test_suite.addTest(unittest.makeSuite(TestPySide6Migration))
    
    # إضافة اختبارات العملة الليبية
    test_suite.addTest(unittest.makeSuite(TestLibyanCurrencySupport))
    
    # إضافة اختبارات واجهة المستخدم
    test_suite.addTest(unittest.makeSuite(TestUIComponents))
    
    # تشغيل الاختبارات
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    print("\n" + "=" * 70)
    if result.wasSuccessful():
        print("🎉 جميع اختبارات الانتقال نجحت!")
        print("✅ التطبيق جاهز للاستخدام مع PySide6 والعملة الليبية")
    else:
        print(f"⚠️ فشل {len(result.failures)} اختبار و {len(result.errors)} خطأ.")
        print("يرجى مراجعة الأخطاء أعلاه وإصلاحها.")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_migration_tests()
    sys.exit(0 if success else 1)
