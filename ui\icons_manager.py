#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير الأيقونات عالية الجودة لتطبيق تصميم الأثاث الاحترافي
High-Quality Icons Manager for Professional Furniture Design Application
"""

from PySide6.QtGui import QIcon, QPixmap, QPainter, QColor, QPen, QBrush
from PySide6.QtCore import Qt, QSize
from PySide6.QtSvg import QSvgRenderer
from typing import Dict, Optional
import os


class IconsManager:
    """مدير الأيقونات المتقدم"""

    # مسارات الأيقونات
    ICONS_DIR = "assets/icons"

    # ذاكرة التخزين المؤقت للأيقونات
    _icon_cache: Dict[str, QIcon] = {}

    # ألوان الأيقونات
    ICON_COLORS = {
        'primary': '#2C3E50',
        'secondary': '#3498DB',
        'success': '#27AE60',
        'warning': '#F39C12',
        'danger': '#E74C3C',
        'light': '#FFFFFF',
        'dark': '#2C3E50',
        'muted': '#95A5A6'
    }

    @classmethod
    def create_icon_from_text(cls, text: str, size: int = 24, color: str = 'primary') -> QIcon:
        """إنشاء أيقونة من نص"""
        pixmap = QPixmap(size, size)
        pixmap.fill(Qt.GlobalColor.transparent)

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # رسم الخلفية الدائرية
        painter.setBrush(QBrush(QColor(cls.ICON_COLORS[color])))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawEllipse(0, 0, size, size)

        # رسم النص
        painter.setPen(QPen(QColor(cls.ICON_COLORS['light'])))
        painter.setFont(painter.font())
        painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, text)

        painter.end()
        return QIcon(pixmap)

    @classmethod
    def create_furniture_icon(cls, furniture_type: str, size: int = 24) -> QIcon:
        """إنشاء أيقونة أثاث مخصصة"""
        pixmap = QPixmap(size, size)
        pixmap.fill(Qt.GlobalColor.transparent)

        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        painter.setPen(QPen(QColor(cls.ICON_COLORS['primary']), 2))

        # رسم أشكال مختلفة حسب نوع الأثاث
        if furniture_type == "table":
            # رسم طاولة
            painter.drawRect(2, size//2-2, size-4, 4)  # سطح الطاولة
            painter.drawLine(4, size//2+2, 4, size-2)  # رجل 1
            painter.drawLine(size-4, size//2+2, size-4, size-2)  # رجل 2

        elif furniture_type == "chair":
            # رسم كرسي
            painter.drawRect(4, size//2, size//2-2, size//2-2)  # المقعد
            painter.drawLine(4, size//2, 4, 4)  # الظهر
            painter.drawLine(size//2+2, size//2, size//2+2, size-2)  # الرجل

        elif furniture_type == "cabinet":
            # رسم خزانة
            painter.drawRect(2, 2, size-4, size-4)  # الإطار
            painter.drawLine(size//2, 2, size//2, size-2)  # الخط الوسطي
            painter.drawEllipse(size//2-2, size//2-1, 2, 2)  # المقبض

        elif furniture_type == "bed":
            # رسم سرير
            painter.drawRect(2, size//2, size-4, size//2-2)  # المرتبة
            painter.drawLine(2, size//2, 2, 4)  # رأس السرير

        else:
            # أيقونة افتراضية
            painter.drawRect(2, 2, size-4, size-4)

        painter.end()
        return QIcon(pixmap)

    @classmethod
    def get_standard_icon(cls, icon_name: str, color: str = 'primary') -> QIcon:
        """الحصول على أيقونة قياسية"""
        cache_key = f"{icon_name}_{color}"

        if cache_key in cls._icon_cache:
            return cls._icon_cache[cache_key]

        # قاموس الأيقونات القياسية
        standard_icons = {
            # أيقونات الملفات
            'new': '📄',
            'open': '📂',
            'save': '💾',
            'save_as': '📋',
            'export': '📤',
            'import': '📥',
            'print': '🖨️',

            # أيقونات التحرير
            'cut': '✂️',
            'copy': '📋',
            'paste': '📌',
            'undo': '↶',
            'redo': '↷',
            'delete': '🗑️',
            'edit': '✏️',

            # أيقونات التنقل
            'home': '🏠',
            'back': '←',
            'forward': '→',
            'up': '↑',
            'down': '↓',
            'refresh': '🔄',
            'search': '🔍',

            # أيقونات الأدوات
            'settings': '⚙️',
            'tools': '🔧',
            'calculator': '🧮',
            'ruler': '📏',
            'measure': '📐',

            # أيقونات البيانات
            'database': '🗄️',
            'table': '📊',
            'chart': '📈',
            'report': '📋',
            'statistics': '📊',

            # أيقونات الأثاث
            'furniture': '🪑',
            'chair': '🪑',
            'table': '🪑',
            'cabinet': '🗄️',
            'bed': '🛏️',
            'sofa': '🛋️',

            # أيقونات العمليات
            'add': '+',
            'remove': '-',
            'plus': '+',
            'minus': '-',
            'check': '✓',
            'close': '✕',
            'warning': '⚠️',
            'error': '❌',
            'info': 'ℹ️',
            'success': '✅',

            # أيقونات المستخدمين
            'user': '👤',
            'users': '👥',
            'client': '👤',
            'supplier': '🏢',

            # أيقونات المال
            'money': '💰',
            'price': 'د.ل',
            'currency': 'د.ل',
            'dinar': 'د.ل',
            'invoice': '🧾',
            'payment': '💳',

            # أيقونات الوقت
            'calendar': '📅',
            'clock': '🕐',
            'date': '📅',
            'time': '⏰',

            # أيقونات الاتصال
            'phone': '📞',
            'email': '📧',
            'address': '📍',
            'contact': '📞',
        }

        if icon_name in standard_icons:
            icon = cls.create_icon_from_text(standard_icons[icon_name], 24, color)
        else:
            # إنشاء أيقونة افتراضية
            icon = cls.create_icon_from_text('?', 24, 'muted')

        cls._icon_cache[cache_key] = icon
        return icon

    @classmethod
    def get_action_icon(cls, action: str) -> QIcon:
        """الحصول على أيقونة إجراء محدد"""
        action_icons = {
            # إجراءات الملفات
            'new_project': cls.get_standard_icon('new', 'success'),
            'open_project': cls.get_standard_icon('open', 'primary'),
            'save_project': cls.get_standard_icon('save', 'primary'),
            'export_excel': cls.get_standard_icon('export', 'success'),
            'export_pdf': cls.get_standard_icon('export', 'danger'),

            # إجراءات العملاء
            'add_client': cls.get_standard_icon('add', 'success'),
            'edit_client': cls.get_standard_icon('edit', 'primary'),
            'delete_client': cls.get_standard_icon('delete', 'danger'),
            'view_client': cls.get_standard_icon('user', 'primary'),

            # إجراءات المخزون
            'add_item': cls.get_standard_icon('add', 'success'),
            'edit_item': cls.get_standard_icon('edit', 'primary'),
            'stock_movement': cls.get_standard_icon('refresh', 'warning'),
            'low_stock': cls.get_standard_icon('warning', 'warning'),

            # إجراءات التصميم
            'add_component': cls.get_standard_icon('add', 'success'),
            'edit_component': cls.get_standard_icon('edit', 'primary'),
            'delete_component': cls.get_standard_icon('delete', 'danger'),
            'optimize_cutting': cls.get_standard_icon('tools', 'primary'),

            # إجراءات التقارير
            'generate_report': cls.get_standard_icon('report', 'primary'),
            'view_statistics': cls.get_standard_icon('statistics', 'primary'),
            'financial_report': cls.get_standard_icon('money', 'success'),

            # إجراءات النظام
            'settings': cls.get_standard_icon('settings', 'primary'),
            'backup': cls.get_standard_icon('database', 'warning'),
            'help': cls.get_standard_icon('info', 'primary'),
            'about': cls.get_standard_icon('info', 'primary'),
        }

        return action_icons.get(action, cls.get_standard_icon('info', 'muted'))

    @classmethod
    def get_status_icon(cls, status: str) -> QIcon:
        """الحصول على أيقونة حالة"""
        status_icons = {
            'active': cls.get_standard_icon('success', 'success'),
            'inactive': cls.get_standard_icon('close', 'muted'),
            'pending': cls.get_standard_icon('clock', 'warning'),
            'completed': cls.get_standard_icon('check', 'success'),
            'cancelled': cls.get_standard_icon('close', 'danger'),
            'in_progress': cls.get_standard_icon('refresh', 'primary'),
            'low_stock': cls.get_standard_icon('warning', 'warning'),
            'out_of_stock': cls.get_standard_icon('error', 'danger'),
            'normal_stock': cls.get_standard_icon('check', 'success'),
        }

        return status_icons.get(status, cls.get_standard_icon('info', 'muted'))

    @classmethod
    def create_toolbar_icon(cls, icon_name: str, size: int = 32) -> QIcon:
        """إنشاء أيقونة شريط أدوات"""
        return cls.get_standard_icon(icon_name, 'primary')

    @classmethod
    def ensure_icons_directory(cls):
        """التأكد من وجود مجلد الأيقونات"""
        if not os.path.exists(cls.ICONS_DIR):
            os.makedirs(cls.ICONS_DIR, exist_ok=True)

    @classmethod
    def clear_cache(cls):
        """مسح ذاكرة التخزين المؤقت للأيقونات"""
        cls._icon_cache.clear()
