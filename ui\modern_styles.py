#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الأنماط الحديثة لتطبيق تصميم الأثاث الاحترافي
Modern Styles System for Professional Furniture Design Application
"""

from PySide6.QtCore import Qt
from PySide6.QtGui import QFont, QPalette, QColor
from typing import Dict, Any


class CurrencyFormatter:
    """مُنسق العملة الليبية"""

    @staticmethod
    def format_currency(amount: float, show_symbol: bool = True) -> str:
        """تنسيق المبلغ بالدينار الليبي"""
        if show_symbol:
            return f"{amount:,.2f} د.ل"
        else:
            return f"{amount:,.2f}"

    @staticmethod
    def format_currency_short(amount: float) -> str:
        """تنسيق مختصر للمبلغ"""
        if amount >= 1000000:
            return f"{amount/1000000:.1f}م د.ل"  # مليون
        elif amount >= 1000:
            return f"{amount/1000:.1f}ألف د.ل"  # ألف
        else:
            return f"{amount:.0f} د.ل"

    @staticmethod
    def parse_currency(currency_str: str) -> float:
        """تحويل نص العملة إلى رقم"""
        try:
            # إزالة رمز العملة والفواصل
            clean_str = currency_str.replace("د.ل", "").replace(",", "").strip()
            return float(clean_str)
        except (ValueError, AttributeError):
            return 0.0


class ModernStyleManager:
    """مدير الأنماط الحديثة"""

    # ألوان النظام الاحترافي
    COLORS = {
        # الألوان الأساسية
        'primary': '#2C3E50',           # أزرق داكن أنيق
        'primary_light': '#34495E',     # أزرق داكن فاتح
        'secondary': '#3498DB',         # أزرق فاتح
        'accent': '#E74C3C',           # أحمر للتنبيهات
        'success': '#27AE60',          # أخضر للنجاح
        'warning': '#F39C12',          # برتقالي للتحذير
        'info': '#3498DB',             # أزرق للمعلومات

        # ألوان الخلفية
        'background': '#ECF0F1',       # رمادي فاتح جداً
        'surface': '#FFFFFF',          # أبيض
        'card': '#FAFAFA',            # رمادي فاتح للبطاقات
        'sidebar': '#2C3E50',         # أزرق داكن للشريط الجانبي

        # ألوان النص
        'text_primary': '#2C3E50',     # نص أساسي داكن
        'text_secondary': '#7F8C8D',   # نص ثانوي رمادي
        'text_light': '#FFFFFF',       # نص فاتح
        'text_muted': '#95A5A6',       # نص خافت

        # ألوان الحدود
        'border': '#BDC3C7',          # حدود رمادية
        'border_light': '#ECF0F1',    # حدود فاتحة
        'border_dark': '#95A5A6',     # حدود داكنة

        # ألوان التفاعل
        'hover': '#3498DB',           # لون التمرير
        'pressed': '#2980B9',         # لون الضغط
        'selected': '#3498DB',        # لون التحديد
        'disabled': '#BDC3C7',       # لون التعطيل
    }

    # أحجام الخطوط
    FONT_SIZES = {
        'title': 24,
        'heading': 18,
        'subheading': 16,
        'body': 14,
        'caption': 12,
        'small': 10
    }

    # المسافات والأبعاد المحسنة
    SPACING = {
        'xs': 4,
        'sm': 8,
        'md': 12,
        'lg': 16,
        'xl': 24,
        'xxl': 32
    }

    # أبعاد الأزرار الموحدة
    BUTTON_SIZES = {
        'small': {'height': 28, 'padding': '6px 12px', 'font_size': 12},
        'medium': {'height': 36, 'padding': '8px 16px', 'font_size': 14},
        'large': {'height': 44, 'padding': '12px 24px', 'font_size': 16},
        'icon': {'width': 36, 'height': 36, 'padding': '8px'}
    }

    # أبعاد العناصر الموحدة
    COMPONENT_SIZES = {
        'input_height': 36,
        'toolbar_height': 48,
        'sidebar_width': 280,
        'panel_width': 320,
        'border_radius': 8,
        'card_radius': 12
    }

    @classmethod
    def get_main_window_style(cls) -> str:
        """الحصول على نمط النافذة الرئيسية"""
        return f"""
        QMainWindow {{
            background-color: {cls.COLORS['background']};
            color: {cls.COLORS['text_primary']};
            font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            font-size: {cls.FONT_SIZES['body']}px;
        }}

        /* شريط القوائم */
        QMenuBar {{
            background-color: {cls.COLORS['primary']};
            color: {cls.COLORS['text_light']};
            border: none;
            padding: 4px;
        }}

        QMenuBar::item {{
            background-color: transparent;
            padding: 8px 16px;
            border-radius: 4px;
            margin: 2px;
        }}

        QMenuBar::item:selected {{
            background-color: {cls.COLORS['hover']};
        }}

        QMenuBar::item:pressed {{
            background-color: {cls.COLORS['pressed']};
        }}

        /* القوائم المنسدلة */
        QMenu {{
            background-color: {cls.COLORS['surface']};
            border: 1px solid {cls.COLORS['border']};
            border-radius: 8px;
            padding: 8px;
            color: {cls.COLORS['text_primary']};
        }}

        QMenu::item {{
            padding: 8px 24px;
            border-radius: 4px;
            margin: 2px;
        }}

        QMenu::item:selected {{
            background-color: {cls.COLORS['hover']};
            color: {cls.COLORS['text_light']};
        }}

        QMenu::separator {{
            height: 1px;
            background-color: {cls.COLORS['border_light']};
            margin: 4px 8px;
        }}

        /* شريط الأدوات */
        QToolBar {{
            background-color: {cls.COLORS['surface']};
            border: 1px solid {cls.COLORS['border_light']};
            border-radius: 8px;
            padding: 8px;
            spacing: 4px;
        }}

        QToolButton {{
            background-color: transparent;
            border: none;
            border-radius: 6px;
            padding: 8px;
            margin: 2px;
            min-width: 32px;
            min-height: 32px;
        }}

        QToolButton:hover {{
            background-color: {cls.COLORS['hover']};
            color: {cls.COLORS['text_light']};
        }}

        QToolButton:pressed {{
            background-color: {cls.COLORS['pressed']};
        }}

        QToolButton:checked {{
            background-color: {cls.COLORS['selected']};
            color: {cls.COLORS['text_light']};
        }}

        /* شريط الحالة */
        QStatusBar {{
            background-color: {cls.COLORS['surface']};
            border-top: 1px solid {cls.COLORS['border_light']};
            color: {cls.COLORS['text_secondary']};
            padding: 4px 8px;
        }}
        """

    @classmethod
    def get_button_style(cls) -> str:
        """الحصول على نمط الأزرار المحسن"""
        return f"""
        /* الأزرار الأساسية - حجم متوسط */
        QPushButton {{
            background-color: {cls.COLORS['primary']};
            color: {cls.COLORS['text_light']};
            border: 2px solid {cls.COLORS['primary']};
            border-radius: {cls.COMPONENT_SIZES['border_radius']}px;
            padding: {cls.BUTTON_SIZES['medium']['padding']};
            font-weight: 600;
            font-size: {cls.BUTTON_SIZES['medium']['font_size']}px;
            min-height: {cls.BUTTON_SIZES['medium']['height']}px;
            font-family: 'Segoe UI', 'Tahoma', sans-serif;
        }}

        QPushButton:hover {{
            background-color: {cls.COLORS['hover']};
            border-color: {cls.COLORS['secondary']};
            color: {cls.COLORS['text_light']};
        }}

        QPushButton:pressed {{
            background-color: {cls.COLORS['pressed']};
            border-color: {cls.COLORS['primary_light']};
        }}

        QPushButton:disabled {{
            background-color: {cls.COLORS['disabled']};
            color: {cls.COLORS['text_muted']};
            border-color: {cls.COLORS['disabled']};
        }}

        /* أزرار صغيرة */
        QPushButton[buttonSize="small"] {{
            padding: {cls.BUTTON_SIZES['small']['padding']};
            font-size: {cls.BUTTON_SIZES['small']['font_size']}px;
            min-height: {cls.BUTTON_SIZES['small']['height']}px;
        }}

        /* أزرار كبيرة */
        QPushButton[buttonSize="large"] {{
            padding: {cls.BUTTON_SIZES['large']['padding']};
            font-size: {cls.BUTTON_SIZES['large']['font_size']}px;
            min-height: {cls.BUTTON_SIZES['large']['height']}px;
        }}

        /* أزرار الأيقونات */
        QPushButton[buttonSize="icon"] {{
            padding: {cls.BUTTON_SIZES['icon']['padding']};
            min-width: {cls.BUTTON_SIZES['icon']['width']}px;
            min-height: {cls.BUTTON_SIZES['icon']['height']}px;
            max-width: {cls.BUTTON_SIZES['icon']['width']}px;
            max-height: {cls.BUTTON_SIZES['icon']['height']}px;
        }}

        /* أزرار ثانوية */
        QPushButton[class="secondary"] {{
            background-color: {cls.COLORS['surface']};
            color: {cls.COLORS['primary']};
            border: 2px solid {cls.COLORS['primary']};
        }}

        QPushButton[class="secondary"]:hover {{
            background-color: {cls.COLORS['primary']};
            color: {cls.COLORS['text_light']};
        }}

        /* أزرار النجاح */
        QPushButton[class="success"] {{
            background-color: {cls.COLORS['success']};
        }}

        QPushButton[class="success"]:hover {{
            background-color: #229954;
        }}

        /* أزرار التحذير */
        QPushButton[class="warning"] {{
            background-color: {cls.COLORS['warning']};
        }}

        QPushButton[class="warning"]:hover {{
            background-color: #E67E22;
        }}

        /* أزرار الخطر */
        QPushButton[class="danger"] {{
            background-color: {cls.COLORS['accent']};
        }}

        QPushButton[class="danger"]:hover {{
            background-color: #C0392B;
        }}
        """

    @classmethod
    def get_input_style(cls) -> str:
        """الحصول على نمط حقول الإدخال المحسن"""
        return f"""
        /* حقول النص */
        QLineEdit {{
            background-color: {cls.COLORS['surface']};
            border: 2px solid {cls.COLORS['border']};
            border-radius: {cls.COMPONENT_SIZES['border_radius']}px;
            padding: 0 {cls.SPACING['md']}px;
            font-size: {cls.FONT_SIZES['body']}px;
            color: {cls.COLORS['text_primary']};
            min-height: {cls.COMPONENT_SIZES['input_height']}px;
            font-family: 'Segoe UI', 'Tahoma', sans-serif;
        }}

        QLineEdit:focus {{
            border-color: {cls.COLORS['secondary']};
            background-color: {cls.COLORS['surface']};
            outline: none;
        }}

        QLineEdit:hover {{
            border-color: {cls.COLORS['border_dark']};
        }}

        QLineEdit:disabled {{
            background-color: {cls.COLORS['card']};
            color: {cls.COLORS['text_muted']};
            border-color: {cls.COLORS['disabled']};
        }}

        QLineEdit[inputSize="small"] {{
            min-height: {cls.BUTTON_SIZES['small']['height']}px;
            font-size: {cls.BUTTON_SIZES['small']['font_size']}px;
            padding: 0 {cls.SPACING['sm']}px;
        }}

        QLineEdit[inputSize="large"] {{
            min-height: {cls.BUTTON_SIZES['large']['height']}px;
            font-size: {cls.BUTTON_SIZES['large']['font_size']}px;
            padding: 0 {cls.SPACING['lg']}px;
        }}

        /* مناطق النص */
        QTextEdit {{
            background-color: {cls.COLORS['surface']};
            border: 2px solid {cls.COLORS['border']};
            border-radius: 8px;
            padding: 12px;
            font-size: {cls.FONT_SIZES['body']}px;
            color: {cls.COLORS['text_primary']};
        }}

        QTextEdit:focus {{
            border-color: {cls.COLORS['secondary']};
        }}

        /* القوائم المنسدلة */
        QComboBox {{
            background-color: {cls.COLORS['surface']};
            border: 2px solid {cls.COLORS['border']};
            border-radius: 8px;
            padding: 12px 16px;
            font-size: {cls.FONT_SIZES['body']}px;
            color: {cls.COLORS['text_primary']};
            min-width: 120px;
        }}

        QComboBox:focus {{
            border-color: {cls.COLORS['secondary']};
        }}

        QComboBox::drop-down {{
            border: none;
            width: 30px;
        }}

        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {cls.COLORS['text_secondary']};
            margin-right: 10px;
        }}

        QComboBox QAbstractItemView {{
            background-color: {cls.COLORS['surface']};
            border: 1px solid {cls.COLORS['border']};
            border-radius: 8px;
            selection-background-color: {cls.COLORS['hover']};
            selection-color: {cls.COLORS['text_light']};
            padding: 4px;
        }}
        """

    @classmethod
    def get_table_style(cls) -> str:
        """الحصول على نمط الجداول"""
        return f"""
        /* الجداول */
        QTableWidget {{
            background-color: {cls.COLORS['surface']};
            border: 1px solid {cls.COLORS['border_light']};
            border-radius: 8px;
            gridline-color: {cls.COLORS['border_light']};
            selection-background-color: {cls.COLORS['hover']};
            selection-color: {cls.COLORS['text_light']};
            font-size: {cls.FONT_SIZES['body']}px;
        }}

        QTableWidget::item {{
            padding: 12px 8px;
            border-bottom: 1px solid {cls.COLORS['border_light']};
        }}

        QTableWidget::item:selected {{
            background-color: {cls.COLORS['selected']};
            color: {cls.COLORS['text_light']};
        }}

        QHeaderView::section {{
            background-color: {cls.COLORS['primary']};
            color: {cls.COLORS['text_light']};
            padding: 12px 8px;
            border: none;
            font-weight: 600;
        }}

        QHeaderView::section:horizontal {{
            border-right: 1px solid {cls.COLORS['primary_light']};
        }}

        QHeaderView::section:vertical {{
            border-bottom: 1px solid {cls.COLORS['primary_light']};
        }}
        """

    @classmethod
    def get_tab_style(cls) -> str:
        """الحصول على نمط التبويبات"""
        return f"""
        /* التبويبات */
        QTabWidget::pane {{
            background-color: {cls.COLORS['surface']};
            border: 1px solid {cls.COLORS['border_light']};
            border-radius: 8px;
            margin-top: -1px;
        }}

        QTabBar::tab {{
            background-color: {cls.COLORS['card']};
            color: {cls.COLORS['text_secondary']};
            padding: 12px 24px;
            margin-right: 2px;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            font-weight: 500;
        }}

        QTabBar::tab:selected {{
            background-color: {cls.COLORS['primary']};
            color: {cls.COLORS['text_light']};
        }}

        QTabBar::tab:hover:!selected {{
            background-color: {cls.COLORS['hover']};
            color: {cls.COLORS['text_light']};
        }}
        """

    @classmethod
    def get_complete_style(cls) -> str:
        """الحصول على النمط الكامل للتطبيق"""
        return (
            cls.get_main_window_style() +
            cls.get_button_style() +
            cls.get_input_style() +
            cls.get_table_style() +
            cls.get_tab_style()
        )

    @classmethod
    def setup_application_font(cls, app) -> None:
        """إعداد خط التطبيق الافتراضي"""
        font = QFont("Segoe UI", cls.FONT_SIZES['body'])
        font.setStyleHint(QFont.StyleHint.SansSerif)
        app.setFont(font)

    @classmethod
    def create_color_palette(cls) -> QPalette:
        """إنشاء لوحة ألوان مخصصة"""
        palette = QPalette()

        # ألوان النوافذ
        palette.setColor(QPalette.ColorRole.Window, QColor(cls.COLORS['background']))
        palette.setColor(QPalette.ColorRole.WindowText, QColor(cls.COLORS['text_primary']))

        # ألوان القواعد
        palette.setColor(QPalette.ColorRole.Base, QColor(cls.COLORS['surface']))
        palette.setColor(QPalette.ColorRole.AlternateBase, QColor(cls.COLORS['card']))

        # ألوان النص
        palette.setColor(QPalette.ColorRole.Text, QColor(cls.COLORS['text_primary']))
        palette.setColor(QPalette.ColorRole.BrightText, QColor(cls.COLORS['text_light']))

        # ألوان الأزرار
        palette.setColor(QPalette.ColorRole.Button, QColor(cls.COLORS['primary']))
        palette.setColor(QPalette.ColorRole.ButtonText, QColor(cls.COLORS['text_light']))

        # ألوان التحديد
        palette.setColor(QPalette.ColorRole.Highlight, QColor(cls.COLORS['selected']))
        palette.setColor(QPalette.ColorRole.HighlightedText, QColor(cls.COLORS['text_light']))

        return palette